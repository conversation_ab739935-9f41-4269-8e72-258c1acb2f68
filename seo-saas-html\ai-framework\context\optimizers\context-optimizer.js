/**
 * Context Optimizer - Intelligent Context Enhancement System
 * Automatically optimizes context for better AI performance and results
 */

class ContextOptimizer {
    constructor(options = {}) {
        this.optimizationStrategies = new Map();
        this.performanceHistory = new Map();
        this.optimizationMetrics = new Map();
        
        // Configuration
        this.config = {
            enableAutoOptimization: options.enableAutoOptimization !== false,
            optimizationThreshold: options.optimizationThreshold || 0.7,
            maxOptimizationAttempts: options.maxOptimizationAttempts || 3,
            learningRate: options.learningRate || 0.1,
            ...options
        };
        
        this.initialize();
    }
    
    /**
     * Initialize the optimizer
     */
    initialize() {
        try {
            // Initialize optimization strategies
            this.initializeOptimizationStrategies();
            
            // Initialize performance tracking
            this.initializePerformanceTracking();
            
            console.log('ContextOptimizer initialized successfully');
        } catch (error) {
            console.error('Failed to initialize ContextOptimizer:', error);
            throw error;
        }
    }
    
    /**
     * Initialize optimization strategies
     */
    initializeOptimizationStrategies() {
        // Content optimization strategies
        this.optimizationStrategies.set('keyword_density_optimization', {
            name: 'Keyword Density Optimization',
            category: 'content',
            priority: 1,
            condition: (context, performance) => {
                const density = context.seo?.optimization?.keywordDensity?.current;
                return density && (density < 1.0 || density > 3.0);
            },
            optimize: (context) => {
                const currentDensity = context.seo?.optimization?.keywordDensity?.current || 2.0;
                let targetDensity = 2.0;
                
                if (currentDensity < 1.0) {
                    targetDensity = Math.min(currentDensity + 0.5, 2.0);
                } else if (currentDensity > 3.0) {
                    targetDensity = Math.max(currentDensity - 0.5, 2.0);
                }
                
                if (!context.seo.optimization) context.seo.optimization = {};
                if (!context.seo.optimization.keywordDensity) context.seo.optimization.keywordDensity = {};
                
                context.seo.optimization.keywordDensity.target = targetDensity;
                
                return {
                    success: true,
                    changes: [`Adjusted keyword density target to ${targetDensity}%`],
                    impact: 'medium'
                };
            }
        });
        
        this.optimizationStrategies.set('word_count_optimization', {
            name: 'Word Count Optimization',
            category: 'content',
            priority: 2,
            condition: (context, performance) => {
                const wordCount = context.content?.wordCount;
                const responseTime = performance?.responseTime;
                return wordCount && responseTime && (
                    (wordCount > 2000 && responseTime > 5000) ||
                    (wordCount < 500 && performance.qualityScore < 0.7)
                );
            },
            optimize: (context) => {
                const currentWordCount = context.content.wordCount;
                const performance = this.performanceHistory.get(context.contextId);
                let optimizedWordCount = currentWordCount;
                
                if (currentWordCount > 2000 && performance?.responseTime > 5000) {
                    // Reduce word count for better performance
                    optimizedWordCount = Math.max(1500, currentWordCount * 0.8);
                } else if (currentWordCount < 500 && performance?.qualityScore < 0.7) {
                    // Increase word count for better quality
                    optimizedWordCount = Math.min(1000, currentWordCount * 1.5);
                }
                
                context.content.wordCount = Math.round(optimizedWordCount);
                
                return {
                    success: true,
                    changes: [`Adjusted word count from ${currentWordCount} to ${context.content.wordCount}`],
                    impact: 'high'
                };
            }
        });
        
        // AI configuration optimization strategies
        this.optimizationStrategies.set('temperature_optimization', {
            name: 'AI Temperature Optimization',
            category: 'ai',
            priority: 1,
            condition: (context, performance) => {
                const temperature = context.ai?.temperature;
                const qualityScore = performance?.qualityScore;
                const consistency = performance?.consistency;
                
                return temperature && qualityScore && (
                    (temperature > 0.8 && consistency < 0.7) ||
                    (temperature < 0.4 && qualityScore < 0.8)
                );
            },
            optimize: (context) => {
                const currentTemp = context.ai.temperature;
                const performance = this.performanceHistory.get(context.contextId);
                let optimizedTemp = currentTemp;
                
                if (currentTemp > 0.8 && performance?.consistency < 0.7) {
                    // Reduce temperature for better consistency
                    optimizedTemp = Math.max(0.5, currentTemp - 0.2);
                } else if (currentTemp < 0.4 && performance?.qualityScore < 0.8) {
                    // Increase temperature for better creativity
                    optimizedTemp = Math.min(0.7, currentTemp + 0.2);
                }
                
                context.ai.temperature = Math.round(optimizedTemp * 10) / 10;
                
                return {
                    success: true,
                    changes: [`Adjusted AI temperature from ${currentTemp} to ${context.ai.temperature}`],
                    impact: 'medium'
                };
            }
        });
        
        this.optimizationStrategies.set('token_limit_optimization', {
            name: 'Token Limit Optimization',
            category: 'ai',
            priority: 2,
            condition: (context, performance) => {
                const maxTokens = context.ai?.maxTokens;
                const responseTime = performance?.responseTime;
                const completeness = performance?.completeness;
                
                return maxTokens && (
                    (maxTokens > 4000 && responseTime > 3000) ||
                    (maxTokens < 2000 && completeness < 0.8)
                );
            },
            optimize: (context) => {
                const currentTokens = context.ai.maxTokens;
                const performance = this.performanceHistory.get(context.contextId);
                let optimizedTokens = currentTokens;
                
                if (currentTokens > 4000 && performance?.responseTime > 3000) {
                    // Reduce tokens for better performance
                    optimizedTokens = Math.max(3000, currentTokens * 0.8);
                } else if (currentTokens < 2000 && performance?.completeness < 0.8) {
                    // Increase tokens for better completeness
                    optimizedTokens = Math.min(4000, currentTokens * 1.3);
                }
                
                context.ai.maxTokens = Math.round(optimizedTokens);
                
                return {
                    success: true,
                    changes: [`Adjusted token limit from ${currentTokens} to ${context.ai.maxTokens}`],
                    impact: 'medium'
                };
            }
        });
        
        // SEO optimization strategies
        this.optimizationStrategies.set('eeat_optimization', {
            name: 'E-E-A-T Optimization',
            category: 'seo',
            priority: 1,
            condition: (context, performance) => {
                const eeat = context.seo?.optimization?.eeat;
                const qualityScore = performance?.qualityScore;
                
                return !eeat && qualityScore < 0.8;
            },
            optimize: (context) => {
                if (!context.seo.optimization) context.seo.optimization = {};
                
                context.seo.optimization.eeat = {
                    experience: true,
                    expertise: true,
                    authoritativeness: true,
                    trustworthiness: true
                };
                
                return {
                    success: true,
                    changes: ['Enabled E-E-A-T optimization'],
                    impact: 'high'
                };
            }
        });
        
        this.optimizationStrategies.set('secondary_keywords_optimization', {
            name: 'Secondary Keywords Optimization',
            category: 'seo',
            priority: 2,
            condition: (context, performance) => {
                const secondaryKeywords = context.content?.secondaryKeywords;
                const primaryKeyword = context.content?.primaryKeyword;
                
                return primaryKeyword && (!secondaryKeywords || secondaryKeywords.length === 0);
            },
            optimize: (context) => {
                const primaryKeyword = context.content.primaryKeyword;
                
                // Generate related keywords based on primary keyword
                const secondaryKeywords = this.generateSecondaryKeywords(primaryKeyword, context.project?.industry);
                
                if (!context.content.secondaryKeywords) {
                    context.content.secondaryKeywords = [];
                }
                context.content.secondaryKeywords.push(...secondaryKeywords);
                
                return {
                    success: true,
                    changes: [`Added ${secondaryKeywords.length} secondary keywords`],
                    impact: 'medium'
                };
            }
        });
        
        // Performance optimization strategies
        this.optimizationStrategies.set('provider_optimization', {
            name: 'AI Provider Optimization',
            category: 'performance',
            priority: 1,
            condition: (context, performance) => {
                const responseTime = performance?.responseTime;
                const errorRate = performance?.errorRate;
                
                return responseTime > 5000 || errorRate > 0.1;
            },
            optimize: (context) => {
                const currentProvider = context.ai.provider;
                let optimizedProvider = currentProvider;
                let optimizedModel = context.ai.model;
                
                // Provider optimization logic based on performance
                if (currentProvider === 'openai' && context.ai.model === 'gpt-4o') {
                    optimizedProvider = 'openai';
                    optimizedModel = 'gpt-4o-mini';
                } else if (currentProvider === 'groq') {
                    optimizedProvider = 'openai';
                    optimizedModel = 'gpt-4o-mini';
                }
                
                context.ai.provider = optimizedProvider;
                context.ai.model = optimizedModel;
                
                return {
                    success: true,
                    changes: [`Switched to ${optimizedProvider}/${optimizedModel} for better performance`],
                    impact: 'high'
                };
            }
        });
    }
    
    /**
     * Initialize performance tracking
     */
    initializePerformanceTracking() {
        this.optimizationMetrics.set('response_time', {
            name: 'Response Time',
            target: 3000, // 3 seconds
            weight: 1.0
        });
        
        this.optimizationMetrics.set('quality_score', {
            name: 'Quality Score',
            target: 0.9,
            weight: 1.0
        });
        
        this.optimizationMetrics.set('consistency', {
            name: 'Consistency',
            target: 0.8,
            weight: 0.8
        });
        
        this.optimizationMetrics.set('completeness', {
            name: 'Completeness',
            target: 0.9,
            weight: 0.9
        });
        
        this.optimizationMetrics.set('cost_efficiency', {
            name: 'Cost Efficiency',
            target: 0.01, // $0.01 per request
            weight: 0.7
        });
    }
    
    /**
     * Optimize context based on performance data
     */
    async optimizeContext(context, performanceData = null) {
        try {
            const optimizationResults = {
                contextId: context.contextId,
                timestamp: new Date().toISOString(),
                appliedOptimizations: [],
                totalChanges: 0,
                impactLevel: 'none',
                success: true
            };
            
            // Update performance history
            if (performanceData) {
                this.performanceHistory.set(context.contextId, performanceData);
            }
            
            const performance = this.performanceHistory.get(context.contextId) || {};
            
            // Apply optimization strategies
            for (const [strategyId, strategy] of this.optimizationStrategies) {
                try {
                    if (strategy.condition(context, performance)) {
                        const result = strategy.optimize(context);
                        
                        if (result.success) {
                            optimizationResults.appliedOptimizations.push({
                                strategy: strategy.name,
                                category: strategy.category,
                                changes: result.changes,
                                impact: result.impact
                            });
                            
                            optimizationResults.totalChanges += result.changes.length;
                            
                            // Update impact level
                            if (result.impact === 'high' && optimizationResults.impactLevel !== 'high') {
                                optimizationResults.impactLevel = 'high';
                            } else if (result.impact === 'medium' && optimizationResults.impactLevel === 'none') {
                                optimizationResults.impactLevel = 'medium';
                            } else if (optimizationResults.impactLevel === 'none') {
                                optimizationResults.impactLevel = 'low';
                            }
                        }
                    }
                } catch (error) {
                    console.error(`Optimization strategy error for ${strategyId}:`, error);
                }
            }
            
            // Update context optimization tracking
            if (!context.performance) context.performance = {};
            if (!context.performance.optimization) context.performance.optimization = {};
            
            context.performance.optimization.lastOptimization = optimizationResults.timestamp;
            context.performance.optimization.appliedOptimizations = [
                ...(context.performance.optimization.appliedOptimizations || []),
                ...optimizationResults.appliedOptimizations.map(opt => opt.strategy)
            ];
            
            return optimizationResults;
        } catch (error) {
            console.error('Context optimization error:', error);
            return {
                contextId: context.contextId,
                timestamp: new Date().toISOString(),
                appliedOptimizations: [],
                totalChanges: 0,
                impactLevel: 'none',
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * Generate secondary keywords based on primary keyword and industry
     */
    generateSecondaryKeywords(primaryKeyword, industry) {
        const keywords = [];
        const keywordParts = primaryKeyword.toLowerCase().split(' ');
        
        // Industry-specific keyword variations
        const industryKeywords = {
            travel: ['guide', 'tips', 'best', 'top', 'destinations', 'planning'],
            technology: ['software', 'tools', 'solutions', 'platform', 'system'],
            healthcare: ['treatment', 'care', 'medical', 'health', 'wellness'],
            finance: ['investment', 'financial', 'money', 'banking', 'planning'],
            ecommerce: ['buy', 'shop', 'store', 'product', 'online', 'purchase']
        };
        
        const industryTerms = industryKeywords[industry] || ['best', 'top', 'guide', 'tips'];
        
        // Generate variations
        for (const term of industryTerms.slice(0, 3)) {
            keywords.push(`${primaryKeyword} ${term}`);
            keywords.push(`${term} ${primaryKeyword}`);
        }
        
        // Add long-tail variations
        keywords.push(`best ${primaryKeyword}`);
        keywords.push(`${primaryKeyword} guide`);
        keywords.push(`how to ${primaryKeyword}`);
        
        return keywords.slice(0, 5); // Return top 5 secondary keywords
    }
    
    /**
     * Calculate optimization score
     */
    calculateOptimizationScore(context, performanceData) {
        let totalScore = 0;
        let totalWeight = 0;
        
        for (const [metricId, metric] of this.optimizationMetrics) {
            const value = performanceData[metricId.replace('_', '')];
            if (value !== undefined) {
                let score = 0;
                
                if (metricId === 'response_time' || metricId === 'cost_efficiency') {
                    // Lower is better
                    score = Math.max(0, 1 - (value / metric.target));
                } else {
                    // Higher is better
                    score = Math.min(1, value / metric.target);
                }
                
                totalScore += score * metric.weight;
                totalWeight += metric.weight;
            }
        }
        
        return totalWeight > 0 ? totalScore / totalWeight : 0;
    }
    
    /**
     * Get optimization statistics
     */
    getOptimizationStats() {
        return {
            totalStrategies: this.optimizationStrategies.size,
            totalMetrics: this.optimizationMetrics.size,
            performanceHistorySize: this.performanceHistory.size,
            optimizationThreshold: this.config.optimizationThreshold
        };
    }
    
    /**
     * Clear performance history for context
     */
    clearPerformanceHistory(contextId) {
        this.performanceHistory.delete(contextId);
    }
}

module.exports = ContextOptimizer;
