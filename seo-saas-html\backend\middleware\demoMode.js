/**
 * Demo Mode Middleware
 * Allows certain endpoints to work without authentication for demo purposes
 */

const logger = require('../services/logger');

const demoMode = (req, res, next) => {
  // Check if demo mode is enabled
  const isDemoMode = process.env.DEMO_MODE === 'true' || req.headers['x-demo-mode'] === 'true';
  
  if (isDemoMode && !req.headers.authorization) {
    // Create a mock user for demo mode
    req.user = {
      id: 'demo-user-001',
      email: '<EMAIL>',
      subscription_tier: 'pro',
      api_usage_count: 0,
      api_usage_limit: 1000,
      isDemo: true
    };
    logger.info('Demo mode activated for request');
  }
  
  next();
};

module.exports = demoMode;