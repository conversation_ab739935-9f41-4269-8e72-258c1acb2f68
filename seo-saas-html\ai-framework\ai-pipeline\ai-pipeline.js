/**
 * AI Pipeline - Intelligent Multi-Provider AI Integration System
 * Manages AI requests across multiple providers with intelligent routing and fallbacks
 */

const ProviderManager = require('./providers/provider-manager');
const ResponseProcessor = require('./processors/response-processor');
const CostOptimizer = require('./optimizers/cost-optimizer');
const CircuitBreaker = require('./utils/circuit-breaker');

class AIPipeline {
    constructor(options = {}) {
        this.providerManager = new ProviderManager(options.providers);
        this.responseProcessor = new ResponseProcessor(options.processing);
        this.costOptimizer = new CostOptimizer(options.cost);
        
        this.circuitBreakers = new Map();
        this.requestQueue = [];
        this.activeRequests = new Map();
        this.performanceMetrics = new Map();
        
        // Configuration
        this.config = {
            maxConcurrentRequests: options.maxConcurrentRequests || 10,
            requestTimeout: options.requestTimeout || 30000,
            retryAttempts: options.retryAttempts || 3,
            retryDelay: options.retryDelay || 1000,
            enableCircuitBreaker: options.enableCircuitBreaker !== false,
            enableCostOptimization: options.enableCostOptimization !== false,
            enableIntelligentRouting: options.enableIntelligentRouting !== false,
            ...options
        };
        
        this.initialize();
    }
    
    /**
     * Initialize the AI pipeline
     */
    async initialize() {
        try {
            // Initialize provider manager
            await this.providerManager.initialize();
            
            // Initialize response processor
            await this.responseProcessor.initialize();
            
            // Initialize cost optimizer
            await this.costOptimizer.initialize();
            
            // Initialize circuit breakers for each provider
            this.initializeCircuitBreakers();
            
            console.log('AIPipeline initialized successfully');
        } catch (error) {
            console.error('Failed to initialize AIPipeline:', error);
            throw error;
        }
    }
    
    /**
     * Initialize circuit breakers
     */
    initializeCircuitBreakers() {
        if (!this.config.enableCircuitBreaker) return;
        
        const providers = this.providerManager.getAvailableProviders();
        
        for (const provider of providers) {
            this.circuitBreakers.set(provider.id, new CircuitBreaker({
                failureThreshold: 5,
                recoveryTimeout: 60000,
                monitoringPeriod: 300000 // 5 minutes
            }));
        }
    }
    
    /**
     * Process AI request through the pipeline
     */
    async processRequest(context, prompt) {
        const requestId = this.generateRequestId();
        const startTime = Date.now();
        
        try {
            // Validate request
            this.validateRequest(context, prompt);
            
            // Check concurrent request limit
            if (this.activeRequests.size >= this.config.maxConcurrentRequests) {
                throw new Error('Maximum concurrent requests exceeded');
            }
            
            // Track active request
            this.activeRequests.set(requestId, {
                context,
                prompt,
                startTime,
                status: 'processing'
            });
            
            // Select optimal provider
            const provider = await this.selectProvider(context, prompt);
            
            // Process request with selected provider
            const result = await this.executeRequest(requestId, provider, context, prompt);
            
            // Process and validate response
            const processedResult = await this.processResponse(result, context);
            
            // Update performance metrics
            this.updatePerformanceMetrics(requestId, provider.id, Date.now() - startTime, true);
            
            // Clean up
            this.activeRequests.delete(requestId);
            
            return {
                success: true,
                requestId,
                provider: provider.id,
                responseTime: Date.now() - startTime,
                result: processedResult
            };
            
        } catch (error) {
            console.error(`AI Pipeline request ${requestId} failed:`, error);
            
            // Update performance metrics
            this.updatePerformanceMetrics(requestId, null, Date.now() - startTime, false);
            
            // Clean up
            this.activeRequests.delete(requestId);
            
            // Try fallback if available
            if (this.config.retryAttempts > 0) {
                return await this.handleFailure(requestId, context, prompt, error);
            }
            
            throw error;
        }
    }
    
    /**
     * Validate request parameters
     */
    validateRequest(context, prompt) {
        if (!context) {
            throw new Error('Context is required');
        }
        
        if (!prompt) {
            throw new Error('Prompt is required');
        }
        
        if (!context.ai?.provider && !this.config.enableIntelligentRouting) {
            throw new Error('AI provider must be specified or intelligent routing must be enabled');
        }
    }
    
    /**
     * Select optimal provider for request
     */
    async selectProvider(context, prompt) {
        try {
            // If specific provider requested and available
            if (context.ai?.provider) {
                const requestedProvider = this.providerManager.getProvider(context.ai.provider);
                if (requestedProvider && this.isProviderAvailable(requestedProvider.id)) {
                    return requestedProvider;
                }
            }
            
            // Use intelligent routing if enabled
            if (this.config.enableIntelligentRouting) {
                return await this.intelligentProviderSelection(context, prompt);
            }
            
            // Fallback to default provider
            const defaultProvider = this.providerManager.getDefaultProvider();
            if (!defaultProvider || !this.isProviderAvailable(defaultProvider.id)) {
                throw new Error('No available providers');
            }
            
            return defaultProvider;
        } catch (error) {
            console.error('Provider selection error:', error);
            throw error;
        }
    }
    
    /**
     * Intelligent provider selection based on context and performance
     */
    async intelligentProviderSelection(context, prompt) {
        const availableProviders = this.providerManager.getAvailableProviders()
            .filter(provider => this.isProviderAvailable(provider.id));
        
        if (availableProviders.length === 0) {
            throw new Error('No available providers');
        }
        
        // Score providers based on multiple factors
        const providerScores = [];
        
        for (const provider of availableProviders) {
            const score = await this.calculateProviderScore(provider, context, prompt);
            providerScores.push({ provider, score });
        }
        
        // Sort by score (highest first)
        providerScores.sort((a, b) => b.score - a.score);
        
        return providerScores[0].provider;
    }
    
    /**
     * Calculate provider score for intelligent routing
     */
    async calculateProviderScore(provider, context, prompt) {
        let score = 0;
        
        // Performance score (40% weight)
        const performanceMetrics = this.performanceMetrics.get(provider.id);
        if (performanceMetrics) {
            const avgResponseTime = performanceMetrics.totalResponseTime / performanceMetrics.requestCount;
            const successRate = performanceMetrics.successCount / performanceMetrics.requestCount;
            
            score += (successRate * 0.3 + (1 - Math.min(avgResponseTime / 5000, 1)) * 0.1) * 40;
        } else {
            score += 30; // Default score for new providers
        }
        
        // Cost score (20% weight)
        if (this.config.enableCostOptimization) {
            const costScore = await this.costOptimizer.calculateProviderCostScore(provider, context);
            score += costScore * 20;
        } else {
            score += 15;
        }
        
        // Capability score (25% weight)
        const capabilityScore = this.calculateCapabilityScore(provider, context);
        score += capabilityScore * 25;
        
        // Load score (15% weight)
        const loadScore = this.calculateLoadScore(provider);
        score += loadScore * 15;
        
        return score;
    }
    
    /**
     * Calculate provider capability score
     */
    calculateCapabilityScore(provider, context) {
        let score = 0.5; // Base score
        
        // Check model compatibility
        const requestedModel = context.ai?.model;
        if (requestedModel && provider.supportedModels?.includes(requestedModel)) {
            score += 0.3;
        }
        
        // Check feature support
        const contentType = context.content?.type;
        if (contentType && provider.supportedContentTypes?.includes(contentType)) {
            score += 0.2;
        }
        
        return Math.min(score, 1.0);
    }
    
    /**
     * Calculate provider load score
     */
    calculateLoadScore(provider) {
        const activeRequestsForProvider = Array.from(this.activeRequests.values())
            .filter(req => req.provider === provider.id).length;
        
        const maxConcurrent = provider.maxConcurrentRequests || 5;
        const loadRatio = activeRequestsForProvider / maxConcurrent;
        
        return Math.max(0, 1 - loadRatio);
    }
    
    /**
     * Check if provider is available (not in circuit breaker open state)
     */
    isProviderAvailable(providerId) {
        if (!this.config.enableCircuitBreaker) return true;
        
        const circuitBreaker = this.circuitBreakers.get(providerId);
        return !circuitBreaker || circuitBreaker.getState() !== 'open';
    }
    
    /**
     * Execute request with selected provider
     */
    async executeRequest(requestId, provider, context, prompt) {
        try {
            // Update request status
            const activeRequest = this.activeRequests.get(requestId);
            if (activeRequest) {
                activeRequest.provider = provider.id;
                activeRequest.status = 'executing';
            }
            
            // Execute with circuit breaker if enabled
            if (this.config.enableCircuitBreaker) {
                const circuitBreaker = this.circuitBreakers.get(provider.id);
                return await circuitBreaker.execute(async () => {
                    return await this.providerManager.executeRequest(provider.id, context, prompt);
                });
            } else {
                return await this.providerManager.executeRequest(provider.id, context, prompt);
            }
        } catch (error) {
            console.error(`Request execution failed for provider ${provider.id}:`, error);
            throw error;
        }
    }
    
    /**
     * Process AI response
     */
    async processResponse(result, context) {
        try {
            return await this.responseProcessor.processResponse(result, context);
        } catch (error) {
            console.error('Response processing error:', error);
            throw error;
        }
    }
    
    /**
     * Handle request failure with fallback logic
     */
    async handleFailure(requestId, context, prompt, originalError) {
        try {
            // Get fallback providers
            const fallbackProviders = this.providerManager.getFallbackProviders(context.ai?.provider);
            
            for (const provider of fallbackProviders) {
                if (this.isProviderAvailable(provider.id)) {
                    try {
                        console.log(`Attempting fallback to provider: ${provider.id}`);
                        
                        // Update context to use fallback provider
                        const fallbackContext = {
                            ...context,
                            ai: {
                                ...context.ai,
                                provider: provider.id
                            }
                        };
                        
                        const result = await this.executeRequest(requestId, provider, fallbackContext, prompt);
                        const processedResult = await this.processResponse(result, fallbackContext);
                        
                        return {
                            success: true,
                            requestId,
                            provider: provider.id,
                            fallback: true,
                            originalError: originalError.message,
                            result: processedResult
                        };
                    } catch (fallbackError) {
                        console.error(`Fallback provider ${provider.id} also failed:`, fallbackError);
                        continue;
                    }
                }
            }
            
            // All fallbacks failed
            throw new Error(`All providers failed. Original error: ${originalError.message}`);
        } catch (error) {
            console.error('Fallback handling failed:', error);
            throw error;
        }
    }
    
    /**
     * Update performance metrics
     */
    updatePerformanceMetrics(requestId, providerId, responseTime, success) {
        if (!providerId) return;
        
        if (!this.performanceMetrics.has(providerId)) {
            this.performanceMetrics.set(providerId, {
                requestCount: 0,
                successCount: 0,
                totalResponseTime: 0,
                lastUpdated: new Date().toISOString()
            });
        }
        
        const metrics = this.performanceMetrics.get(providerId);
        metrics.requestCount++;
        metrics.totalResponseTime += responseTime;
        metrics.lastUpdated = new Date().toISOString();
        
        if (success) {
            metrics.successCount++;
        }
    }
    
    /**
     * Generate unique request ID
     */
    generateRequestId() {
        return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * Get pipeline status
     */
    getStatus() {
        return {
            activeRequests: this.activeRequests.size,
            queuedRequests: this.requestQueue.length,
            availableProviders: this.providerManager.getAvailableProviders().length,
            circuitBreakerStates: this.getCircuitBreakerStates(),
            performanceMetrics: Object.fromEntries(this.performanceMetrics)
        };
    }
    
    /**
     * Get circuit breaker states
     */
    getCircuitBreakerStates() {
        const states = {};
        for (const [providerId, circuitBreaker] of this.circuitBreakers) {
            states[providerId] = circuitBreaker.getState();
        }
        return states;
    }
    
    /**
     * Get performance metrics
     */
    getPerformanceMetrics() {
        const metrics = {};
        
        for (const [providerId, data] of this.performanceMetrics) {
            metrics[providerId] = {
                ...data,
                averageResponseTime: data.requestCount > 0 ? data.totalResponseTime / data.requestCount : 0,
                successRate: data.requestCount > 0 ? data.successCount / data.requestCount : 0
            };
        }
        
        return metrics;
    }
    
    /**
     * Reset performance metrics
     */
    resetPerformanceMetrics(providerId = null) {
        if (providerId) {
            this.performanceMetrics.delete(providerId);
        } else {
            this.performanceMetrics.clear();
        }
    }
    
    /**
     * Shutdown pipeline gracefully
     */
    async shutdown() {
        try {
            // Wait for active requests to complete (with timeout)
            const timeout = 30000; // 30 seconds
            const startTime = Date.now();
            
            while (this.activeRequests.size > 0 && (Date.now() - startTime) < timeout) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            // Force close remaining requests
            this.activeRequests.clear();
            this.requestQueue.length = 0;
            
            console.log('AIPipeline shutdown completed');
        } catch (error) {
            console.error('Pipeline shutdown error:', error);
        }
    }
}

module.exports = AIPipeline;
