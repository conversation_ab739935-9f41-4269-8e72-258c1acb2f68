/**
 * Prompt Analytics - Performance Tracking and A/B Testing System
 * Tracks prompt performance, conducts A/B tests, and provides optimization insights
 */

class PromptAnalytics {
    constructor(options = {}) {
        this.performanceData = new Map();
        this.abTests = new Map();
        this.analyticsMetrics = new Map();
        this.insights = new Map();
        
        // Configuration
        this.config = {
            enableABTesting: options.enableABTesting !== false,
            minSampleSize: options.minSampleSize || 30,
            confidenceLevel: options.confidenceLevel || 0.95,
            trackingWindow: options.trackingWindow || 7 * 24 * 60 * 60 * 1000, // 7 days
            ...options
        };
        
        this.initialize();
    }
    
    /**
     * Initialize the analytics system
     */
    initialize() {
        try {
            // Initialize analytics metrics
            this.initializeAnalyticsMetrics();
            
            // Initialize A/B testing framework
            this.initializeABTesting();
            
            console.log('PromptAnalytics initialized successfully');
        } catch (error) {
            console.error('Failed to initialize PromptAnalytics:', error);
            throw error;
        }
    }
    
    /**
     * Initialize analytics metrics
     */
    initializeAnalyticsMetrics() {
        this.analyticsMetrics.set('response_time', {
            name: 'Response Time',
            unit: 'milliseconds',
            target: 3000,
            aggregation: 'average'
        });
        
        this.analyticsMetrics.set('quality_score', {
            name: 'Quality Score',
            unit: 'score',
            target: 0.9,
            aggregation: 'average'
        });
        
        this.analyticsMetrics.set('user_satisfaction', {
            name: 'User Satisfaction',
            unit: 'rating',
            target: 4.5,
            aggregation: 'average'
        });
        
        this.analyticsMetrics.set('keyword_density_accuracy', {
            name: 'Keyword Density Accuracy',
            unit: 'percentage',
            target: 0.95,
            aggregation: 'average'
        });
        
        this.analyticsMetrics.set('content_completeness', {
            name: 'Content Completeness',
            unit: 'percentage',
            target: 0.95,
            aggregation: 'average'
        });
        
        this.analyticsMetrics.set('seo_compliance', {
            name: 'SEO Compliance',
            unit: 'score',
            target: 0.9,
            aggregation: 'average'
        });
        
        this.analyticsMetrics.set('error_rate', {
            name: 'Error Rate',
            unit: 'percentage',
            target: 0.05,
            aggregation: 'average'
        });
        
        this.analyticsMetrics.set('cost_per_request', {
            name: 'Cost Per Request',
            unit: 'usd',
            target: 0.01,
            aggregation: 'average'
        });
    }
    
    /**
     * Initialize A/B testing framework
     */
    initializeABTesting() {
        // A/B test configurations will be stored here
        // Each test has control and variant prompts with performance tracking
    }
    
    /**
     * Track prompt performance
     */
    async trackPerformance(promptId, templateId, performanceData) {
        try {
            const timestamp = new Date().toISOString();
            const trackingKey = `${templateId}_${promptId}`;
            
            if (!this.performanceData.has(trackingKey)) {
                this.performanceData.set(trackingKey, []);
            }
            
            const data = {
                timestamp,
                promptId,
                templateId,
                ...performanceData
            };
            
            this.performanceData.get(trackingKey).push(data);
            
            // Clean old data outside tracking window
            this.cleanOldData(trackingKey);
            
            // Update insights
            await this.updateInsights(templateId);
            
            return data;
        } catch (error) {
            console.error('Performance tracking error:', error);
            throw error;
        }
    }
    
    /**
     * Create A/B test
     */
    async createABTest(testConfig) {
        try {
            const testId = this.generateTestId();
            const test = {
                id: testId,
                name: testConfig.name,
                description: testConfig.description,
                templateId: testConfig.templateId,
                control: testConfig.control, // Control prompt
                variant: testConfig.variant, // Variant prompt
                metrics: testConfig.metrics || ['quality_score', 'response_time', 'user_satisfaction'],
                status: 'active',
                startDate: new Date().toISOString(),
                endDate: null,
                sampleSize: 0,
                results: {
                    control: { samples: 0, metrics: {} },
                    variant: { samples: 0, metrics: {} }
                },
                significance: null
            };
            
            this.abTests.set(testId, test);
            
            return test;
        } catch (error) {
            console.error('A/B test creation error:', error);
            throw error;
        }
    }
    
    /**
     * Record A/B test result
     */
    async recordABTestResult(testId, version, performanceData) {
        try {
            const test = this.abTests.get(testId);
            if (!test || test.status !== 'active') {
                throw new Error(`Test ${testId} not found or not active`);
            }
            
            const versionData = test.results[version];
            if (!versionData) {
                throw new Error(`Invalid test version: ${version}`);
            }
            
            // Update sample count
            versionData.samples++;
            test.sampleSize++;
            
            // Update metrics
            for (const metric of test.metrics) {
                if (performanceData[metric] !== undefined) {
                    if (!versionData.metrics[metric]) {
                        versionData.metrics[metric] = [];
                    }
                    versionData.metrics[metric].push(performanceData[metric]);
                }
            }
            
            // Check if test is ready for analysis
            if (test.sampleSize >= this.config.minSampleSize) {
                await this.analyzeABTest(testId);
            }
            
            return test;
        } catch (error) {
            console.error('A/B test result recording error:', error);
            throw error;
        }
    }
    
    /**
     * Analyze A/B test results
     */
    async analyzeABTest(testId) {
        try {
            const test = this.abTests.get(testId);
            if (!test) {
                throw new Error(`Test ${testId} not found`);
            }
            
            const analysis = {
                testId,
                timestamp: new Date().toISOString(),
                metrics: {},
                winner: null,
                confidence: 0,
                recommendations: []
            };
            
            // Analyze each metric
            for (const metric of test.metrics) {
                const controlData = test.results.control.metrics[metric] || [];
                const variantData = test.results.variant.metrics[metric] || [];
                
                if (controlData.length > 0 && variantData.length > 0) {
                    const metricAnalysis = this.performStatisticalTest(controlData, variantData);
                    analysis.metrics[metric] = metricAnalysis;
                }
            }
            
            // Determine overall winner
            const winnerAnalysis = this.determineWinner(analysis.metrics);
            analysis.winner = winnerAnalysis.winner;
            analysis.confidence = winnerAnalysis.confidence;
            
            // Generate recommendations
            analysis.recommendations = this.generateABTestRecommendations(analysis);
            
            // Update test with analysis
            test.analysis = analysis;
            
            // End test if conclusive
            if (analysis.confidence > this.config.confidenceLevel) {
                test.status = 'completed';
                test.endDate = new Date().toISOString();
            }
            
            return analysis;
        } catch (error) {
            console.error('A/B test analysis error:', error);
            throw error;
        }
    }
    
    /**
     * Perform statistical test (simplified t-test)
     */
    performStatisticalTest(controlData, variantData) {
        const controlMean = this.calculateMean(controlData);
        const variantMean = this.calculateMean(variantData);
        const controlStd = this.calculateStandardDeviation(controlData, controlMean);
        const variantStd = this.calculateStandardDeviation(variantData, variantMean);
        
        // Calculate t-statistic
        const pooledStd = Math.sqrt(
            ((controlData.length - 1) * controlStd * controlStd + 
             (variantData.length - 1) * variantStd * variantStd) /
            (controlData.length + variantData.length - 2)
        );
        
        const tStatistic = (variantMean - controlMean) / 
            (pooledStd * Math.sqrt(1/controlData.length + 1/variantData.length));
        
        // Calculate improvement percentage
        const improvement = ((variantMean - controlMean) / controlMean) * 100;
        
        return {
            controlMean,
            variantMean,
            improvement,
            tStatistic,
            significant: Math.abs(tStatistic) > 1.96, // 95% confidence
            sampleSizes: {
                control: controlData.length,
                variant: variantData.length
            }
        };
    }
    
    /**
     * Determine overall test winner
     */
    determineWinner(metricsAnalysis) {
        let controlWins = 0;
        let variantWins = 0;
        let totalSignificant = 0;
        
        for (const [metric, analysis] of Object.entries(metricsAnalysis)) {
            if (analysis.significant) {
                totalSignificant++;
                if (analysis.improvement > 0) {
                    variantWins++;
                } else {
                    controlWins++;
                }
            }
        }
        
        let winner = 'inconclusive';
        let confidence = 0;
        
        if (totalSignificant > 0) {
            if (variantWins > controlWins) {
                winner = 'variant';
                confidence = variantWins / totalSignificant;
            } else if (controlWins > variantWins) {
                winner = 'control';
                confidence = controlWins / totalSignificant;
            }
        }
        
        return { winner, confidence };
    }
    
    /**
     * Generate A/B test recommendations
     */
    generateABTestRecommendations(analysis) {
        const recommendations = [];
        
        if (analysis.winner === 'variant') {
            recommendations.push('Implement the variant prompt as it shows significant improvement.');
        } else if (analysis.winner === 'control') {
            recommendations.push('Keep the control prompt as the variant did not show improvement.');
        } else {
            recommendations.push('Continue testing as results are inconclusive.');
        }
        
        // Metric-specific recommendations
        for (const [metric, metricAnalysis] of Object.entries(analysis.metrics)) {
            if (metricAnalysis.significant) {
                if (metricAnalysis.improvement > 10) {
                    recommendations.push(`Significant improvement in ${metric}: ${metricAnalysis.improvement.toFixed(1)}%`);
                } else if (metricAnalysis.improvement < -10) {
                    recommendations.push(`Significant decline in ${metric}: ${metricAnalysis.improvement.toFixed(1)}%`);
                }
            }
        }
        
        return recommendations;
    }
    
    /**
     * Update insights for template
     */
    async updateInsights(templateId) {
        try {
            const templateData = this.getTemplatePerformanceData(templateId);
            if (templateData.length === 0) return;
            
            const insights = {
                templateId,
                timestamp: new Date().toISOString(),
                totalSamples: templateData.length,
                metrics: {},
                trends: {},
                recommendations: []
            };
            
            // Calculate metric insights
            for (const [metricId, metric] of this.analyticsMetrics) {
                const values = templateData
                    .map(d => d[metricId])
                    .filter(v => v !== undefined);
                
                if (values.length > 0) {
                    insights.metrics[metricId] = {
                        current: this.calculateMean(values),
                        target: metric.target,
                        trend: this.calculateTrend(values),
                        performance: this.calculatePerformanceRating(values, metric.target)
                    };
                }
            }
            
            // Generate recommendations
            insights.recommendations = this.generateInsightRecommendations(insights);
            
            this.insights.set(templateId, insights);
            
            return insights;
        } catch (error) {
            console.error('Insights update error:', error);
            throw error;
        }
    }
    
    /**
     * Get template performance data
     */
    getTemplatePerformanceData(templateId) {
        const data = [];
        
        for (const [key, values] of this.performanceData) {
            if (key.startsWith(templateId)) {
                data.push(...values);
            }
        }
        
        return data.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
    }
    
    /**
     * Calculate trend
     */
    calculateTrend(values) {
        if (values.length < 2) return 'stable';
        
        const recent = values.slice(-Math.min(10, values.length));
        const older = values.slice(0, Math.min(10, values.length));
        
        const recentMean = this.calculateMean(recent);
        const olderMean = this.calculateMean(older);
        
        const change = ((recentMean - olderMean) / olderMean) * 100;
        
        if (change > 5) return 'improving';
        if (change < -5) return 'declining';
        return 'stable';
    }
    
    /**
     * Calculate performance rating
     */
    calculatePerformanceRating(values, target) {
        const mean = this.calculateMean(values);
        const ratio = mean / target;
        
        if (ratio >= 0.95) return 'excellent';
        if (ratio >= 0.85) return 'good';
        if (ratio >= 0.75) return 'fair';
        return 'poor';
    }
    
    /**
     * Generate insight recommendations
     */
    generateInsightRecommendations(insights) {
        const recommendations = [];
        
        for (const [metricId, metricData] of Object.entries(insights.metrics)) {
            if (metricData.performance === 'poor') {
                recommendations.push(`Improve ${metricId}: current performance is below target`);
            }
            
            if (metricData.trend === 'declining') {
                recommendations.push(`Address declining trend in ${metricId}`);
            }
        }
        
        return recommendations;
    }
    
    /**
     * Utility functions
     */
    calculateMean(values) {
        return values.reduce((sum, val) => sum + val, 0) / values.length;
    }
    
    calculateStandardDeviation(values, mean) {
        const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
        return Math.sqrt(variance);
    }
    
    generateTestId() {
        return 'test_' + Math.random().toString(36).substr(2, 9);
    }
    
    cleanOldData(trackingKey) {
        const data = this.performanceData.get(trackingKey);
        const cutoff = Date.now() - this.config.trackingWindow;
        
        const filteredData = data.filter(item => 
            new Date(item.timestamp).getTime() > cutoff
        );
        
        this.performanceData.set(trackingKey, filteredData);
    }
    
    /**
     * Get analytics dashboard data
     */
    getDashboardData(templateId = null) {
        const data = {
            overview: {
                totalTests: this.abTests.size,
                activeTests: Array.from(this.abTests.values()).filter(t => t.status === 'active').length,
                totalInsights: this.insights.size
            },
            tests: Array.from(this.abTests.values()),
            insights: templateId ? this.insights.get(templateId) : Array.from(this.insights.values())
        };
        
        return data;
    }
    
    /**
     * Get analytics statistics
     */
    getAnalyticsStats() {
        return {
            performanceDataPoints: Array.from(this.performanceData.values()).reduce((sum, arr) => sum + arr.length, 0),
            activeABTests: Array.from(this.abTests.values()).filter(t => t.status === 'active').length,
            completedABTests: Array.from(this.abTests.values()).filter(t => t.status === 'completed').length,
            totalInsights: this.insights.size,
            trackingWindow: this.config.trackingWindow
        };
    }
}

module.exports = PromptAnalytics;
