/**
 * OpenAI Provider - OpenAI API Integration
 * Handles OpenAI API requests with advanced features and error handling
 */

const OpenAI = require('openai');

class OpenAIProvider {
    constructor(options = {}) {
        this.id = options.id || 'openai';
        this.config = options.config || {};
        this.timeout = options.timeout || 30000;
        
        this.client = null;
        this.isInitialized = false;
        this.stats = {
            requestCount: 0,
            successCount: 0,
            errorCount: 0,
            totalTokensUsed: 0,
            totalCost: 0,
            averageResponseTime: 0,
            lastRequestTime: null
        };
        
        // Rate limiting
        this.rateLimiter = {
            requests: [],
            tokens: [],
            windowMs: 60000 // 1 minute
        };
    }
    
    /**
     * Initialize OpenAI provider
     */
    async initialize() {
        try {
            const apiKey = process.env.OPENAI_API_KEY;
            if (!apiKey) {
                throw new Error('OPENAI_API_KEY environment variable is required');
            }
            
            this.client = new OpenAI({
                apiKey: apiKey,
                organization: process.env.OPENAI_ORG_ID,
                project: process.env.OPENAI_PROJECT_ID,
                timeout: this.timeout
            });
            
            // Test connection
            await this.healthCheck();
            
            this.isInitialized = true;
            console.log('OpenAI provider initialized successfully');
        } catch (error) {
            console.error('Failed to initialize OpenAI provider:', error);
            throw error;
        }
    }
    
    /**
     * Generate content using OpenAI
     */
    async generateContent(context, prompt) {
        if (!this.isInitialized) {
            throw new Error('OpenAI provider not initialized');
        }
        
        const startTime = Date.now();
        
        try {
            // Check rate limits
            await this.checkRateLimit(context);
            
            // Prepare request parameters
            const requestParams = this.prepareRequestParams(context, prompt);
            
            // Make API request
            const response = await this.client.chat.completions.create(requestParams);
            
            // Process response
            const result = this.processResponse(response, context);
            
            // Update statistics
            this.updateStats(startTime, true, result.usage);
            
            return result;
        } catch (error) {
            this.updateStats(startTime, false);
            console.error('OpenAI content generation error:', error);
            throw this.handleError(error);
        }
    }
    
    /**
     * Prepare request parameters for OpenAI API
     */
    prepareRequestParams(context, prompt) {
        const model = context.ai?.model || process.env.OPENAI_DEFAULT_MODEL || 'gpt-4o-mini';
        const maxTokens = context.ai?.maxTokens || 4000;
        const temperature = context.ai?.temperature || 0.7;
        
        const messages = [];
        
        // Add system message if provided
        if (prompt.system) {
            messages.push({
                role: 'system',
                content: prompt.system
            });
        }
        
        // Add user message
        messages.push({
            role: 'user',
            content: prompt.user
        });
        
        const params = {
            model: model,
            messages: messages,
            max_tokens: maxTokens,
            temperature: temperature,
            top_p: context.ai?.topP || 0.9,
            frequency_penalty: context.ai?.frequencyPenalty || 0,
            presence_penalty: context.ai?.presencePenalty || 0
        };
        
        // Add response format if specified
        if (context.ai?.responseFormat === 'json') {
            params.response_format = { type: 'json_object' };
        }
        
        // Add function calling if specified
        if (context.ai?.functions) {
            params.functions = context.ai.functions;
            params.function_call = context.ai.functionCall || 'auto';
        }
        
        // Add streaming if specified
        if (context.ai?.stream) {
            params.stream = true;
        }
        
        return params;
    }
    
    /**
     * Process OpenAI API response
     */
    processResponse(response, context) {
        const choice = response.choices[0];
        if (!choice) {
            throw new Error('No response choices returned from OpenAI');
        }
        
        const content = choice.message.content;
        if (!content) {
            throw new Error('No content in OpenAI response');
        }
        
        // Calculate cost
        const usage = response.usage;
        const model = context.ai?.model || 'gpt-4o-mini';
        const cost = this.calculateCost(model, usage);
        
        return {
            content: content.trim(),
            usage: {
                promptTokens: usage.prompt_tokens,
                completionTokens: usage.completion_tokens,
                totalTokens: usage.total_tokens
            },
            cost: cost,
            model: model,
            finishReason: choice.finish_reason,
            timestamp: new Date().toISOString()
        };
    }
    
    /**
     * Calculate request cost
     */
    calculateCost(model, usage) {
        const costPerToken = this.config.costPerToken?.[model];
        if (!costPerToken) {
            return 0;
        }
        
        const inputCost = (usage.prompt_tokens / 1000) * costPerToken.input;
        const outputCost = (usage.completion_tokens / 1000) * costPerToken.output;
        
        return inputCost + outputCost;
    }
    
    /**
     * Check rate limits
     */
    async checkRateLimit(context) {
        const now = Date.now();
        const windowStart = now - this.rateLimiter.windowMs;
        
        // Clean old entries
        this.rateLimiter.requests = this.rateLimiter.requests.filter(time => time > windowStart);
        this.rateLimiter.tokens = this.rateLimiter.tokens.filter(entry => entry.time > windowStart);
        
        // Check request rate limit
        const requestsInWindow = this.rateLimiter.requests.length;
        const maxRequests = this.config.rateLimit?.requestsPerMinute || 500;
        
        if (requestsInWindow >= maxRequests) {
            throw new Error(`Rate limit exceeded: ${requestsInWindow}/${maxRequests} requests per minute`);
        }
        
        // Check token rate limit
        const tokensInWindow = this.rateLimiter.tokens.reduce((sum, entry) => sum + entry.tokens, 0);
        const maxTokens = this.config.rateLimit?.tokensPerMinute || 150000;
        const estimatedTokens = this.estimateTokens(context);
        
        if (tokensInWindow + estimatedTokens > maxTokens) {
            throw new Error(`Token rate limit exceeded: ${tokensInWindow + estimatedTokens}/${maxTokens} tokens per minute`);
        }
        
        // Record this request
        this.rateLimiter.requests.push(now);
        this.rateLimiter.tokens.push({
            time: now,
            tokens: estimatedTokens
        });
    }
    
    /**
     * Estimate token count for request
     */
    estimateTokens(context) {
        // Rough estimation: 1 token ≈ 0.75 words
        const wordCount = context.content?.wordCount || 1000;
        const promptLength = 500; // Estimated prompt length
        
        return Math.ceil((wordCount + promptLength) / 0.75);
    }
    
    /**
     * Handle API errors
     */
    handleError(error) {
        if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.error?.message || error.message;
            
            switch (status) {
                case 400:
                    return new Error(`Bad Request: ${message}`);
                case 401:
                    return new Error(`Authentication Error: ${message}`);
                case 403:
                    return new Error(`Permission Denied: ${message}`);
                case 429:
                    return new Error(`Rate Limited: ${message}`);
                case 500:
                    return new Error(`OpenAI Server Error: ${message}`);
                case 503:
                    return new Error(`OpenAI Service Unavailable: ${message}`);
                default:
                    return new Error(`OpenAI API Error (${status}): ${message}`);
            }
        }
        
        if (error.code === 'ECONNABORTED') {
            return new Error('Request timeout');
        }
        
        if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
            return new Error('Network connection error');
        }
        
        return error;
    }
    
    /**
     * Update provider statistics
     */
    updateStats(startTime, success, usage = null) {
        const responseTime = Date.now() - startTime;
        
        this.stats.requestCount++;
        this.stats.lastRequestTime = new Date().toISOString();
        
        if (success) {
            this.stats.successCount++;
            
            if (usage) {
                this.stats.totalTokensUsed += usage.totalTokens;
                this.stats.totalCost += this.calculateCost(
                    process.env.OPENAI_DEFAULT_MODEL || 'gpt-4o-mini',
                    usage
                );
            }
        } else {
            this.stats.errorCount++;
        }
        
        // Update average response time
        this.stats.averageResponseTime = (
            (this.stats.averageResponseTime * (this.stats.requestCount - 1)) + responseTime
        ) / this.stats.requestCount;
    }
    
    /**
     * Health check
     */
    async healthCheck() {
        try {
            const response = await this.client.models.list();
            return response.data && response.data.length > 0;
        } catch (error) {
            console.error('OpenAI health check failed:', error);
            return false;
        }
    }
    
    /**
     * Check if provider is available
     */
    isAvailable() {
        return this.isInitialized && this.client !== null;
    }
    
    /**
     * Get maximum tokens supported
     */
    getMaxTokens() {
        return 128000; // GPT-4 Turbo max context
    }
    
    /**
     * Get supported models
     */
    getSupportedModels() {
        return this.config.supportedModels || ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo'];
    }
    
    /**
     * Get provider statistics
     */
    getStats() {
        return {
            ...this.stats,
            successRate: this.stats.requestCount > 0 ? this.stats.successCount / this.stats.requestCount : 0,
            errorRate: this.stats.requestCount > 0 ? this.stats.errorCount / this.stats.requestCount : 0,
            averageCostPerRequest: this.stats.requestCount > 0 ? this.stats.totalCost / this.stats.requestCount : 0
        };
    }
    
    /**
     * Reset statistics
     */
    resetStats() {
        this.stats = {
            requestCount: 0,
            successCount: 0,
            errorCount: 0,
            totalTokensUsed: 0,
            totalCost: 0,
            averageResponseTime: 0,
            lastRequestTime: null
        };
    }
    
    /**
     * Update provider configuration
     */
    updateConfig(updates) {
        Object.assign(this.config, updates);
    }
    
    /**
     * Get current rate limit status
     */
    getRateLimitStatus() {
        const now = Date.now();
        const windowStart = now - this.rateLimiter.windowMs;
        
        const requestsInWindow = this.rateLimiter.requests.filter(time => time > windowStart).length;
        const tokensInWindow = this.rateLimiter.tokens
            .filter(entry => entry.time > windowStart)
            .reduce((sum, entry) => sum + entry.tokens, 0);
        
        return {
            requests: {
                current: requestsInWindow,
                limit: this.config.rateLimit?.requestsPerMinute || 500,
                remaining: Math.max(0, (this.config.rateLimit?.requestsPerMinute || 500) - requestsInWindow)
            },
            tokens: {
                current: tokensInWindow,
                limit: this.config.rateLimit?.tokensPerMinute || 150000,
                remaining: Math.max(0, (this.config.rateLimit?.tokensPerMinute || 150000) - tokensInWindow)
            }
        };
    }
    
    /**
     * Shutdown provider
     */
    async shutdown() {
        this.isInitialized = false;
        this.client = null;
        this.rateLimiter.requests = [];
        this.rateLimiter.tokens = [];
        console.log('OpenAI provider shutdown completed');
    }
}

module.exports = OpenAIProvider;
