#!/bin/bash

# SEO Pro Complete Application Stop Script
# This script stops both backend and frontend servers

echo "🛑 Stopping SEO Pro Complete Application..."
echo "=========================================="

# Set base directory
BASE_DIR="/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas-html"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to safely kill process
safe_kill() {
    local pid=$1
    local name=$2
    
    if kill -0 $pid 2>/dev/null; then
        print_info "Stopping $name (PID: $pid)..."
        kill $pid
        sleep 2
        
        if kill -0 $pid 2>/dev/null; then
            print_warning "$name didn't stop gracefully, forcing..."
            kill -9 $pid
        fi
        
        if ! kill -0 $pid 2>/dev/null; then
            print_status "$name stopped successfully"
        else
            print_error "Failed to stop $name"
        fi
    else
        print_info "$name is not running"
    fi
}

# Stop processes using PID files
if [ -f "$BASE_DIR/backend.pid" ]; then
    BACKEND_PID=$(cat "$BASE_DIR/backend.pid")
    safe_kill $BACKEND_PID "Backend Server"
    rm -f "$BASE_DIR/backend.pid"
fi

if [ -f "$BASE_DIR/frontend.pid" ]; then
    FRONTEND_PID=$(cat "$BASE_DIR/frontend.pid")
    safe_kill $FRONTEND_PID "Frontend Server"
    rm -f "$BASE_DIR/frontend.pid"
fi

# Kill any remaining processes
print_info "Checking for remaining processes..."

# Kill Node.js backend processes
if pgrep -f "node server.js" > /dev/null; then
    print_warning "Found remaining Node.js processes, stopping them..."
    pkill -f "node server.js"
fi

# Kill Python frontend processes
if pgrep -f "python3 -m http.server" > /dev/null; then
    print_warning "Found remaining Python HTTP server processes, stopping them..."
    pkill -f "python3 -m http.server"
fi

# Clean up log files
print_info "Cleaning up log files..."
[ -f "$BASE_DIR/backend/backend.log" ] && rm -f "$BASE_DIR/backend/backend.log"
[ -f "$BASE_DIR/frontend-v2/src/frontend.log" ] && rm -f "$BASE_DIR/frontend-v2/src/frontend.log"

# Verify all processes are stopped
sleep 2
if pgrep -f "node server.js" > /dev/null || pgrep -f "python3 -m http.server" > /dev/null; then
    print_error "Some processes may still be running. Please check manually."
    echo "Use: ps aux | grep -E '(node|python3.*http.server)' | grep -v grep"
else
    print_status "All application processes have been stopped"
fi

echo ""
print_status "SEO Pro application has been stopped successfully!"
echo "To start the application again, run: ./start-complete-app.sh"
echo ""