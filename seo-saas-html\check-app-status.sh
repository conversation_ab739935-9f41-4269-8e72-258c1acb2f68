#!/bin/bash

# SEO Pro Application Status Check Script
# This script checks the status of both backend and frontend servers

echo "🔍 SEO Pro Application Status Check"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to check if a URL is accessible
check_url() {
    local url=$1
    local timeout=5
    
    if curl -s --max-time $timeout "$url" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to get response time
get_response_time() {
    local url=$1
    curl -s -o /dev/null -w "%{time_total}" --max-time 5 "$url" 2>/dev/null
}

echo ""
print_info "Checking Backend Server (Port 3001)..."
echo "======================================="

if pgrep -f "node server.js" > /dev/null; then
    BACKEND_PID=$(pgrep -f "node server.js")
    print_status "Backend process is running (PID: $BACKEND_PID)"
    
    if check_url "http://localhost:3001/api/health"; then
        RESPONSE_TIME=$(get_response_time "http://localhost:3001/api/health")
        print_status "Backend API is responding (${RESPONSE_TIME}s)"
        
        # Get health check details
        HEALTH_DATA=$(curl -s "http://localhost:3001/api/health" 2>/dev/null)
        echo "  Health Status: $HEALTH_DATA"
        
        # Check WebSocket
        if netstat -tlnp 2>/dev/null | grep -q ":3001.*LISTEN"; then
            print_status "WebSocket port is listening"
        else
            print_warning "WebSocket port may not be accessible"
        fi
    else
        print_error "Backend API is not responding"
    fi
else
    print_error "Backend process is not running"
fi

echo ""
print_info "Checking Frontend Server..."
echo "============================"

# Check multiple possible ports
FRONTEND_PORTS=(8080 3000 8888 5555)
FRONTEND_FOUND=false

for port in "${FRONTEND_PORTS[@]}"; do
    if pgrep -f "python3 -m http.server $port" > /dev/null; then
        FRONTEND_PID=$(pgrep -f "python3 -m http.server $port")
        print_status "Frontend server is running on port $port (PID: $FRONTEND_PID)"
        
        if check_url "http://localhost:$port/"; then
            RESPONSE_TIME=$(get_response_time "http://localhost:$port/")
            print_status "Frontend is accessible (${RESPONSE_TIME}s)"
            
            # Check if main pages exist
            if check_url "http://localhost:$port/dashboard.html"; then
                print_status "Dashboard page is accessible"
            else
                print_warning "Dashboard page may not be accessible"
            fi
            
            if check_url "http://localhost:$port/content-generator.html"; then
                print_status "Content Generator page is accessible"
            else
                print_warning "Content Generator page may not be accessible"
            fi
        else
            print_error "Frontend server is not responding on port $port"
        fi
        
        FRONTEND_FOUND=true
        break
    fi
done

if [ "$FRONTEND_FOUND" = false ]; then
    print_error "No frontend server process found"
fi

echo ""
print_info "System Resources..."
echo "==================="

# Check memory usage
MEMORY_USAGE=$(ps aux | grep -E "(node|python3.*http.server)" | grep -v grep | awk '{sum += $6} END {print sum/1024}')
if [ ! -z "$MEMORY_USAGE" ]; then
    print_info "Total memory usage: ${MEMORY_USAGE}MB"
else
    print_info "No application processes found for memory check"
fi

# Check disk usage
DISK_USAGE=$(df -h . | tail -1 | awk '{print $5}')
print_info "Disk usage: $DISK_USAGE"

# Check system load
LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}')
print_info "System load:$LOAD_AVG"

echo ""
print_info "Network Connections..."
echo "======================="

# Check listening ports
if netstat -tlnp 2>/dev/null | grep -q ":3001.*LISTEN"; then
    print_status "Port 3001 (Backend) is listening"
else
    print_warning "Port 3001 (Backend) is not listening"
fi

for port in "${FRONTEND_PORTS[@]}"; do
    if netstat -tlnp 2>/dev/null | grep -q ":$port.*LISTEN"; then
        print_status "Port $port (Frontend) is listening"
        break
    fi
done

echo ""
print_info "Application URLs..."
echo "==================="

if pgrep -f "node server.js" > /dev/null; then
    echo "🔗 Backend API: http://localhost:3001"
    echo "   - Health Check: http://localhost:3001/api/health"
    echo "   - WebSocket: ws://localhost:3001/ws"
fi

for port in "${FRONTEND_PORTS[@]}"; do
    if pgrep -f "python3 -m http.server $port" > /dev/null; then
        echo "🔗 Frontend App: http://localhost:$port"
        echo "   - Landing Page: http://localhost:$port/"
        echo "   - Dashboard: http://localhost:$port/dashboard.html"
        echo "   - Content Generator: http://localhost:$port/content-generator.html"
        echo "   - SEO Analyzer: http://localhost:$port/seo-analyzer.html"
        break
    fi
done

echo ""
print_info "Recent Logs..."
echo "=============="

# Check backend logs
if [ -f "/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas-html/backend/server.log" ]; then
    echo "📋 Backend Log (last 3 lines):"
    tail -3 "/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas-html/backend/server.log" 2>/dev/null | sed 's/^/   /'
fi

# Check for any error logs
if [ -f "/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas-html/backend/backend.log" ]; then
    echo "📋 Backend Runtime Log (last 3 lines):"
    tail -3 "/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas-html/backend/backend.log" 2>/dev/null | sed 's/^/   /'
fi

echo ""
print_info "Quick Commands..."
echo "================="
echo "  Start App: ./start-complete-app.sh"
echo "  Stop App: ./stop-complete-app.sh"
echo "  Check Status: ./check-app-status.sh"
echo ""

# Overall status
if pgrep -f "node server.js" > /dev/null && [ "$FRONTEND_FOUND" = true ]; then
    print_status "SEO Pro Application is running successfully!"
elif pgrep -f "node server.js" > /dev/null; then
    print_warning "Backend is running but frontend may have issues"
elif [ "$FRONTEND_FOUND" = true ]; then
    print_warning "Frontend is running but backend may have issues"
else
    print_error "Application is not running"
    echo "Run ./start-complete-app.sh to start the application"
fi

echo ""