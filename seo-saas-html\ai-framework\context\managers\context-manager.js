/**
 * Context Manager - Advanced Context Engineering System
 * Manages context creation, storage, retrieval, and optimization for AI interactions
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

class ContextManager {
    constructor(options = {}) {
        this.contextSchema = null;
        this.contextStore = new Map();
        this.contextHistory = new Map();
        this.optimizationRules = new Map();
        this.performanceMetrics = new Map();
        
        // Configuration
        this.config = {
            maxContextAge: options.maxContextAge || 24 * 60 * 60 * 1000, // 24 hours
            maxHistorySize: options.maxHistorySize || 100,
            autoOptimize: options.autoOptimize !== false,
            persistContext: options.persistContext !== false,
            contextStorePath: options.contextStorePath || './context-store',
            ...options
        };
        
        this.initialize();
    }
    
    /**
     * Initialize the context manager
     */
    async initialize() {
        try {
            // Load context schema
            await this.loadContextSchema();
            
            // Initialize context store directory
            if (this.config.persistContext) {
                await this.initializeContextStore();
            }
            
            // Load optimization rules
            await this.loadOptimizationRules();
            
            console.log('ContextManager initialized successfully');
        } catch (error) {
            console.error('Failed to initialize ContextManager:', error);
            throw error;
        }
    }
    
    /**
     * Load context schema from file
     */
    async loadContextSchema() {
        try {
            const schemaPath = path.join(__dirname, '../schemas/context-schema.json');
            const schemaContent = await fs.readFile(schemaPath, 'utf8');
            this.contextSchema = JSON.parse(schemaContent);
        } catch (error) {
            console.error('Failed to load context schema:', error);
            throw error;
        }
    }
    
    /**
     * Initialize context store directory
     */
    async initializeContextStore() {
        try {
            await fs.mkdir(this.config.contextStorePath, { recursive: true });
        } catch (error) {
            console.error('Failed to initialize context store:', error);
        }
    }
    
    /**
     * Load optimization rules
     */
    async loadOptimizationRules() {
        // Default optimization rules
        this.optimizationRules.set('keyword_density', {
            condition: (context) => context.seo?.optimization?.keywordDensity?.current > 3.0,
            action: (context) => {
                context.seo.optimization.keywordDensity.target = Math.min(
                    context.seo.optimization.keywordDensity.target,
                    2.5
                );
                return context;
            }
        });
        
        this.optimizationRules.set('word_count_optimization', {
            condition: (context) => context.content?.wordCount > 2500,
            action: (context) => {
                context.ai.maxTokens = Math.min(context.ai.maxTokens, 6000);
                return context;
            }
        });
        
        this.optimizationRules.set('performance_optimization', {
            condition: (context) => {
                const metrics = this.performanceMetrics.get(context.contextId);
                return metrics && metrics.responseTime > 5000;
            },
            action: (context) => {
                context.ai.temperature = Math.max(context.ai.temperature - 0.1, 0.3);
                context.ai.maxTokens = Math.min(context.ai.maxTokens, 3000);
                return context;
            }
        });
    }
    
    /**
     * Create a new context instance
     */
    async createContext(baseContext = {}) {
        try {
            const contextId = this.generateContextId();
            const timestamp = new Date().toISOString();
            
            // Create context with defaults
            const context = {
                contextId,
                version: '1.0.0',
                timestamp,
                user: {
                    userId: baseContext.userId || 'anonymous',
                    subscription: baseContext.subscription || 'free',
                    preferences: baseContext.preferences || {}
                },
                project: baseContext.project || {},
                content: baseContext.content || {},
                seo: {
                    targetLocation: 'Global',
                    language: 'en',
                    ...baseContext.seo
                },
                ai: {
                    provider: 'openai',
                    model: 'gpt-4o-mini',
                    temperature: 0.7,
                    maxTokens: 4000,
                    ...baseContext.ai
                },
                performance: {
                    metrics: {},
                    optimization: {
                        suggestions: [],
                        appliedOptimizations: []
                    }
                },
                ...baseContext
            };
            
            // Validate context
            const validationResult = await this.validateContext(context);
            if (!validationResult.valid) {
                throw new Error(`Context validation failed: ${validationResult.errors.join(', ')}`);
            }
            
            // Store context
            this.contextStore.set(contextId, context);
            
            // Initialize history
            this.contextHistory.set(contextId, []);
            
            // Persist if enabled
            if (this.config.persistContext) {
                await this.persistContext(context);
            }
            
            return context;
        } catch (error) {
            console.error('Failed to create context:', error);
            throw error;
        }
    }
    
    /**
     * Retrieve context by ID
     */
    async getContext(contextId) {
        try {
            // Check memory store first
            if (this.contextStore.has(contextId)) {
                const context = this.contextStore.get(contextId);
                
                // Check if context is expired
                if (this.isContextExpired(context)) {
                    this.contextStore.delete(contextId);
                    this.contextHistory.delete(contextId);
                    return null;
                }
                
                return context;
            }
            
            // Try to load from persistent store
            if (this.config.persistContext) {
                const context = await this.loadPersistedContext(contextId);
                if (context && !this.isContextExpired(context)) {
                    this.contextStore.set(contextId, context);
                    return context;
                }
            }
            
            return null;
        } catch (error) {
            console.error('Failed to get context:', error);
            return null;
        }
    }
    
    /**
     * Update existing context
     */
    async updateContext(contextId, updates) {
        try {
            const context = await this.getContext(contextId);
            if (!context) {
                throw new Error(`Context ${contextId} not found`);
            }
            
            // Store current state in history
            const history = this.contextHistory.get(contextId) || [];
            history.push({
                timestamp: new Date().toISOString(),
                state: JSON.parse(JSON.stringify(context))
            });
            
            // Limit history size
            if (history.length > this.config.maxHistorySize) {
                history.shift();
            }
            this.contextHistory.set(contextId, history);
            
            // Apply updates
            const updatedContext = this.deepMerge(context, updates);
            updatedContext.timestamp = new Date().toISOString();
            
            // Validate updated context
            const validationResult = await this.validateContext(updatedContext);
            if (!validationResult.valid) {
                throw new Error(`Context validation failed: ${validationResult.errors.join(', ')}`);
            }
            
            // Apply optimizations if enabled
            if (this.config.autoOptimize) {
                await this.optimizeContext(updatedContext);
            }
            
            // Store updated context
            this.contextStore.set(contextId, updatedContext);
            
            // Persist if enabled
            if (this.config.persistContext) {
                await this.persistContext(updatedContext);
            }
            
            return updatedContext;
        } catch (error) {
            console.error('Failed to update context:', error);
            throw error;
        }
    }
    
    /**
     * Optimize context based on rules and performance data
     */
    async optimizeContext(context) {
        try {
            let optimizedContext = { ...context };
            const appliedOptimizations = [];
            
            // Apply optimization rules
            for (const [ruleName, rule] of this.optimizationRules) {
                if (rule.condition(optimizedContext)) {
                    optimizedContext = rule.action(optimizedContext);
                    appliedOptimizations.push(ruleName);
                }
            }
            
            // Update optimization tracking
            if (appliedOptimizations.length > 0) {
                optimizedContext.performance.optimization.appliedOptimizations.push(
                    ...appliedOptimizations
                );
            }
            
            return optimizedContext;
        } catch (error) {
            console.error('Failed to optimize context:', error);
            return context;
        }
    }
    
    /**
     * Validate context against schema
     */
    async validateContext(context) {
        try {
            const errors = [];
            
            // Basic required fields validation
            const requiredFields = ['contextId', 'version', 'timestamp', 'user', 'content', 'seo', 'ai'];
            for (const field of requiredFields) {
                if (!context[field]) {
                    errors.push(`Missing required field: ${field}`);
                }
            }
            
            // User validation
            if (context.user) {
                if (!context.user.userId) errors.push('Missing user.userId');
                if (!context.user.subscription) errors.push('Missing user.subscription');
            }
            
            // Content validation
            if (context.content) {
                if (!context.content.type) errors.push('Missing content.type');
                if (!context.content.primaryKeyword) errors.push('Missing content.primaryKeyword');
                if (!context.content.intent) errors.push('Missing content.intent');
                if (!context.content.wordCount) errors.push('Missing content.wordCount');
            }
            
            // SEO validation
            if (context.seo) {
                if (!context.seo.targetLocation) errors.push('Missing seo.targetLocation');
                if (!context.seo.language) errors.push('Missing seo.language');
            }
            
            // AI validation
            if (context.ai) {
                if (!context.ai.provider) errors.push('Missing ai.provider');
                if (!context.ai.model) errors.push('Missing ai.model');
            }
            
            return {
                valid: errors.length === 0,
                errors
            };
        } catch (error) {
            console.error('Context validation error:', error);
            return {
                valid: false,
                errors: ['Validation process failed']
            };
        }
    }
    
    /**
     * Generate unique context ID
     */
    generateContextId() {
        return 'ctx_' + crypto.randomBytes(8).toString('hex');
    }
    
    /**
     * Check if context is expired
     */
    isContextExpired(context) {
        const contextAge = Date.now() - new Date(context.timestamp).getTime();
        return contextAge > this.config.maxContextAge;
    }
    
    /**
     * Deep merge objects
     */
    deepMerge(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.deepMerge(result[key] || {}, source[key]);
            } else {
                result[key] = source[key];
            }
        }
        
        return result;
    }
    
    /**
     * Persist context to storage
     */
    async persistContext(context) {
        try {
            const filePath = path.join(this.config.contextStorePath, `${context.contextId}.json`);
            await fs.writeFile(filePath, JSON.stringify(context, null, 2));
        } catch (error) {
            console.error('Failed to persist context:', error);
        }
    }
    
    /**
     * Load persisted context
     */
    async loadPersistedContext(contextId) {
        try {
            const filePath = path.join(this.config.contextStorePath, `${contextId}.json`);
            const content = await fs.readFile(filePath, 'utf8');
            return JSON.parse(content);
        } catch (error) {
            return null;
        }
    }
    
    /**
     * Get context statistics
     */
    getContextStats() {
        return {
            totalContexts: this.contextStore.size,
            totalHistory: Array.from(this.contextHistory.values()).reduce((sum, history) => sum + history.length, 0),
            optimizationRules: this.optimizationRules.size,
            performanceMetrics: this.performanceMetrics.size
        };
    }
    
    /**
     * Clean up expired contexts
     */
    async cleanupExpiredContexts() {
        const expiredContexts = [];
        
        for (const [contextId, context] of this.contextStore) {
            if (this.isContextExpired(context)) {
                expiredContexts.push(contextId);
            }
        }
        
        for (const contextId of expiredContexts) {
            this.contextStore.delete(contextId);
            this.contextHistory.delete(contextId);
            this.performanceMetrics.delete(contextId);
        }
        
        return expiredContexts.length;
    }
}

module.exports = ContextManager;
