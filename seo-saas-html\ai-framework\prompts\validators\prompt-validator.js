/**
 * Prompt Validator - Prompt Quality and Effectiveness Testing System
 * Validates prompt quality, effectiveness, and optimization potential
 */

class PromptValidator {
    constructor(options = {}) {
        this.validationRules = new Map();
        this.qualityMetrics = new Map();
        this.testResults = new Map();
        
        // Configuration
        this.config = {
            enableStrictValidation: options.enableStrictValidation !== false,
            qualityThreshold: options.qualityThreshold || 0.8,
            maxPromptLength: options.maxPromptLength || 8000,
            minPromptLength: options.minPromptLength || 100,
            ...options
        };
        
        this.initialize();
    }
    
    /**
     * Initialize the validator
     */
    initialize() {
        try {
            // Initialize validation rules
            this.initializeValidationRules();
            
            // Initialize quality metrics
            this.initializeQualityMetrics();
            
            console.log('PromptValidator initialized successfully');
        } catch (error) {
            console.error('Failed to initialize PromptValidator:', error);
            throw error;
        }
    }
    
    /**
     * Initialize validation rules
     */
    initializeValidationRules() {
        // Length validation rules
        this.validationRules.set('prompt_length', {
            name: 'Prompt Length Validation',
            category: 'structure',
            priority: 1,
            validate: (prompt) => {
                const totalLength = (prompt.system || '').length + (prompt.user || '').length;
                
                if (totalLength < this.config.minPromptLength) {
                    return {
                        valid: false,
                        message: `Prompt too short (${totalLength} chars). Minimum: ${this.config.minPromptLength}`,
                        severity: 'error'
                    };
                }
                
                if (totalLength > this.config.maxPromptLength) {
                    return {
                        valid: false,
                        message: `Prompt too long (${totalLength} chars). Maximum: ${this.config.maxPromptLength}`,
                        severity: 'warning'
                    };
                }
                
                return { valid: true };
            }
        });
        
        // Keyword presence validation
        this.validationRules.set('keyword_presence', {
            name: 'Keyword Presence Validation',
            category: 'seo',
            priority: 1,
            validate: (prompt) => {
                const userPrompt = prompt.user || '';
                const variables = prompt.variables || {};
                
                // Check for primary keyword
                if (variables.primaryKeyword && !userPrompt.includes(variables.primaryKeyword)) {
                    return {
                        valid: false,
                        message: 'Primary keyword not found in prompt',
                        severity: 'error'
                    };
                }
                
                // Check for keyword density instructions
                if (!userPrompt.includes('keyword density') && !userPrompt.includes('keyword usage')) {
                    return {
                        valid: false,
                        message: 'Missing keyword density instructions',
                        severity: 'warning'
                    };
                }
                
                return { valid: true };
            }
        });
        
        // SEO optimization validation
        this.validationRules.set('seo_optimization', {
            name: 'SEO Optimization Validation',
            category: 'seo',
            priority: 2,
            validate: (prompt) => {
                const userPrompt = prompt.user || '';
                const seoElements = [
                    'meta description',
                    'heading',
                    'SEO',
                    'search engine',
                    'optimization'
                ];
                
                const foundElements = seoElements.filter(element => 
                    userPrompt.toLowerCase().includes(element.toLowerCase())
                );
                
                if (foundElements.length < 2) {
                    return {
                        valid: false,
                        message: 'Insufficient SEO optimization instructions',
                        severity: 'warning'
                    };
                }
                
                return { valid: true };
            }
        });
        
        // E-E-A-T validation
        this.validationRules.set('eeat_compliance', {
            name: 'E-E-A-T Compliance Validation',
            category: 'quality',
            priority: 2,
            validate: (prompt) => {
                const userPrompt = prompt.user || '';
                const eatElements = [
                    'experience',
                    'expertise',
                    'authoritative',
                    'trustworthy',
                    'credible'
                ];
                
                const foundElements = eatElements.filter(element => 
                    userPrompt.toLowerCase().includes(element.toLowerCase())
                );
                
                if (foundElements.length === 0) {
                    return {
                        valid: false,
                        message: 'Missing E-E-A-T compliance instructions',
                        severity: 'warning'
                    };
                }
                
                return { valid: true };
            }
        });
        
        // Clarity and specificity validation
        this.validationRules.set('clarity_specificity', {
            name: 'Clarity and Specificity Validation',
            category: 'quality',
            priority: 1,
            validate: (prompt) => {
                const userPrompt = prompt.user || '';
                
                // Check for vague terms
                const vagueTerms = ['good', 'nice', 'great', 'awesome', 'amazing'];
                const foundVagueTerms = vagueTerms.filter(term => 
                    userPrompt.toLowerCase().includes(term)
                );
                
                if (foundVagueTerms.length > 2) {
                    return {
                        valid: false,
                        message: `Too many vague terms found: ${foundVagueTerms.join(', ')}`,
                        severity: 'warning'
                    };
                }
                
                // Check for specific instructions
                const specificInstructions = [
                    'word count',
                    'tone',
                    'target audience',
                    'format',
                    'structure'
                ];
                
                const foundInstructions = specificInstructions.filter(instruction => 
                    userPrompt.toLowerCase().includes(instruction.toLowerCase())
                );
                
                if (foundInstructions.length < 3) {
                    return {
                        valid: false,
                        message: 'Insufficient specific instructions',
                        severity: 'warning'
                    };
                }
                
                return { valid: true };
            }
        });
        
        // Template variable validation
        this.validationRules.set('template_variables', {
            name: 'Template Variables Validation',
            category: 'structure',
            priority: 1,
            validate: (prompt) => {
                const userPrompt = prompt.user || '';
                const variables = prompt.variables || {};
                
                // Check for undefined variables in template
                const undefinedVariables = [];
                const variablePattern = /\{\{(\w+)\}\}/g;
                let match;
                
                while ((match = variablePattern.exec(userPrompt)) !== null) {
                    const varName = match[1];
                    if (!variables.hasOwnProperty(varName)) {
                        undefinedVariables.push(varName);
                    }
                }
                
                if (undefinedVariables.length > 0) {
                    return {
                        valid: false,
                        message: `Undefined template variables: ${undefinedVariables.join(', ')}`,
                        severity: 'error'
                    };
                }
                
                return { valid: true };
            }
        });
        
        // Output format validation
        this.validationRules.set('output_format', {
            name: 'Output Format Validation',
            category: 'structure',
            priority: 2,
            validate: (prompt) => {
                const userPrompt = prompt.user || '';
                
                // Check for output format instructions
                if (!userPrompt.includes('Output Format') && !userPrompt.includes('format')) {
                    return {
                        valid: false,
                        message: 'Missing output format instructions',
                        severity: 'warning'
                    };
                }
                
                // Check for structured output elements
                const structureElements = ['1.', '2.', '3.', '-', '*'];
                const hasStructure = structureElements.some(element => 
                    userPrompt.includes(element)
                );
                
                if (!hasStructure) {
                    return {
                        valid: false,
                        message: 'Missing structured output format',
                        severity: 'warning'
                    };
                }
                
                return { valid: true };
            }
        });
    }
    
    /**
     * Initialize quality metrics
     */
    initializeQualityMetrics() {
        this.qualityMetrics.set('completeness', {
            name: 'Prompt Completeness',
            calculate: (prompt) => {
                const requiredElements = [
                    'system prompt',
                    'user prompt',
                    'variables',
                    'template'
                ];
                
                let score = 0;
                if (prompt.system && prompt.system.length > 50) score += 0.25;
                if (prompt.user && prompt.user.length > 100) score += 0.25;
                if (prompt.variables && Object.keys(prompt.variables).length > 0) score += 0.25;
                if (prompt.template) score += 0.25;
                
                return score;
            },
            weight: 1.0
        });
        
        this.qualityMetrics.set('specificity', {
            name: 'Prompt Specificity',
            calculate: (prompt) => {
                const userPrompt = prompt.user || '';
                const specificTerms = [
                    'word count', 'tone', 'industry', 'audience', 'keyword',
                    'format', 'structure', 'style', 'length', 'density'
                ];
                
                const foundTerms = specificTerms.filter(term => 
                    userPrompt.toLowerCase().includes(term.toLowerCase())
                );
                
                return Math.min(1.0, foundTerms.length / 6);
            },
            weight: 0.9
        });
        
        this.qualityMetrics.set('seo_focus', {
            name: 'SEO Focus Level',
            calculate: (prompt) => {
                const userPrompt = prompt.user || '';
                const seoTerms = [
                    'seo', 'keyword', 'meta', 'optimization', 'search engine',
                    'ranking', 'serp', 'organic', 'visibility'
                ];
                
                const foundTerms = seoTerms.filter(term => 
                    userPrompt.toLowerCase().includes(term.toLowerCase())
                );
                
                return Math.min(1.0, foundTerms.length / 5);
            },
            weight: 0.8
        });
        
        this.qualityMetrics.set('clarity', {
            name: 'Prompt Clarity',
            calculate: (prompt) => {
                const userPrompt = prompt.user || '';
                
                // Penalize for unclear language
                const unclearTerms = ['maybe', 'perhaps', 'might', 'could', 'possibly'];
                const foundUnclear = unclearTerms.filter(term => 
                    userPrompt.toLowerCase().includes(term)
                );
                
                // Reward clear instructions
                const clearTerms = ['must', 'should', 'ensure', 'include', 'create'];
                const foundClear = clearTerms.filter(term => 
                    userPrompt.toLowerCase().includes(term)
                );
                
                const clarityScore = Math.max(0, 1 - (foundUnclear.length * 0.2) + (foundClear.length * 0.1));
                return Math.min(1.0, clarityScore);
            },
            weight: 0.9
        });
        
        this.qualityMetrics.set('actionability', {
            name: 'Prompt Actionability',
            calculate: (prompt) => {
                const userPrompt = prompt.user || '';
                const actionTerms = [
                    'create', 'generate', 'write', 'develop', 'build',
                    'analyze', 'optimize', 'include', 'focus', 'ensure'
                ];
                
                const foundActions = actionTerms.filter(term => 
                    userPrompt.toLowerCase().includes(term)
                );
                
                return Math.min(1.0, foundActions.length / 5);
            },
            weight: 0.8
        });
    }
    
    /**
     * Validate prompt comprehensively
     */
    async validatePrompt(prompt) {
        try {
            const validationResult = {
                valid: true,
                errors: [],
                warnings: [],
                qualityScore: 0,
                categoryScores: {},
                suggestions: [],
                timestamp: new Date().toISOString()
            };
            
            // Run validation rules
            const ruleResults = await this.runValidationRules(prompt);
            validationResult.errors = ruleResults.errors;
            validationResult.warnings = ruleResults.warnings;
            validationResult.categoryScores = ruleResults.categoryScores;
            
            if (ruleResults.errors.length > 0) {
                validationResult.valid = false;
            }
            
            // Calculate quality score
            validationResult.qualityScore = await this.calculateQualityScore(prompt);
            
            // Generate suggestions
            validationResult.suggestions = await this.generateSuggestions(prompt, validationResult);
            
            // Store test results
            if (prompt.template) {
                this.testResults.set(prompt.template, validationResult);
            }
            
            return validationResult;
        } catch (error) {
            console.error('Prompt validation error:', error);
            return {
                valid: false,
                errors: ['Validation process failed'],
                warnings: [],
                qualityScore: 0,
                categoryScores: {},
                suggestions: [],
                timestamp: new Date().toISOString()
            };
        }
    }
    
    /**
     * Run validation rules
     */
    async runValidationRules(prompt) {
        const errors = [];
        const warnings = [];
        const categoryScores = {};
        
        for (const [ruleId, rule] of this.validationRules) {
            try {
                const result = rule.validate(prompt);
                
                if (!result.valid) {
                    if (result.severity === 'error') {
                        errors.push(`${rule.name}: ${result.message}`);
                    } else {
                        warnings.push(`${rule.name}: ${result.message}`);
                    }
                }
                
                // Track category scores
                if (!categoryScores[rule.category]) {
                    categoryScores[rule.category] = { passed: 0, total: 0, score: 0 };
                }
                
                categoryScores[rule.category].total++;
                if (result.valid) {
                    categoryScores[rule.category].passed++;
                }
            } catch (error) {
                console.error(`Validation rule error for ${ruleId}:`, error);
                warnings.push(`Rule ${rule.name} failed to execute`);
            }
        }
        
        // Calculate category scores
        for (const category in categoryScores) {
            const cat = categoryScores[category];
            cat.score = cat.total > 0 ? cat.passed / cat.total : 0;
        }
        
        return { errors, warnings, categoryScores };
    }
    
    /**
     * Calculate overall quality score
     */
    async calculateQualityScore(prompt) {
        let totalScore = 0;
        let totalWeight = 0;
        
        for (const [metricId, metric] of this.qualityMetrics) {
            try {
                const score = metric.calculate(prompt);
                totalScore += score * metric.weight;
                totalWeight += metric.weight;
            } catch (error) {
                console.error(`Quality metric error for ${metricId}:`, error);
            }
        }
        
        return totalWeight > 0 ? totalScore / totalWeight : 0;
    }
    
    /**
     * Generate improvement suggestions
     */
    async generateSuggestions(prompt, validationResult) {
        const suggestions = [];
        
        // Quality-based suggestions
        if (validationResult.qualityScore < this.config.qualityThreshold) {
            suggestions.push('Overall prompt quality is below threshold. Consider improving specificity and clarity.');
        }
        
        // Category-based suggestions
        if (validationResult.categoryScores.seo?.score < 0.8) {
            suggestions.push('Add more specific SEO optimization instructions.');
        }
        
        if (validationResult.categoryScores.quality?.score < 0.8) {
            suggestions.push('Include E-E-A-T compliance and quality guidelines.');
        }
        
        if (validationResult.categoryScores.structure?.score < 0.8) {
            suggestions.push('Improve prompt structure and output format instructions.');
        }
        
        // Specific improvement suggestions
        const userPrompt = prompt.user || '';
        
        if (!userPrompt.includes('example')) {
            suggestions.push('Consider adding examples to clarify expectations.');
        }
        
        if (!userPrompt.includes('avoid')) {
            suggestions.push('Add instructions about what to avoid for better guidance.');
        }
        
        if (userPrompt.length < 500) {
            suggestions.push('Prompt may be too brief. Consider adding more detailed instructions.');
        }
        
        return suggestions;
    }
    
    /**
     * Get validation statistics
     */
    getValidationStats() {
        return {
            totalRules: this.validationRules.size,
            totalMetrics: this.qualityMetrics.size,
            testResults: this.testResults.size,
            qualityThreshold: this.config.qualityThreshold
        };
    }
    
    /**
     * Get test results for template
     */
    getTestResults(templateId) {
        return this.testResults.get(templateId);
    }
    
    /**
     * Clear test results
     */
    clearTestResults() {
        this.testResults.clear();
    }
}

module.exports = PromptValidator;
