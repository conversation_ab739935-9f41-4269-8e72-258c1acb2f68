#!/bin/bash

# SEO Pro Complete Application Startup Script
# This script starts both backend and frontend servers

echo "🚀 Starting SEO Pro Complete Application..."
echo "=========================================="

# Set base directory
BASE_DIR="/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas-html"
BACKEND_DIR="$BASE_DIR/backend"
FRONTEND_DIR="$BASE_DIR/frontend-v2/src"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1
    else
        return 0
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    print_info "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            print_status "$service_name is ready!"
            return 0
        fi
        sleep 1
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within 30 seconds"
    return 1
}

# Kill existing processes
print_info "Checking for existing processes..."
if pgrep -f "node server.js" > /dev/null; then
    print_warning "Stopping existing backend server..."
    pkill -f "node server.js"
    sleep 2
fi

if pgrep -f "python3 -m http.server" > /dev/null; then
    print_warning "Stopping existing frontend server..."
    pkill -f "python3 -m http.server"
    sleep 2
fi

# Check if directories exist
if [ ! -d "$BACKEND_DIR" ]; then
    print_error "Backend directory not found: $BACKEND_DIR"
    exit 1
fi

if [ ! -d "$FRONTEND_DIR" ]; then
    print_error "Frontend directory not found: $FRONTEND_DIR"
    exit 1
fi

# Start Backend Server
print_info "Starting Backend Server..."
cd "$BACKEND_DIR"

# Check if backend dependencies are installed
if [ ! -d "node_modules" ]; then
    print_warning "Backend dependencies not found. Installing..."
    npm install
fi

# Check if backend port is available
if ! check_port 3001; then
    print_error "Port 3001 is already in use. Please free the port and try again."
    exit 1
fi

# Start backend server
print_info "Launching Node.js backend server on port 3001..."
nohup node server.js > backend.log 2>&1 &
BACKEND_PID=$!

# Wait for backend to be ready
if wait_for_service "http://localhost:3001/api/health" "Backend Server"; then
    print_status "Backend server is running (PID: $BACKEND_PID)"
else
    print_error "Backend server failed to start"
    exit 1
fi

# Start Frontend Server
print_info "Starting Frontend Server..."
cd "$FRONTEND_DIR"

# Find available port for frontend
FRONTEND_PORT=8080
if ! check_port $FRONTEND_PORT; then
    print_warning "Port $FRONTEND_PORT is in use, trying port 3000..."
    FRONTEND_PORT=3000
    if ! check_port $FRONTEND_PORT; then
        print_warning "Port $FRONTEND_PORT is in use, trying port 8888..."
        FRONTEND_PORT=8888
        if ! check_port $FRONTEND_PORT; then
            print_error "Could not find available port for frontend"
            exit 1
        fi
    fi
fi

# Start frontend server
print_info "Launching Python frontend server on port $FRONTEND_PORT..."
nohup python3 -m http.server $FRONTEND_PORT > frontend.log 2>&1 &
FRONTEND_PID=$!

# Wait for frontend to be ready
if wait_for_service "http://localhost:$FRONTEND_PORT/" "Frontend Server"; then
    print_status "Frontend server is running (PID: $FRONTEND_PID)"
else
    print_error "Frontend server failed to start"
    exit 1
fi

# Display success message
echo ""
echo "🎉 SEO Pro Application is now running!"
echo "======================================"
echo ""
print_status "Backend API Server: http://localhost:3001"
print_info "  - Health Check: http://localhost:3001/api/health"
print_info "  - WebSocket: ws://localhost:3001/ws"
print_info "  - Log file: $BACKEND_DIR/backend.log"
echo ""
print_status "Frontend Application: http://localhost:$FRONTEND_PORT"
print_info "  - Landing Page: http://localhost:$FRONTEND_PORT/"
print_info "  - Dashboard: http://localhost:$FRONTEND_PORT/dashboard.html"
print_info "  - Log file: $FRONTEND_DIR/frontend.log"
echo ""

# Display feature overview
echo "🌟 Available Features:"
echo "====================="
echo "  📊 Dashboard - Real-time analytics and metrics"
echo "  📝 Content Generator - AI-powered content creation"
echo "  ✏️  Content Editor - Professional content editing with SEO analysis"
echo "  🔍 SEO Analyzer - Comprehensive SEO analysis and optimization"
echo "  🏆 Competitor Analysis - Detailed competitor research and insights"
echo "  📦 Bulk Operations - Batch processing for large-scale operations"
echo "  🎨 Dark Mode - Complete theme system with auto-detection"
echo "  📱 Responsive Design - Mobile-first approach with accessibility"
echo ""

# Display process information
echo "📋 Process Information:"
echo "======================"
echo "  Backend PID: $BACKEND_PID"
echo "  Frontend PID: $FRONTEND_PID"
echo ""
echo "To stop the application, run:"
echo "  kill $BACKEND_PID $FRONTEND_PID"
echo ""
echo "Or use the stop script: ./stop-complete-app.sh"
echo ""

# Create PID file for easy management
echo "$BACKEND_PID" > "$BASE_DIR/backend.pid"
echo "$FRONTEND_PID" > "$BASE_DIR/frontend.pid"

print_status "Application started successfully!"
print_info "Press Ctrl+C to stop monitoring or close this terminal to run in background"

# Monitor processes
while true; do
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        print_error "Backend server has stopped unexpectedly"
        break
    fi
    
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        print_error "Frontend server has stopped unexpectedly"
        break
    fi
    
    sleep 5
done