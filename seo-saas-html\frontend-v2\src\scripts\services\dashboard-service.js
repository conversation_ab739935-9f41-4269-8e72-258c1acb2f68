/**
 * Dashboard Service
 * Handles dashboard analytics and metrics API calls
 */

class DashboardService {
  constructor(apiClient) {
    this.api = apiClient;
  }

  /**
   * Get dashboard overview
   */
  async getOverview(period = '30d') {
    return await this.api.get(`/dashboard/overview?period=${period}`);
  }

  /**
   * Get detailed analytics
   */
  async getAnalytics(period = '30d', metrics = []) {
    const params = new URLSearchParams({
      period,
      metrics: metrics.join(',')
    });

    return await this.api.get(`/dashboard/analytics?${params}`);
  }

  /**
   * Get system health metrics
   */
  async getSystemHealth() {
    return await this.api.get('/dashboard/health');
  }

  /**
   * Get user activity metrics
   */
  async getUserActivity(period = '30d') {
    return await this.api.get(`/dashboard/user-activity?period=${period}`);
  }

  /**
   * Get content generation metrics
   */
  async getContentMetrics(period = '30d') {
    return await this.api.get(`/dashboard/content-metrics?period=${period}`);
  }

  /**
   * Get SEO performance metrics
   */
  async getSEOMetrics(period = '30d') {
    return await this.api.get(`/api/dashboard/seo-metrics?period=${period}`);
  }

  /**
   * Get competitor analysis metrics
   */
  async getCompetitorMetrics(period = '30d') {
    return await this.api.get(`/api/dashboard/competitor-metrics?period=${period}`);
  }

  /**
   * Get usage statistics
   */
  async getUsageStats(period = '30d') {
    return await this.api.get(`/api/dashboard/usage-stats?period=${period}`);
  }

  /**
   * Get subscription metrics
   */
  async getSubscriptionMetrics() {
    return await this.api.get('/api/dashboard/subscription-metrics');
  }

  /**
   * Get revenue metrics
   */
  async getRevenueMetrics(period = '30d') {
    return await this.api.get(`/api/dashboard/revenue-metrics?period=${period}`);
  }

  /**
   * Get top performing content
   */
  async getTopContent(period = '30d', limit = 10) {
    return await this.api.get(`/api/dashboard/top-content?period=${period}&limit=${limit}`);
  }

  /**
   * Get top keywords
   */
  async getTopKeywords(period = '30d', limit = 10) {
    return await this.api.get(`/api/dashboard/top-keywords?period=${period}&limit=${limit}`);
  }

  /**
   * Get trending topics
   */
  async getTrendingTopics(period = '7d', limit = 10) {
    return await this.api.get(`/dashboard/trending-topics?period=${period}&limit=${limit}`);
  }

  /**
   * Get recent activities
   */
  async getRecentActivities(limit = 20) {
    return await this.api.get(`/dashboard/recent-activities?limit=${limit}`);
  }

  /**
   * Get notifications
   */
  async getNotifications(page = 1, limit = 20, unreadOnly = false) {
    // Return mock notifications for now
    return Promise.resolve([
      {
        id: 1,
        type: 'success',
        message: 'Content generation completed successfully',
        time: new Date(Date.now() - 120000).toISOString(),
        read: false
      },
      {
        id: 2,
        type: 'info',
        message: 'New SEO recommendations available',
        time: new Date(Date.now() - 3600000).toISOString(),
        read: false
      },
      {
        id: 3,
        type: 'warning',
        message: 'API usage at 80% of monthly limit',
        time: new Date(Date.now() - 10800000).toISOString(),
        read: true
      }
    ]);
  }

  /**
   * Mark notification as read
   */
  async markNotificationRead(notificationId) {
    return await this.api.put(`/dashboard/notifications/${notificationId}/read`);
  }

  /**
   * Mark all notifications as read
   */
  async markAllNotificationsRead() {
    return await this.api.put('/dashboard/notifications/read-all');
  }

  /**
   * Delete notification
   */
  async deleteNotification(notificationId) {
    return await this.api.delete(`/dashboard/notifications/${notificationId}`);
  }

  /**
   * Get alerts
   */
  async getAlerts(active = true) {
    return await this.api.get(`/api/dashboard/alerts?active=${active}`);
  }

  /**
   * Dismiss alert
   */
  async dismissAlert(alertId) {
    return await this.api.put(`/api/dashboard/alerts/${alertId}/dismiss`);
  }

  /**
   * Get performance trends
   */
  async getPerformanceTrends(metric, period = '30d') {
    return await this.api.get(`/api/dashboard/trends/${metric}?period=${period}`);
  }

  /**
   * Get comparative metrics
   */
  async getComparativeMetrics(period = '30d', comparison = 'previous') {
    return await this.api.get(`/api/dashboard/comparative-metrics?period=${period}&comparison=${comparison}`);
  }

  /**
   * Get goal progress
   */
  async getGoalProgress() {
    return await this.api.get('/api/dashboard/goals/progress');
  }

  /**
   * Create goal
   */
  async createGoal(goalData) {
    return await this.api.post('/api/dashboard/goals', goalData);
  }

  /**
   * Update goal
   */
  async updateGoal(goalId, goalData) {
    return await this.api.put(`/api/dashboard/goals/${goalId}`, goalData);
  }

  /**
   * Delete goal
   */
  async deleteGoal(goalId) {
    return await this.api.delete(`/api/dashboard/goals/${goalId}`);
  }

  /**
   * Get widget configuration
   */
  async getWidgetConfig() {
    return await this.api.get('/api/dashboard/widgets/config');
  }

  /**
   * Update widget configuration
   */
  async updateWidgetConfig(config) {
    return await this.api.put('/api/dashboard/widgets/config', config);
  }

  /**
   * Get custom reports
   */
  async getCustomReports() {
    return await this.api.get('/api/dashboard/reports');
  }

  /**
   * Create custom report
   */
  async createCustomReport(reportData) {
    return await this.api.post('/api/dashboard/reports', reportData);
  }

  /**
   * Update custom report
   */
  async updateCustomReport(reportId, reportData) {
    return await this.api.put(`/api/dashboard/reports/${reportId}`, reportData);
  }

  /**
   * Delete custom report
   */
  async deleteCustomReport(reportId) {
    return await this.api.delete(`/api/dashboard/reports/${reportId}`);
  }

  /**
   * Generate report
   */
  async generateReport(reportId, format = 'pdf') {
    return await this.api.get(`/api/dashboard/reports/${reportId}/generate?format=${format}`);
  }

  /**
   * Schedule report
   */
  async scheduleReport(reportId, schedule) {
    return await this.api.post(`/api/dashboard/reports/${reportId}/schedule`, schedule);
  }

  /**
   * Get scheduled reports
   */
  async getScheduledReports() {
    return await this.api.get('/api/dashboard/reports/scheduled');
  }

  /**
   * Cancel scheduled report
   */
  async cancelScheduledReport(scheduleId) {
    return await this.api.delete(`/api/dashboard/reports/scheduled/${scheduleId}`);
  }

  /**
   * Get export history
   */
  async getExportHistory(page = 1, limit = 20) {
    return await this.api.get(`/api/dashboard/exports?page=${page}&limit=${limit}`);
  }

  /**
   * Get team metrics
   */
  async getTeamMetrics(period = '30d') {
    return await this.api.get(`/api/dashboard/team-metrics?period=${period}`);
  }

  /**
   * Get team activity
   */
  async getTeamActivity(period = '30d') {
    return await this.api.get(`/api/dashboard/team-activity?period=${period}`);
  }

  /**
   * Get project metrics
   */
  async getProjectMetrics(projectId, period = '30d') {
    return await this.api.get(`/api/dashboard/project-metrics/${projectId}?period=${period}`);
  }

  /**
   * Get all projects metrics
   */
  async getAllProjectsMetrics(period = '30d') {
    return await this.api.get(`/api/dashboard/all-projects-metrics?period=${period}`);
  }

  /**
   * Get API usage metrics
   */
  async getAPIUsageMetrics(period = '30d') {
    return await this.api.get(`/api/dashboard/api-usage?period=${period}`);
  }

  /**
   * Get error logs
   */
  async getErrorLogs(page = 1, limit = 20, severity = 'all') {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      severity
    });

    return await this.api.get(`/api/dashboard/error-logs?${params}`);
  }

  /**
   * Get system status
   */
  async getSystemStatus() {
    return await this.api.get('/api/dashboard/system-status');
  }

  /**
   * Get backup status
   */
  async getBackupStatus() {
    return await this.api.get('/api/dashboard/backup-status');
  }

  /**
   * Get database metrics
   */
  async getDatabaseMetrics() {
    return await this.api.get('/api/dashboard/database-metrics');
  }

  /**
   * Get cache metrics
   */
  async getCacheMetrics() {
    return await this.api.get('/api/dashboard/cache-metrics');
  }

  /**
   * Clear cache
   */
  async clearCache(cacheType = 'all') {
    return await this.api.post('/api/dashboard/cache/clear', { type: cacheType });
  }

  /**
   * Get security metrics
   */
  async getSecurityMetrics(period = '30d') {
    return await this.api.get(`/api/dashboard/security-metrics?period=${period}`);
  }

  /**
   * Get audit logs
   */
  async getAuditLogs(page = 1, limit = 20, action = 'all') {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      action
    });

    return await this.api.get(`/api/dashboard/audit-logs?${params}`);
  }

  /**
   * Get real-time metrics
   */
  async getRealTimeMetrics() {
    return await this.api.get('/api/dashboard/realtime-metrics');
  }

  /**
   * Get dashboard settings
   */
  async getDashboardSettings() {
    return await this.api.get('/api/dashboard/settings');
  }

  /**
   * Update dashboard settings
   */
  async updateDashboardSettings(settings) {
    return await this.api.put('/api/dashboard/settings', settings);
  }

  /**
   * Reset dashboard settings
   */
  async resetDashboardSettings() {
    return await this.api.post('/api/dashboard/settings/reset');
  }

  /**
   * Get dashboard themes
   */
  async getDashboardThemes() {
    return await this.api.get('/api/dashboard/themes');
  }

  /**
   * Apply dashboard theme
   */
  async applyDashboardTheme(themeId) {
    return await this.api.put('/api/dashboard/themes/apply', { themeId });
  }

  /**
   * Create custom theme
   */
  async createCustomTheme(themeData) {
    return await this.api.post('/api/dashboard/themes/custom', themeData);
  }

  /**
   * Update custom theme
   */
  async updateCustomTheme(themeId, themeData) {
    return await this.api.put(`/api/dashboard/themes/custom/${themeId}`, themeData);
  }

  /**
   * Delete custom theme
   */
  async deleteCustomTheme(themeId) {
    return await this.api.delete(`/api/dashboard/themes/custom/${themeId}`);
  }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
  window.DashboardService = DashboardService;
  
  // Auto-initialize with global API client
  if (window.api) {
    window.dashboardService = new DashboardService(window.api);
  }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DashboardService;
}