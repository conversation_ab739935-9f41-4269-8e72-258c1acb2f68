const OpenAI = require('openai');

class OpenAIContentGenerator {
    constructor() {
        // Validate OpenAI API key
        if (!process.env.OPENAI_API_KEY) {
            throw new Error('OPENAI_API_KEY is required. Please add a valid OpenAI API key to your .env file.');
        }
        
        if (!this.isValidOpenAIKey(process.env.OPENAI_API_KEY)) {
            throw new Error('Invalid OpenAI API key format. Please check your OPENAI_API_KEY in .env file.');
        }

        this.client = new OpenAI({
            apiKey: process.env.OPENAI_API_KEY,
            organization: process.env.OPENAI_ORG_ID,
            project: process.env.OPENAI_PROJECT_ID,
        });
        
        this.defaultModel = process.env.OPENAI_DEFAULT_MODEL || 'gpt-4o-mini';
        this.premiumModel = process.env.OPENAI_PREMIUM_MODEL || 'gpt-4o';
        this.maxRetries = 3;
        
        console.log('✅ OpenAI Content Generator initialized with valid API key');
        
        // Content type configurations
        this.contentConfigs = {
            'blog-post': {
                model: 'gpt-4o-mini',
                temperature: 0.7,
                top_p: 0.9,
                frequency_penalty: 0.1,
                presence_penalty: 0.1,
                max_tokens: 4000,
                quality_threshold: 75
            },
            'article': {
                model: 'gpt-4o-mini',
                temperature: 0.6,
                top_p: 0.85,
                frequency_penalty: 0.15,
                presence_penalty: 0.1,
                max_tokens: 5000,
                quality_threshold: 80
            },
            'product-page': {
                model: 'gpt-4o-mini',
                temperature: 0.8,
                top_p: 0.95,
                frequency_penalty: 0.3,
                presence_penalty: 0.2,
                max_tokens: 2500,
                quality_threshold: 80
            },
            'landing-page': {
                model: 'gpt-4o',
                temperature: 0.6,
                top_p: 0.85,
                frequency_penalty: 0.15,
                presence_penalty: 0.15,
                max_tokens: 3000,
                quality_threshold: 90
            },
            'how-to-guide': {
                model: 'gpt-4o-mini',
                temperature: 0.5,
                top_p: 0.8,
                frequency_penalty: 0.2,
                presence_penalty: 0.1,
                max_tokens: 4500,
                quality_threshold: 85
            },
            'listicle': {
                model: 'gpt-4o-mini',
                temperature: 0.8,
                top_p: 0.9,
                frequency_penalty: 0.2,
                presence_penalty: 0.2,
                max_tokens: 3500,
                quality_threshold: 75
            },
            'review': {
                model: 'gpt-4o-mini',
                temperature: 0.7,
                top_p: 0.9,
                frequency_penalty: 0.25,
                presence_penalty: 0.15,
                max_tokens: 3000,
                quality_threshold: 80
            },
            'comparison': {
                model: 'gpt-4o',
                temperature: 0.5,
                top_p: 0.8,
                frequency_penalty: 0.2,
                presence_penalty: 0.1,
                max_tokens: 4000,
                quality_threshold: 85
            },
            'case-study': {
                model: 'gpt-4o',
                temperature: 0.4,
                top_p: 0.75,
                frequency_penalty: 0.15,
                presence_penalty: 0.1,
                max_tokens: 5000,
                quality_threshold: 90
            },
            'whitepaper': {
                model: 'gpt-4o',
                temperature: 0.3,
                top_p: 0.7,
                frequency_penalty: 0.1,
                presence_penalty: 0.05,
                max_tokens: 6000,
                quality_threshold: 95
            },
            'comprehensive_travel_guide': {
                model: 'gpt-4o',
                temperature: 0.7,
                top_p: 0.9,
                frequency_penalty: 0.15,
                presence_penalty: 0.1,
                max_tokens: 4500,
                quality_threshold: 88
            },
            'travel_guide': {
                model: 'gpt-4o-mini',
                temperature: 0.7,
                top_p: 0.9,
                frequency_penalty: 0.15,
                presence_penalty: 0.1,
                max_tokens: 4000,
                quality_threshold: 85
            },
            'category-page': {
                model: 'gpt-4o-mini',
                temperature: 0.6,
                top_p: 0.85,
                frequency_penalty: 0.2,
                presence_penalty: 0.15,
                max_tokens: 3000,
                quality_threshold: 80
            },
            'news-article': {
                model: 'gpt-4o-mini',
                temperature: 0.5,
                top_p: 0.8,
                frequency_penalty: 0.1,
                presence_penalty: 0.05,
                max_tokens: 3500,
                quality_threshold: 85
            }
        };

        // Industry-specific optimizations
        this.industryOptimizations = {
            'healthcare': {
                temperature_modifier: -0.2,
                additional_instructions: 'Ensure medical accuracy and include appropriate disclaimers',
                compliance_check: true
            },
            'finance': {
                temperature_modifier: -0.3,
                additional_instructions: 'Include financial disclaimers and regulatory compliance',
                compliance_check: true
            },
            'legal': {
                temperature_modifier: -0.3,
                additional_instructions: 'Ensure legal accuracy and include disclaimers',
                compliance_check: true
            },
            'technology': {
                temperature_modifier: 0.1,
                additional_instructions: 'Include technical specifications and comparisons',
                compliance_check: false
            },
            'ecommerce': {
                temperature_modifier: 0.2,
                additional_instructions: 'Focus on benefits, features, and conversion optimization',
                compliance_check: false
            },
            'education': {
                temperature_modifier: 0.0,
                additional_instructions: 'Ensure educational value and accuracy',
                compliance_check: false
            },
            'travel': {
                temperature_modifier: 0.15,
                additional_instructions: 'Include practical information and engaging descriptions',
                compliance_check: false
            },
            'food': {
                temperature_modifier: 0.2,
                additional_instructions: 'Include sensory descriptions and practical information',
                compliance_check: false
            }
        };
    }

    /**
     * Validate OpenAI API key format
     */
    isValidOpenAIKey(key) {
        return key && 
               key.startsWith('sk-') && 
               key.length > 40 && 
               !key.includes('your-') &&
               !key.includes('example') &&
               !key.includes('invalid');
    }

    async generateContent(params) {
        try {
            console.log('🤖 OpenAI Content Generator called!');
            
            // Handle different parameter formats (direct params or nested structure)
            const normalizedParams = await this.normalizeParameters(params);
            
            console.log('🤖 Starting OpenAI content generation with normalized params:', {
                keyword: normalizedParams.keyword,
                contentType: normalizedParams.contentType,
                wordCount: normalizedParams.wordCount,
                industry: normalizedParams.industry,
                targetCountry: normalizedParams.targetCountry
            });

            // Validate required parameters
            if (!normalizedParams.keyword) {
                throw new Error('Keyword is required for content generation');
            }

            // Get configuration for content type
            const config = this.getContentConfig(normalizedParams.contentType, normalizedParams.industry);
            
            // Build messages for chat completion
            const messages = this.buildMessages(normalizedParams, config);
            
            // Generate content with retry logic
            const result = await this.generateWithRetry(messages, config, normalizedParams);
            
            console.log('✅ OpenAI content generation successful');
            return {
                success: true,
                content: result.data.content,
                generatedContent: result.data.content,
                metadata: result.data,
                seoScore: result.data.quality_score,
                qualityScore: result.data.quality_score,
                generationType: 'openai'
            };
            
        } catch (error) {
            console.error('❌ OpenAI content generation failed:', error);
            return {
                success: false,
                error: error.message,
                content: '',
                generatedContent: '',
                generationType: 'openai'
            };
        }
    }

    // Normalize different parameter formats
    async normalizeParameters(params) {
        // Handle nested parameter structure from test
        if (params.contentSpecifications) {
            return {
                keyword: params.keyword,
                contentType: params.contentSpecifications.type || 'comprehensive_travel_guide',
                wordCount: params.contentSpecifications.length || params.seoRequirements?.targetLength || 2500,
                industry: this.extractIndustryFromKeyword(params.keyword),
                targetCountry: params.targetCountry,
                tone: params.contentSpecifications.tone || 'expert_authoritative',
                targetAudience: params.contentSpecifications.audience || 'USA_travelers',
                intent: 'informational',
                secondaryKeywords: await this.generateSecondaryKeywords(params.keyword),
                includeCurrentEvents: params.contentSpecifications.includeCurrentEvents || true,
                include2025Trends: params.contentSpecifications.include2025Trends || true,
                experienceLevel: params.contentSpecifications.experienceLevel || 'advanced',
                competitorData: params.competitorData
            };
        }
        
        // Handle direct parameter structure
        return {
            keyword: params.keyword,
            contentType: params.contentType || params.type || 'comprehensive_travel_guide', 
            wordCount: params.wordCount || params.length || 2500,
            industry: params.industry || this.extractIndustryFromKeyword(params.keyword),
            targetCountry: params.targetCountry || 'USA',
            tone: params.tone || 'expert_authoritative',
            targetAudience: params.targetAudience || 'general',
            intent: params.intent || 'informational',
            secondaryKeywords: params.secondaryKeywords,
            includeCurrentEvents: params.includeCurrentEvents !== false,
            include2025Trends: params.include2025Trends !== false,
            experienceLevel: params.experienceLevel || 'advanced'
        };
    }

    // Extract industry from keyword
    extractIndustryFromKeyword(keyword) {
        const keywordLower = keyword.toLowerCase();
        
        if (keywordLower.includes('tour') || keywordLower.includes('travel') || keywordLower.includes('vacation')) {
            return 'travel';
        } else if (keywordLower.includes('tech') || keywordLower.includes('software') || keywordLower.includes('app')) {
            return 'technology';
        } else if (keywordLower.includes('health') || keywordLower.includes('medical') || keywordLower.includes('fitness')) {
            return 'healthcare';
        } else if (keywordLower.includes('finance') || keywordLower.includes('money') || keywordLower.includes('investment')) {
            return 'finance';
        } else if (keywordLower.includes('marketing') || keywordLower.includes('seo') || keywordLower.includes('advertising')) {
            return 'marketing';
        } else {
            return 'general';
        }
    }

    // Generate secondary keywords based on main keyword using real-time analysis
    async generateSecondaryKeywords(keyword) {
        try {
            // Use OpenAI to generate related keywords based on the main keyword
            const response = await this.client.chat.completions.create({
                model: 'gpt-4o-mini',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a keyword research expert. Generate 5 closely related secondary keywords for SEO content optimization. Return only the keywords in a comma-separated list, no explanations.'
                    },
                    {
                        role: 'user',
                        content: `Generate 5 related secondary keywords for: "${keyword}"`
                    }
                ],
                temperature: 0.3,
                max_tokens: 100
            });

            const content = response.choices[0]?.message?.content?.trim();
            if (content) {
                const keywords = content.split(',').map(k => k.trim()).filter(k => k.length > 0);
                return keywords.slice(0, 5); // Ensure we have max 5 keywords
            }
        } catch (error) {
            console.warn('⚠️ Failed to generate secondary keywords:', error.message);
        }
        
        // Fallback to basic keyword variants if AI fails
        return this.generateBasicKeywordVariants(keyword);
    }

    // Generate basic keyword variants as fallback
    generateBasicKeywordVariants(keyword) {
        const keywordLower = keyword.toLowerCase();
        const variants = [];
        
        // Add basic variants
        variants.push(`${keyword} guide`);
        variants.push(`${keyword} tips`);
        variants.push(`best ${keyword}`);
        variants.push(`${keyword} advice`);
        variants.push(`${keyword} information`);
        
        return variants.slice(0, 5);
    }

    getContentConfig(contentType, industry) {
        // Get base configuration for content type
        const baseConfig = this.contentConfigs[contentType] || this.contentConfigs['blog-post'];
        
        // Apply industry-specific optimizations
        const industryOpt = this.industryOptimizations[industry] || {};
        
        return {
            ...baseConfig,
            temperature: Math.max(0.1, Math.min(1.0, baseConfig.temperature + (industryOpt.temperature_modifier || 0))),
            additional_instructions: industryOpt.additional_instructions || '',
            compliance_check: industryOpt.compliance_check || false
        };
    }

    buildMessages(params, config) {
        const systemPrompt = this.buildSystemPrompt(params, config);
        const userPrompt = this.buildUserPrompt(params);

        return [
            {
                role: "system",
                content: systemPrompt
            },
            {
                role: "user",
                content: userPrompt
            }
        ];
    }

    buildSystemPrompt(params, config) {
        const wordCountRange = this.getWordCountRange(params.wordCount);
        const targetWordCount = params.targetWordCount || this.extractWordCount(params.wordCount);
        
        return `You are an expert SEO content writer and digital marketing specialist with 10+ years of experience.

EXPERTISE AREAS:
- Search Engine Optimization (SEO)
- Content Marketing Strategy
- Keyword Research & Integration
- User Intent Analysis
- E-E-A-T (Experience, Expertise, Authoritativeness, Trustworthiness)

CONTENT REQUIREMENTS:
- Industry: ${params.industry || 'general'}
- Content Type: ${params.contentType || 'blog-post'}
- Target Audience: ${params.targetAudience || 'general'}
- Tone: ${params.tone || 'professional'}
- Word Count: EXACTLY ${targetWordCount} words (based on competitor analysis)
- Primary Keyword: "${params.keyword}"
- Secondary Keywords: ${params.secondaryKeywords || 'N/A'}

CRITICAL WORD COUNT REQUIREMENT:
- You MUST generate exactly ${targetWordCount} words
- This target is based on analysis of top 5 competitors
- Do NOT go significantly over or under this target
- If you're slightly short, add more examples, explanations, or supporting details
- If you're over, be more concise while maintaining quality

SEO OPTIMIZATION RULES:
1. Natural keyword integration (avoid keyword stuffing)
2. Semantic keyword variations and LSI keywords
3. Proper heading structure (H1, H2, H3)
4. Meta title and description optimization
5. Internal linking opportunities
6. Featured snippet optimization
7. User intent alignment (${params.intent || 'informational'})
8. Readability optimization (Flesch-Kincaid score 60+)

${params.headingStructure ? `HEADING STRUCTURE REQUIREMENTS:
- Use exactly ${params.headingStructure.h1 || 1} H1 heading
- Include ${params.headingStructure.h2 || 4} H2 headings
- Add ${params.headingStructure.h3 || 3} H3 headings
- Use ${params.headingStructure.h4 || 2} H4 headings if needed
` : ''}

${params.keywordStrategy ? `KEYWORD OPTIMIZATION TARGETS:
- Use "${params.keywordStrategy.primary}" ${params.keywordStrategy.recommendedOccurrences || 15} times (${params.keywordStrategy.targetDensity || 1.5}% density)
- Include keyword variations: ${params.keywordStrategy.variations ? params.keywordStrategy.variations.join(', ') : 'N/A'}
- Place keywords strategically in title, first paragraph, headings, and conclusion
` : ''}

${params.sectionStructure ? `CONTENT STRUCTURE:
- Organize into ${params.sectionStructure.recommended || 6} main sections
- Average ${params.sectionStructure.averageWordsPerSection || 200} words per section
- Follow this structure: ${params.sectionStructure.structure ? params.sectionStructure.structure.join(', ') : 'Introduction, Main topics, Conclusion'}
` : ''}

${params.competitorInsights ? `COMPETITIVE ADVANTAGES TO HIGHLIGHT:
${params.competitorInsights}
` : ''}

${params.additionalInstructions ? `OPTIMIZATION TARGETS:
${params.additionalInstructions}
` : ''}

${config.additional_instructions ? `INDUSTRY-SPECIFIC REQUIREMENTS:\n${config.additional_instructions}\n` : ''}

${config.compliance_check ? 'COMPLIANCE: Ensure all content meets industry regulations and includes appropriate disclaimers.\n' : ''}

OUTPUT FORMAT:
Return a JSON object with the following structure:
{
    "title": "SEO-optimized title (50-60 characters)",
    "meta_description": "Compelling meta description (150-160 characters)",
    "content": "Full article content with proper HTML structure including headings",
    "headings": ["H2 heading 1", "H2 heading 2", "H3 subheading"],
    "keywords_used": ["primary keyword", "secondary keyword 1"],
    "word_count": actual_word_count,
    "readability_score": estimated_score,
    "seo_recommendations": ["recommendation 1", "recommendation 2"],
    "internal_links": ["suggested internal link 1", "suggested internal link 2"],
    "call_to_action": "Compelling CTA for the content"
}

IMPORTANT: Ensure the content is comprehensive, engaging, and provides real value to readers while naturally incorporating the target keywords.`;
    }

    buildUserPrompt(params) {
        const targetWordCount = params.targetWordCount || this.extractWordCount(params.wordCount);
        
        let prompt = `Create high-quality SEO content for the keyword "${params.keyword}".`;
        
        // Emphasize word count requirement
        prompt += `\n\nCRITICAL WORD COUNT REQUIREMENT:\n- You MUST generate exactly ${targetWordCount} words\n- This target is based on analysis of top 5 competitors in search results\n- Content shorter than ${Math.round(targetWordCount * 0.9)} words will be rejected\n- Content longer than ${Math.round(targetWordCount * 1.1)} words will be rejected\n- Count every word in your response to ensure accuracy`;
        
        if (params.targetUrl) {
            prompt += `\n\nTARGET URL: ${params.targetUrl}`;
        }
        
        if (params.competitorAnalysis) {
            prompt += `\n\nCOMPETITOR ANALYSIS:\n${params.competitorAnalysis}`;
        }
        
        if (params.additionalInstructions) {
            prompt += `\n\nADDITIONAL REQUIREMENTS:\n${params.additionalInstructions}`;
        }
        
        if (params.competitorInsights) {
            prompt += `\n\nCOMPETITOR INSIGHTS:\n${params.competitorInsights}`;
        }
        
        if (params.optimizationTargets && params.optimizationTargets.length > 0) {
            prompt += `\n\nOPTIMIZATION TARGETS:\n${params.optimizationTargets.slice(0, 3).join('\n')}`;
        }
        
        prompt += `\n\nPlease create comprehensive, engaging, and SEO-optimized content that provides real value to readers while naturally incorporating the target keywords. Focus on user intent and ensure the content is authoritative and trustworthy.\n\nREMEMBER: The content must be exactly ${targetWordCount} words to compete effectively with top-ranking pages.`;
        
        return prompt;
    }

    getWordCountRange(wordCount) {
        if (typeof wordCount === 'string') {
            return wordCount;
        }
        
        const count = parseInt(wordCount) || 1000;
        
        if (count <= 500) return '300-500';
        if (count <= 800) return '500-800';
        if (count <= 1200) return '800-1200';
        if (count <= 2000) return '1200-2000';
        if (count <= 3000) return '2000-3000';
        return '3000+';
    }

    async generateWithRetry(messages, config, params, attempt = 1) {
        try {
            const completion = await this.client.chat.completions.create({
                model: config.model,
                messages: messages,
                temperature: config.temperature,
                top_p: config.top_p,
                frequency_penalty: config.frequency_penalty,
                presence_penalty: config.presence_penalty,
                max_tokens: config.max_tokens,
                response_format: { type: "json_object" },
                stream: false
            });

            const result = this.processResponse(completion, params, config);
            
            // Check if word count target is met and attempt optimization if not
            if (params.targetWordCount && result.data.word_count) {
                const wordCountAccuracy = this.calculateWordCountAccuracy(result.data.word_count, params.targetWordCount);
                console.log(`📊 Word count accuracy: ${wordCountAccuracy.toFixed(1)}% (${result.data.word_count}/${params.targetWordCount} words)`);
                
                // If word count is significantly off and we have retry attempts left, try to optimize
                if (wordCountAccuracy < 80 && attempt < this.maxRetries) {
                    console.log(`🔄 Word count below target (${wordCountAccuracy.toFixed(1)}%), attempting optimization...`);
                    const optimizedResult = await this.optimizeContentLength(result, params, config, attempt);
                    if (optimizedResult) {
                        return optimizedResult;
                    }
                }
            }
            
            return result;
            
        } catch (error) {
            if (this.isRetryableError(error) && attempt < this.maxRetries) {
                const delay = this.calculateBackoffDelay(attempt);
                console.log(`⏳ Retrying OpenAI request in ${delay}ms (attempt ${attempt + 1}/${this.maxRetries})`);
                await this.sleep(delay);
                return this.generateWithRetry(messages, config, params, attempt + 1);
            }
            throw error;
        }
    }

    processResponse(completion, params, config) {
        try {
            const response = completion.choices[0].message.content;
            const parsedContent = JSON.parse(response);
            
            // Validate required fields
            this.validateResponse(parsedContent);
            
            // Calculate quality score
            const qualityScore = this.assessContentQuality(parsedContent, params);
            
            return {
                success: true,
                data: {
                    title: parsedContent.title,
                    content: parsedContent.content,
                    meta_description: parsedContent.meta_description,
                    headings: parsedContent.headings || [],
                    keywords_used: parsedContent.keywords_used || [],
                    word_count: parsedContent.word_count,
                    readability_score: parsedContent.readability_score || 60,
                    seo_recommendations: parsedContent.seo_recommendations || [],
                    internal_links: parsedContent.internal_links || [],
                    call_to_action: parsedContent.call_to_action || '',
                    quality_score: qualityScore,
                    model_used: completion.model,
                    tokens_used: completion.usage,
                    generated_at: new Date().toISOString(),
                    parameters: params
                }
            };
        } catch (error) {
            console.error('Error parsing OpenAI response:', error);
            throw new Error('Failed to parse AI response. Please try again.');
        }
    }

    validateResponse(content) {
        const required = ['title', 'content', 'meta_description'];
        for (const field of required) {
            if (!content[field]) {
                throw new Error(`Missing required field: ${field}`);
            }
        }
    }

    assessContentQuality(content, params) {
        let score = 0;
        
        // Word count accuracy (30 points) - increased weight for dynamic optimization
        const targetWords = params.targetWordCount || this.extractWordCount(params.wordCount);
        const actualWords = content.word_count || 0;
        if (targetWords > 0) {
            const accuracy = this.calculateWordCountAccuracy(actualWords, targetWords) / 100;
            score += Math.max(0, accuracy * 30);
            
            // Bonus for hitting target exactly
            if (Math.abs(targetWords - actualWords) <= 25) {
                score += 5; // Bonus for very close word count
            }
        } else {
            score += 20; // Default if no target specified
        }
        
        // Content structure (25 points)
        if (content.headings && content.headings.length >= 3) score += 25;
        else if (content.headings && content.headings.length >= 1) score += 15;
        else score += 5;
        
        // SEO elements (25 points)
        if (content.meta_description && content.meta_description.length >= 120) score += 10;
        if (content.seo_recommendations && content.seo_recommendations.length >= 2) score += 10;
        if (content.keywords_used && content.keywords_used.length >= 1) score += 5;
        
        // Content quality (20 points) - reduced slightly to accommodate word count bonus
        if (content.readability_score >= 60) score += 12;
        if (content.call_to_action) score += 4;
        if (content.internal_links && content.internal_links.length >= 1) score += 4;
        
        return Math.round(score);
    }

    extractWordCount(wordCount) {
        if (typeof wordCount === 'number') return wordCount;
        if (typeof wordCount === 'string') {
            const match = wordCount.match(/(\d+)/);
            return match ? parseInt(match[1]) : 1000;
        }
        return 1000;
    }

    isRetryableError(error) {
        const retryableCodes = [429, 500, 502, 503, 504];
        return retryableCodes.includes(error.status) || 
               error.code === 'ECONNRESET' || 
               error.code === 'ETIMEDOUT';
    }

    calculateBackoffDelay(attempt) {
        return Math.min(1000 * Math.pow(2, attempt), 10000); // Exponential backoff, max 10s
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Calculate word count accuracy percentage
     * @param {number} actualWords - Actual word count
     * @param {number} targetWords - Target word count
     * @returns {number} Accuracy percentage
     */
    calculateWordCountAccuracy(actualWords, targetWords) {
        if (!targetWords || targetWords === 0) return 100;
        const accuracy = (actualWords / targetWords) * 100;
        // If content is longer than target, still consider it good if within 20% over
        return accuracy > 100 ? Math.max(80, 100 - ((accuracy - 100) * 2)) : accuracy;
    }

    /**
     * Optimize content length to meet word count targets
     * @param {object} result - Initial generation result
     * @param {object} params - Generation parameters
     * @param {object} config - Model configuration
     * @param {number} attempt - Current attempt number
     * @returns {object} Optimized result or null if optimization fails
     */
    async optimizeContentLength(result, params, config, attempt) {
        try {
            const currentWordCount = result.data.word_count;
            const targetWordCount = params.targetWordCount;
            const wordCountGap = targetWordCount - currentWordCount;
            
            console.log(`🎯 Optimizing content length: ${currentWordCount} → ${targetWordCount} (gap: ${wordCountGap > 0 ? '+' : ''}${wordCountGap})`);
            
            let optimizationPrompt;
            
            if (wordCountGap > 0) {
                // Need to expand content
                const expansionPercentage = (wordCountGap / currentWordCount) * 100;
                optimizationPrompt = `The current content has ${currentWordCount} words but needs to reach ${targetWordCount} words (${wordCountGap} more words needed, ${expansionPercentage.toFixed(1)}% expansion).

EXPANSION REQUIREMENTS:
- Add approximately ${wordCountGap} words to reach the target
- Expand existing sections with more detailed explanations
- Add practical examples and case studies
- Include additional subtopics and supporting information
- Maintain the same keyword density and SEO optimization
- Keep the same structure but make each section more comprehensive

Please rewrite the content to reach exactly ${targetWordCount} words while maintaining high quality and SEO optimization.`;
            } else {
                // Need to condense content
                const reductionPercentage = (Math.abs(wordCountGap) / currentWordCount) * 100;
                optimizationPrompt = `The current content has ${currentWordCount} words but needs to be reduced to ${targetWordCount} words (${Math.abs(wordCountGap)} words less, ${reductionPercentage.toFixed(1)}% reduction).

CONDENSATION REQUIREMENTS:
- Remove approximately ${Math.abs(wordCountGap)} words to reach the target
- Maintain all essential information and key points
- Combine similar sections for better flow
- Remove redundant explanations while keeping clarity
- Keep the same keyword density and SEO optimization
- Preserve the core structure and main headings

Please rewrite the content to reach exactly ${targetWordCount} words while maintaining high quality and SEO optimization.`;
            }
            
            const optimizationMessages = [
                {
                    role: "system",
                    content: this.buildSystemPrompt(params, config) + `\n\nCRITICAL: The content MUST be exactly ${targetWordCount} words. This is a strict requirement based on competitor analysis.`
                },
                {
                    role: "user",
                    content: `Here is the current content:\n\n${JSON.stringify(result.data, null, 2)}\n\n${optimizationPrompt}`
                }
            ];
            
            const optimizationCompletion = await this.client.chat.completions.create({
                model: config.model,
                messages: optimizationMessages,
                temperature: Math.max(0.1, config.temperature - 0.2), // Slightly lower temperature for more focused optimization
                top_p: config.top_p,
                frequency_penalty: config.frequency_penalty,
                presence_penalty: config.presence_penalty,
                max_tokens: config.max_tokens,
                response_format: { type: "json_object" },
                stream: false
            });
            
            const optimizedResult = this.processResponse(optimizationCompletion, params, config);
            
            // Check if optimization was successful
            const optimizedAccuracy = this.calculateWordCountAccuracy(optimizedResult.data.word_count, targetWordCount);
            console.log(`✅ Optimization result: ${optimizedAccuracy.toFixed(1)}% accuracy (${optimizedResult.data.word_count}/${targetWordCount} words)`);
            
            // If optimization improved accuracy significantly, use it
            if (optimizedAccuracy > this.calculateWordCountAccuracy(currentWordCount, targetWordCount)) {
                optimizedResult.data.optimization_applied = true;
                optimizedResult.data.optimization_attempt = attempt;
                optimizedResult.data.original_word_count = currentWordCount;
                return optimizedResult;
            }
            
            return null;
            
        } catch (error) {
            console.warn('⚠️ Content length optimization failed:', error.message);
            return null;
        }
    }

    /**
     * Enhanced word count extraction with better parsing
     * @param {string|number} wordCount - Word count input
     * @returns {number} Parsed word count
     */
    extractWordCount(wordCount) {
        if (typeof wordCount === 'number') return wordCount;
        if (typeof wordCount === 'string') {
            // Handle ranges like "1200-1500" - use the target (higher end)
            const rangeMatch = wordCount.match(/(\d+)-(\d+)/);
            if (rangeMatch) {
                return parseInt(rangeMatch[2]); // Use the higher end as target
            }
            // Handle single numbers
            const match = wordCount.match(/(\d+)/);
            return match ? parseInt(match[1]) : 1000;
        }
        return 1000;
    }
}

module.exports = OpenAIContentGenerator;
