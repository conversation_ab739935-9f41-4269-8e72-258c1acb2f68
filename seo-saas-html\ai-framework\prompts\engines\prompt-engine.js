/**
 * Prompt Engine - Dynamic Prompt Generation System
 * Generates optimized prompts based on context and templates
 */

const fs = require('fs').promises;
const path = require('path');
const Handlebars = require('handlebars');

class PromptEngine {
    constructor(options = {}) {
        this.templates = new Map();
        this.compiledTemplates = new Map();
        this.promptHistory = new Map();
        this.performanceMetrics = new Map();
        
        // Configuration
        this.config = {
            templatesPath: options.templatesPath || path.join(__dirname, '../templates'),
            enableCaching: options.enableCaching !== false,
            enableOptimization: options.enableOptimization !== false,
            maxHistorySize: options.maxHistorySize || 100,
            ...options
        };
        
        this.initialize();
    }
    
    /**
     * Initialize the prompt engine
     */
    async initialize() {
        try {
            // Register Handlebars helpers
            this.registerHandlebarsHelpers();
            
            // Load prompt templates
            await this.loadPromptTemplates();
            
            // Compile templates
            await this.compileTemplates();
            
            console.log('PromptEngine initialized successfully');
        } catch (error) {
            console.error('Failed to initialize PromptEngine:', error);
            throw error;
        }
    }
    
    /**
     * Register custom Handlebars helpers
     */
    registerHandlebarsHelpers() {
        // Conditional helper
        Handlebars.registerHelper('if', function(conditional, options) {
            if (conditional) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        
        // Each helper for arrays
        Handlebars.registerHelper('each', function(context, options) {
            let ret = '';
            if (context && context.length > 0) {
                for (let i = 0; i < context.length; i++) {
                    ret += options.fn({
                        ...context[i],
                        '@index': i,
                        '@first': i === 0,
                        '@last': i === context.length - 1
                    });
                }
            }
            return ret;
        });
        
        // Join array helper
        Handlebars.registerHelper('join', function(array, separator) {
            if (Array.isArray(array)) {
                return array.join(separator || ', ');
            }
            return '';
        });
        
        // Capitalize helper
        Handlebars.registerHelper('capitalize', function(str) {
            if (typeof str === 'string') {
                return str.charAt(0).toUpperCase() + str.slice(1);
            }
            return str;
        });
        
        // Truncate helper
        Handlebars.registerHelper('truncate', function(str, length) {
            if (typeof str === 'string' && str.length > length) {
                return str.substring(0, length) + '...';
            }
            return str;
        });
        
        // Number formatting helper
        Handlebars.registerHelper('number', function(num, decimals = 1) {
            if (typeof num === 'number') {
                return num.toFixed(decimals);
            }
            return num;
        });
    }
    
    /**
     * Load prompt templates from files
     */
    async loadPromptTemplates() {
        try {
            const templatesFile = path.join(this.config.templatesPath, 'seo-content-templates.json');
            const templatesContent = await fs.readFile(templatesFile, 'utf8');
            const templatesData = JSON.parse(templatesContent);
            
            // Store templates
            for (const [templateId, template] of Object.entries(templatesData.templates)) {
                this.templates.set(templateId, {
                    ...template,
                    id: templateId,
                    modifiers: templatesData.promptModifiers,
                    qualityStandards: templatesData.qualityStandards
                });
            }
            
            console.log(`Loaded ${this.templates.size} prompt templates`);
        } catch (error) {
            console.error('Failed to load prompt templates:', error);
            throw error;
        }
    }
    
    /**
     * Compile templates using Handlebars
     */
    async compileTemplates() {
        try {
            for (const [templateId, template] of this.templates) {
                // Compile system prompt
                if (template.systemPrompt) {
                    this.compiledTemplates.set(`${templateId}_system`, 
                        Handlebars.compile(template.systemPrompt)
                    );
                }
                
                // Compile user prompt template
                if (template.userPromptTemplate) {
                    this.compiledTemplates.set(`${templateId}_user`, 
                        Handlebars.compile(template.userPromptTemplate)
                    );
                }
            }
            
            console.log(`Compiled ${this.compiledTemplates.size} prompt templates`);
        } catch (error) {
            console.error('Failed to compile templates:', error);
            throw error;
        }
    }
    
    /**
     * Generate prompt from context
     */
    async generatePrompt(context) {
        try {
            const contentType = context.content?.type || 'blog_post';
            const template = this.templates.get(contentType);
            
            if (!template) {
                throw new Error(`Template not found for content type: ${contentType}`);
            }
            
            // Prepare template variables
            const templateVars = this.prepareTemplateVariables(context, template);
            
            // Generate system prompt
            const systemPromptCompiled = this.compiledTemplates.get(`${contentType}_system`);
            const systemPrompt = systemPromptCompiled ? systemPromptCompiled(templateVars) : template.systemPrompt;
            
            // Generate user prompt
            const userPromptCompiled = this.compiledTemplates.get(`${contentType}_user`);
            const userPrompt = userPromptCompiled(templateVars);
            
            // Apply optimizations if enabled
            let optimizedPrompt = {
                system: systemPrompt,
                user: userPrompt,
                template: template.id,
                variables: templateVars
            };
            
            if (this.config.enableOptimization) {
                optimizedPrompt = await this.optimizePrompt(optimizedPrompt, context);
            }
            
            // Store in history
            this.storePromptHistory(context.contextId, optimizedPrompt);
            
            return optimizedPrompt;
        } catch (error) {
            console.error('Failed to generate prompt:', error);
            throw error;
        }
    }
    
    /**
     * Prepare template variables from context
     */
    prepareTemplateVariables(context, template) {
        const vars = {
            // Content variables
            contentType: context.content?.type || 'blog post',
            primaryKeyword: context.content?.primaryKeyword || '',
            secondaryKeywords: this.formatSecondaryKeywords(context.content?.secondaryKeywords),
            wordCount: context.content?.wordCount || 1000,
            intent: context.content?.intent || 'informational',
            
            // User and project variables
            targetLocation: context.seo?.targetLocation || 'Global',
            industry: context.project?.industry || 'general',
            targetAudience: context.project?.targetAudience || 'general audience',
            tone: context.project?.brandVoice?.tone || context.user?.preferences?.defaultTone || 'professional',
            
            // SEO variables
            keywordDensity: context.seo?.optimization?.keywordDensity?.target || 2.0,
            eeat: context.seo?.optimization?.eeat || {},
            
            // Structure variables
            structure: context.content?.structure || {},
            
            // Competitor analysis
            competitorAnalysis: context.seo?.competitorAnalysis || null,
            
            // Additional instructions
            additionalInstructions: context.content?.additionalInstructions || 'Create high-quality, engaging content that provides value to readers.'
        };
        
        // Apply tone modifiers
        if (template.modifiers?.tone_modifiers?.[vars.tone]) {
            vars.toneModifier = template.modifiers.tone_modifiers[vars.tone];
        }
        
        // Apply industry modifiers
        if (template.modifiers?.industry_modifiers?.[vars.industry]) {
            vars.industryModifier = template.modifiers.industry_modifiers[vars.industry];
        }
        
        // Apply intent modifiers
        if (template.modifiers?.intent_modifiers?.[vars.intent]) {
            vars.intentModifier = template.modifiers.intent_modifiers[vars.intent];
        }
        
        return vars;
    }
    
    /**
     * Format secondary keywords for template
     */
    formatSecondaryKeywords(keywords) {
        if (!keywords || !Array.isArray(keywords)) {
            return 'None specified';
        }
        
        if (keywords.length === 0) {
            return 'None specified';
        }
        
        return keywords.join(', ');
    }
    
    /**
     * Optimize prompt based on performance data
     */
    async optimizePrompt(prompt, context) {
        try {
            const contextId = context.contextId;
            const history = this.promptHistory.get(contextId) || [];
            const metrics = this.performanceMetrics.get(contextId);
            
            let optimizedPrompt = { ...prompt };
            
            // Apply optimization strategies based on performance
            if (metrics) {
                // If response time is too high, simplify prompt
                if (metrics.responseTime > 5000) {
                    optimizedPrompt = this.simplifyPrompt(optimizedPrompt);
                }
                
                // If quality score is low, enhance prompt
                if (metrics.qualityScore < 0.7) {
                    optimizedPrompt = this.enhancePrompt(optimizedPrompt, context);
                }
                
                // If keyword density is off, adjust instructions
                if (metrics.keywordDensity && (metrics.keywordDensity < 1.0 || metrics.keywordDensity > 3.0)) {
                    optimizedPrompt = this.adjustKeywordInstructions(optimizedPrompt, metrics.keywordDensity);
                }
            }
            
            // Apply A/B testing variations if available
            if (history.length > 0) {
                optimizedPrompt = this.applyABTestingVariations(optimizedPrompt, history);
            }
            
            return optimizedPrompt;
        } catch (error) {
            console.error('Prompt optimization error:', error);
            return prompt;
        }
    }
    
    /**
     * Simplify prompt for better performance
     */
    simplifyPrompt(prompt) {
        const simplified = { ...prompt };
        
        // Reduce system prompt complexity
        simplified.system = simplified.system.replace(/\n\n/g, '\n').trim();
        
        // Simplify user prompt by removing optional sections
        simplified.user = simplified.user
            .replace(/\*\*Additional Instructions:\*\*[\s\S]*?(?=\*\*|$)/g, '')
            .replace(/\n{3,}/g, '\n\n')
            .trim();
        
        return simplified;
    }
    
    /**
     * Enhance prompt for better quality
     */
    enhancePrompt(prompt, context) {
        const enhanced = { ...prompt };
        
        // Add quality enhancement instructions
        const qualityEnhancement = `\n\n**Quality Enhancement Instructions:**
- Ensure exceptional content quality and depth
- Include specific examples and actionable insights
- Use data-driven information where possible
- Create engaging, reader-focused content
- Optimize for both search engines and user experience`;
        
        enhanced.user += qualityEnhancement;
        
        return enhanced;
    }
    
    /**
     * Adjust keyword density instructions
     */
    adjustKeywordInstructions(prompt, currentDensity) {
        const adjusted = { ...prompt };
        
        let instruction = '';
        if (currentDensity < 1.0) {
            instruction = '\n\n**IMPORTANT: Increase keyword usage naturally throughout the content. Current density is too low.**';
        } else if (currentDensity > 3.0) {
            instruction = '\n\n**IMPORTANT: Reduce keyword repetition. Focus on natural language and semantic variations.**';
        }
        
        adjusted.user += instruction;
        
        return adjusted;
    }
    
    /**
     * Apply A/B testing variations
     */
    applyABTestingVariations(prompt, history) {
        // Simple A/B testing logic - can be expanded
        const variation = Math.random() < 0.5 ? 'A' : 'B';
        
        if (variation === 'B') {
            // Apply variation B modifications
            prompt.user = prompt.user.replace(
                'Create a comprehensive',
                'Develop an in-depth and engaging'
            );
        }
        
        prompt.variation = variation;
        
        return prompt;
    }
    
    /**
     * Store prompt in history
     */
    storePromptHistory(contextId, prompt) {
        if (!this.promptHistory.has(contextId)) {
            this.promptHistory.set(contextId, []);
        }
        
        const history = this.promptHistory.get(contextId);
        history.push({
            timestamp: new Date().toISOString(),
            prompt: prompt,
            template: prompt.template
        });
        
        // Limit history size
        if (history.length > this.config.maxHistorySize) {
            history.shift();
        }
    }
    
    /**
     * Update performance metrics for optimization
     */
    updatePerformanceMetrics(contextId, metrics) {
        this.performanceMetrics.set(contextId, {
            ...this.performanceMetrics.get(contextId),
            ...metrics,
            timestamp: new Date().toISOString()
        });
    }
    
    /**
     * Get available templates
     */
    getAvailableTemplates() {
        const templates = [];
        for (const [id, template] of this.templates) {
            templates.push({
                id,
                name: template.name,
                description: template.description,
                category: template.category,
                variables: template.variables
            });
        }
        return templates;
    }
    
    /**
     * Get template by ID
     */
    getTemplate(templateId) {
        return this.templates.get(templateId);
    }
    
    /**
     * Get prompt history for context
     */
    getPromptHistory(contextId) {
        return this.promptHistory.get(contextId) || [];
    }
    
    /**
     * Get engine statistics
     */
    getEngineStats() {
        return {
            totalTemplates: this.templates.size,
            compiledTemplates: this.compiledTemplates.size,
            contextHistories: this.promptHistory.size,
            performanceMetrics: this.performanceMetrics.size
        };
    }
    
    /**
     * Clear history for context
     */
    clearHistory(contextId) {
        this.promptHistory.delete(contextId);
        this.performanceMetrics.delete(contextId);
    }
}

module.exports = PromptEngine;
