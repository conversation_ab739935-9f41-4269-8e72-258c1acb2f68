export = LeadingStar;
declare class LeadingStar extends BasePlugin {
    /** @param {import('postcss').Result=} result */
    constructor(result?: import('postcss').Result | undefined);
    /**
     * @param {import('postcss').Declaration | import('postcss').AtRule} node
     * @return {void}
     */
    detect(node: import('postcss').Declaration | import('postcss').AtRule): void;
}
import BasePlugin = require("../plugin");
//# sourceMappingURL=leadingStar.d.ts.map