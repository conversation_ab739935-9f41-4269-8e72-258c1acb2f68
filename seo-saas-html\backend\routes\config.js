const express = require('express');
const router = express.Router();

/**
 * Configuration Status Endpoint
 * Returns the status of all required API configurations
 */
router.get('/status', (req, res) => {
  try {
    const config = {
      openai: {
        configured: !!process.env.OPENAI_API_KEY,
        valid: process.env.OPENAI_API_KEY && 
               process.env.OPENAI_API_KEY.startsWith('sk-') && 
               process.env.OPENAI_API_KEY.length > 40 &&
               !process.env.OPENAI_API_KEY.includes('your-') &&
               !process.env.OPENAI_API_KEY.includes('example'),
        models: {
          default: process.env.OPENAI_DEFAULT_MODEL || 'gpt-4o-mini',
          premium: process.env.OPENAI_PREMIUM_MODEL || 'gpt-4o'
        }
      },
      serper: {
        configured: !!process.env.SERPER_API_KEY,
        valid: process.env.SERPER_API_KEY && process.env.SERPER_API_KEY.length > 20
      },
      firecrawl: {
        configured: !!process.env.FIRECRAWL_API_KEY,
        valid: process.env.FIRECRAWL_API_KEY && process.env.FIRECRAWL_API_KEY.startsWith('fc-')
      },
      supabase: {
        configured: !!(process.env.SUPABASE_URL && process.env.SUPABASE_SERVICE_KEY),
        valid: process.env.SUPABASE_URL && 
               process.env.SUPABASE_URL.includes('supabase.co') &&
               process.env.SUPABASE_SERVICE_KEY && 
               process.env.SUPABASE_SERVICE_KEY.length > 50
      }
    };

    const overallStatus = {
      ready: config.openai.valid && config.serper.valid && config.firecrawl.valid && config.supabase.valid,
      canGenerateContent: config.openai.valid,
      canAnalyzeCompetitors: config.firecrawl.valid && config.serper.valid,
      canStoreData: config.supabase.valid
    };

    const missingConfigurations = [];
    if (!config.openai.valid) missingConfigurations.push('OpenAI API Key');
    if (!config.serper.valid) missingConfigurations.push('Serper API Key');
    if (!config.firecrawl.valid) missingConfigurations.push('Firecrawl API Key');
    if (!config.supabase.valid) missingConfigurations.push('Supabase Configuration');

    res.json({
      success: true,
      data: {
        ...config,
        overall: overallStatus,
        missing: missingConfigurations,
        setupInstructions: missingConfigurations.length > 0 ? {
          message: 'Please configure the missing API keys to enable all features',
          documentation: '/backend/OPENAI_SETUP.md',
          requiredActions: missingConfigurations.map(item => {
            switch (item) {
              case 'OpenAI API Key':
                return 'Add OPENAI_API_KEY to .env file (get from https://platform.openai.com/api-keys)';
              case 'Serper API Key':
                return 'Add SERPER_API_KEY to .env file (get from https://serper.dev)';
              case 'Firecrawl API Key':
                return 'Add FIRECRAWL_API_KEY to .env file (get from https://firecrawl.dev)';
              case 'Supabase Configuration':
                return 'Add SUPABASE_URL and SUPABASE_SERVICE_KEY to .env file';
              default:
                return `Configure ${item}`;
            }
          })
        } : null
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Configuration status check failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check configuration status',
      details: error.message
    });
  }
});

/**
 * OpenAI Validation Endpoint
 * Tests if the OpenAI API key is working
 */
router.post('/validate-openai', async (req, res) => {
  try {
    if (!process.env.OPENAI_API_KEY) {
      return res.status(400).json({
        success: false,
        error: 'OpenAI API key not configured',
        action: 'Add OPENAI_API_KEY to your .env file'
      });
    }

    const OpenAI = require('openai');
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    // Test with a simple completion
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'user',
          content: 'Say "API test successful" in exactly 3 words.'
        }
      ],
      max_tokens: 10,
      temperature: 0
    });

    const response = completion.choices[0]?.message?.content;
    
    res.json({
      success: true,
      message: 'OpenAI API key is valid and working',
      data: {
        model: 'gpt-4o-mini',
        test_response: response,
        usage: completion.usage
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ OpenAI validation failed:', error);
    
    let errorMessage = 'OpenAI API validation failed';
    let actionRequired = 'Check your OpenAI API key configuration';
    
    if (error.code === 'invalid_api_key') {
      errorMessage = 'Invalid OpenAI API key';
      actionRequired = 'Please check your OPENAI_API_KEY in .env file';
    } else if (error.code === 'insufficient_quota') {
      errorMessage = 'OpenAI quota exceeded';
      actionRequired = 'Add payment method to your OpenAI account';
    } else if (error.code === 'rate_limit_exceeded') {
      errorMessage = 'OpenAI rate limit exceeded';
      actionRequired = 'Wait a moment and try again';
    }

    res.status(400).json({
      success: false,
      error: errorMessage,
      action: actionRequired,
      details: error.message,
      code: error.code || 'unknown'
    });
  }
});

module.exports = router;