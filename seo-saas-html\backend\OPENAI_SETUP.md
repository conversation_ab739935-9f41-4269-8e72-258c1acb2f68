# OpenAI API Configuration Setup

## Required: Valid OpenAI API Key

The SEO SAAS application requires a **valid OpenAI API key** to generate high-quality, SEO-optimized content using real AI models.

### Step 1: Get OpenAI API Key

1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Sign up or log in to your account
3. Navigate to **API Keys** section
4. Click **"Create new secret key"**
5. Copy the generated key (starts with `sk-...`)

### Step 2: Configure Environment

1. Open `.env` file in the backend directory
2. Replace the placeholder with your real API key:

```env
# Replace this line:
# OPENAI_API_KEY=your-valid-openai-api-key-here

# With your actual key:
OPENAI_API_KEY=sk-your-actual-openai-api-key-here
```

### Step 3: Optional Organization Settings

If you have an OpenAI organization, add these:

```env
OPENAI_ORG_ID=org-your-organization-id
OPENAI_PROJECT_ID=proj_your-project-id
```

### Step 4: Restart Server

After updating the `.env` file:
```bash
# Kill existing server
pkill -f "node server.js"

# Start server again
node server.js
```

## Important Notes

- **No Mock Data**: This application only uses real OpenAI models and real research data
- **No Fallbacks**: Only OpenAI is used for AI generation (no Groq or other providers)
- **Deep Research**: All content is based on comprehensive competitor analysis using Firecrawl
- **Real SERP Data**: Uses Serper API for actual search results and keyword research

## Supported Models

- **Default**: `gpt-4o-mini` (faster, cost-effective)
- **Premium**: `gpt-4o` (highest quality for important content)

## Troubleshooting

### Error: "OPENAI_API_KEY is required"
- Add a valid OpenAI API key to your `.env` file

### Error: "Invalid OpenAI API key format"
- Ensure your key starts with `sk-` and is the complete key
- Remove any quotes or extra spaces

### Error: "Insufficient quota"
- Check your OpenAI billing and usage limits
- Add payment method to your OpenAI account if needed

### Error: "Model not found"
- Ensure you have access to the models (gpt-4o, gpt-4o-mini)
- Check your OpenAI plan and model permissions

## Cost Considerations

- **gpt-4o-mini**: ~$0.15 per 1M input tokens, ~$0.60 per 1M output tokens
- **gpt-4o**: ~$5.00 per 1M input tokens, ~$15.00 per 1M output tokens
- Typical blog post: ~1,000-3,000 tokens ($0.01-0.05 for mini, $0.10-0.30 for premium)

## Security

- Never commit your API key to version control
- Keep your `.env` file secure
- Monitor your OpenAI usage regularly
- Rotate keys periodically for security

## API Key Format Example

Valid format:
```
sk-proj-1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890
```

The application will validate your key format and reject invalid or placeholder keys.