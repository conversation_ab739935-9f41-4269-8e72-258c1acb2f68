const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

async function createTestUser() {
  try {
    // Hash password
    const password_hash = await bcrypt.hash('demo123', 10);
    
    // Create test user
    const { data: user, error } = await supabase
      .from('users')
      .upsert({
        email: '<EMAIL>',
        password_hash,
        subscription_tier: 'pro',
        api_usage_count: 0,
        api_usage_limit: 1000,
        last_login: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'email'
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating test user:', error);
    } else {
      console.log('Test user created successfully:', {
        email: '<EMAIL>',
        password: 'demo123',
        subscription: 'pro'
      });
    }
  } catch (error) {
    console.error('Failed to create test user:', error);
  }
}

createTestUser();