<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - SEO Pro</title>
    <meta name="description" content="Manage your SEO content generation, analyze performance, and track your ranking improvements with SEO Pro dashboard.">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <!-- Skip to Content Link -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <!-- Header -->
    <header class="header header-dashboard">
        <div class="header-container">
            <a href="/" class="header-brand">
                <img src="assets/images/logo.svg" alt="SEO Pro Logo" class="header-logo">
                <span>SEO Pro</span>
            </a>
            
            <div class="header-search">
                <div class="search-container">
                    <input type="search" class="search-input" placeholder="Search projects, content, or keywords...">
                    <button class="search-button" aria-label="Search">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>
            </div>
            
            <div class="header-actions">
                <button class="header-action-btn" aria-label="Notifications">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18 8A6 6 0 0 0 6 8C6 15 3 17 3 17H21C21 17 18 15 18 8Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M13.73 21A2 2 0 0 1 10.27 21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <span class="notification-badge">3</span>
                </button>
                
                <div class="user-menu">
                    <button class="user-menu-trigger" aria-label="User menu">
                        <img src="assets/images/avatar-placeholder.jpg" alt="User Avatar" class="user-avatar">
                        <span class="user-name">John Doe</span>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <polyline points="6,9 12,15 18,9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>
            </div>
            
            <button class="header-mobile-toggle" aria-label="Toggle mobile menu">
                <span class="header-mobile-toggle-line"></span>
                <span class="header-mobile-toggle-line"></span>
                <span class="header-mobile-toggle-line"></span>
            </button>
        </div>
    </header>
    
    <!-- Sidebar -->
    <aside class="sidebar">
        <nav class="sidebar-nav">
            <ul class="sidebar-nav-list">
                <li class="sidebar-nav-item">
                    <a href="dashboard.html" class="sidebar-nav-link active">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="sidebar-nav-item">
                    <a href="content-generator.html" class="sidebar-nav-link">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14 2H6A2 2 0 0 0 4 4V20A2 2 0 0 0 6 22H18A2 2 0 0 0 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>Content Generator</span>
                    </a>
                </li>
                <li class="sidebar-nav-item">
                    <a href="content-editor.html" class="sidebar-nav-link">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M11 4H4A2 2 0 0 0 2 6V18A2 2 0 0 0 4 20H16A2 2 0 0 0 18 18V13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M18.5 2.5A2.121 2.121 0 0 1 21 4.621L20.621 5L18 7.621L15.379 5L16 4.379A2.121 2.121 0 0 1 18.5 2.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M15.379 5L8 12.379V15H10.621L18 7.621L15.379 5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>Content Editor</span>
                    </a>
                </li>
                <li class="sidebar-nav-item">
                    <a href="seo-analyzer.html" class="sidebar-nav-link">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 14C19 18.4183 15.4183 22 11 22C6.58172 22 3 18.4183 3 14C3 9.58172 6.58172 6 11 6C15.4183 6 19 9.58172 19 14Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M21 2L16.5 7.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M15 8L18 11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>SEO Analyzer</span>
                    </a>
                </li>
                <li class="sidebar-nav-item">
                    <a href="competitor-analysis.html" class="sidebar-nav-link">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <line x1="18" y1="20" x2="18" y2="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <line x1="12" y1="20" x2="12" y2="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <line x1="6" y1="20" x2="6" y2="14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>Competitor Analysis</span>
                    </a>
                </li>
                <li class="sidebar-nav-item">
                    <a href="bulk-operations.html" class="sidebar-nav-link">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M2 3H8A4 4 0 0 1 12 7V21A2 2 0 0 1 10 21H4A2 2 0 0 1 2 19V3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M22 3H16A4 4 0 0 0 12 7V21A2 2 0 0 0 14 21H20A2 2 0 0 0 22 19V3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>Bulk Operations</span>
                    </a>
                </li>
                <li class="sidebar-nav-item">
                    <a href="projects.html" class="sidebar-nav-link">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M22 19A2 2 0 0 1 20 21H4A2 2 0 0 1 2 19V5A2 2 0 0 1 4 3H9L11 6H20A2 2 0 0 1 22 8V19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>Projects</span>
                    </a>
                </li>
                <li class="sidebar-nav-item">
                    <a href="analytics.html" class="sidebar-nav-link">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <polyline points="22,12 18,12 15,21 9,3 6,12 2,12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>Analytics</span>
                    </a>
                </li>
                <li class="sidebar-nav-item">
                    <a href="settings.html" class="sidebar-nav-link">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M19.4 15A1.65 1.65 0 0 0 21 13.09A1.65 1.65 0 0 0 19.4 9L18.85 8.87A4.48 4.48 0 0 0 18.24 7.16L18.24 7.16A4.48 4.48 0 0 0 17.16 6.24L17.13 5.6A1.65 1.65 0 0 0 13.91 3A1.65 1.65 0 0 0 12 4.6L11.87 5.15A4.48 4.48 0 0 0 10.16 5.76L10.16 5.76A4.48 4.48 0 0 0 9.24 6.84L8.6 6.87A1.65 1.65 0 0 0 3 8.09A1.65 1.65 0 0 0 4.6 12L5.15 12.13A4.48 4.48 0 0 0 5.76 13.84L5.76 13.84A4.48 4.48 0 0 0 6.84 14.76L6.87 15.4A1.65 1.65 0 0 0 9 21A1.65 1.65 0 0 0 12 19.4L12.13 18.85A4.48 4.48 0 0 0 13.84 18.24L13.84 18.24A4.48 4.48 0 0 0 14.76 17.16L15.4 17.13A1.65 1.65 0 0 0 21 15.91Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span>Settings</span>
                    </a>
                </li>
            </ul>
        </nav>
        
        <div class="sidebar-footer">
            <div class="usage-indicator">
                <div class="usage-header">
                    <span class="usage-label">Content Generation</span>
                    <span class="usage-count">87/500</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 17.4%"></div>
                </div>
                <a href="#upgrade" class="upgrade-link">Upgrade Plan</a>
            </div>
        </div>
    </aside>
    
    <!-- Main Content -->
    <main id="main-content" class="app-content">
        <div class="dashboard-container">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <div class="dashboard-title-section">
                    <h1 class="dashboard-title">Dashboard</h1>
                    <p class="dashboard-subtitle">Welcome back! Here's what's happening with your SEO content.</p>
                </div>
                <div class="dashboard-actions">
                    <button class="btn btn-outline">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 15V19A2 2 0 0 1 19 21H5A2 2 0 0 1 3 19V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Export Report
                    </button>
                    <a href="content-generator.html" class="btn btn-primary">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        Create Content
                    </a>
                </div>
            </div>
            
            <!-- Key Metrics -->
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon metric-icon-primary">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M14 2H6A2 2 0 0 0 4 4V20A2 2 0 0 0 6 22H18A2 2 0 0 0 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="metric-trend metric-trend-up">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <polyline points="23,6 13.5,15.5 8.5,10.5 1,18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <polyline points="17,6 23,6 23,12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>+15%</span>
                        </div>
                    </div>
                    <div class="metric-content">
                        <div class="metric-value" data-metric="content-count">247</div>
                        <div class="metric-label">Content Pieces</div>
                        <div class="metric-description">Generated this month</div>
                        <div class="metric-change">+12%</div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon metric-icon-success">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <polyline points="22,12 18,12 15,21 9,3 6,12 2,12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="metric-trend metric-trend-up">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <polyline points="23,6 13.5,15.5 8.5,10.5 1,18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <polyline points="17,6 23,6 23,12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>+28%</span>
                        </div>
                    </div>
                    <div class="metric-content">
                        <div class="metric-value" data-metric="seo-score">94.2%</div>
                        <div class="metric-label">Avg SEO Score</div>
                        <div class="metric-description">Content optimization rating</div>
                        <div class="metric-change">+5%</div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon metric-icon-warning">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M19 14C19 18.4183 15.4183 22 11 22C6.58172 22 3 18.4183 3 14C3 9.58172 6.58172 6 11 6C15.4183 6 19 9.58172 19 14Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M21 2L16.5 7.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="metric-trend metric-trend-up">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <polyline points="23,6 13.5,15.5 8.5,10.5 1,18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <polyline points="17,6 23,6 23,12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>+42%</span>
                        </div>
                    </div>
                    <div class="metric-content">
                        <div class="metric-value" data-metric="keywords">1,847</div>
                        <div class="metric-label">Keywords Tracked</div>
                        <div class="metric-description">Across all projects</div>
                        <div class="metric-change">+23%</div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon metric-icon-info">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <line x1="18" y1="20" x2="18" y2="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <line x1="12" y1="20" x2="12" y2="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <line x1="6" y1="20" x2="6" y2="14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="metric-trend metric-trend-down">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <polyline points="23,18 13.5,8.5 8.5,13.5 1,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <polyline points="17,18 23,18 23,12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>-3.2</span>
                        </div>
                    </div>
                    <div class="metric-content">
                        <div class="metric-value" data-metric="traffic">12.4</div>
                        <div class="metric-label">Avg Position</div>
                        <div class="metric-description">Search ranking average</div>
                        <div class="metric-change">+18%</div>
                    </div>
                </div>
            </div>
            
            <!-- Charts and Recent Activity -->
            <div class="dashboard-grid">
                <!-- Performance Chart -->
                <div class="dashboard-card chart-card">
                    <div class="card-header">
                        <h3 class="card-title">Performance Overview</h3>
                        <div class="card-actions">
                            <select class="select-input select-sm">
                                <option>Last 30 days</option>
                                <option>Last 7 days</option>
                                <option>Last 90 days</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="chart-container">
                            <canvas id="analytics-chart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- Performance Chart -->
                <div class="dashboard-card chart-card">
                    <div class="card-header">
                        <h3 class="card-title">SEO Performance</h3>
                    </div>
                    <div class="card-content">
                        <div class="chart-container">
                            <canvas id="performance-chart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Content -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Recent Content</h3>
                        <a href="content-generator.html" class="card-action-link">View All</a>
                    </div>
                    <div class="card-content">
                        <div class="content-list recent-activity-list">
                            <div class="content-item">
                                <div class="content-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14 2H6A2 2 0 0 0 4 4V20A2 2 0 0 0 6 22H18A2 2 0 0 0 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="content-details">
                                    <div class="content-title">Ultimate Guide to Local SEO</div>
                                    <div class="content-meta">
                                        <span class="content-date">2 hours ago</span>
                                        <span class="content-status status-published">Published</span>
                                    </div>
                                </div>
                                <div class="content-score">
                                    <span class="score-value">96</span>
                                    <span class="score-label">SEO</span>
                                </div>
                            </div>
                            
                            <div class="content-item">
                                <div class="content-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14 2H6A2 2 0 0 0 4 4V20A2 2 0 0 0 6 22H18A2 2 0 0 0 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="content-details">
                                    <div class="content-title">E-commerce SEO Best Practices</div>
                                    <div class="content-meta">
                                        <span class="content-date">5 hours ago</span>
                                        <span class="content-status status-draft">Draft</span>
                                    </div>
                                </div>
                                <div class="content-score">
                                    <span class="score-value">89</span>
                                    <span class="score-label">SEO</span>
                                </div>
                            </div>
                            
                            <div class="content-item">
                                <div class="content-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14 2H6A2 2 0 0 0 4 4V20A2 2 0 0 0 6 22H18A2 2 0 0 0 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="content-details">
                                    <div class="content-title">Technical SEO Audit Checklist</div>
                                    <div class="content-meta">
                                        <span class="content-date">1 day ago</span>
                                        <span class="content-status status-published">Published</span>
                                    </div>
                                </div>
                                <div class="content-score">
                                    <span class="score-value">92</span>
                                    <span class="score-label">SEO</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Top Keywords -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Top Performing Keywords</h3>
                        <a href="seo-analyzer.html" class="card-action-link">View All</a>
                    </div>
                    <div class="card-content">
                        <div class="keyword-list">
                            <div class="keyword-item">
                                <div class="keyword-details">
                                    <div class="keyword-term">local seo optimization</div>
                                    <div class="keyword-volume">12K monthly searches</div>
                                </div>
                                <div class="keyword-rank">
                                    <span class="rank-position">#3</span>
                                    <span class="rank-change rank-up">+2</span>
                                </div>
                            </div>
                            
                            <div class="keyword-item">
                                <div class="keyword-details">
                                    <div class="keyword-term">seo content strategy</div>
                                    <div class="keyword-volume">8.5K monthly searches</div>
                                </div>
                                <div class="keyword-rank">
                                    <span class="rank-position">#7</span>
                                    <span class="rank-change rank-up">+5</span>
                                </div>
                            </div>
                            
                            <div class="keyword-item">
                                <div class="keyword-details">
                                    <div class="keyword-term">technical seo audit</div>
                                    <div class="keyword-volume">6.2K monthly searches</div>
                                </div>
                                <div class="keyword-rank">
                                    <span class="rank-position">#12</span>
                                    <span class="rank-change rank-down">-1</span>
                                </div>
                            </div>
                            
                            <div class="keyword-item">
                                <div class="keyword-details">
                                    <div class="keyword-term">ecommerce seo tips</div>
                                    <div class="keyword-volume">4.8K monthly searches</div>
                                </div>
                                <div class="keyword-rank">
                                    <span class="rank-position">#5</span>
                                    <span class="rank-change rank-same">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Project Activity -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3 class="card-title">Project Activity</h3>
                        <a href="projects.html" class="card-action-link">View All</a>
                    </div>
                    <div class="card-content">
                        <div class="activity-list notifications-list">
                            <div class="activity-item">
                                <div class="activity-icon activity-icon-success">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="activity-details">
                                    <div class="activity-title">Content published in E-commerce Project</div>
                                    <div class="activity-time">2 hours ago</div>
                                </div>
                            </div>
                            
                            <div class="activity-item">
                                <div class="activity-icon activity-icon-info">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="activity-details">
                                    <div class="activity-title">SEO analysis completed for Local Business</div>
                                    <div class="activity-time">5 hours ago</div>
                                </div>
                            </div>
                            
                            <div class="activity-item">
                                <div class="activity-icon activity-icon-warning">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M10.29 3.86L1.82 18A2 2 0 0 0 3.64 21H20.36A2 2 0 0 0 22.18 18L13.71 3.86A2 2 0 0 0 10.29 3.86Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <line x1="12" y1="9" x2="12" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="activity-details">
                                    <div class="activity-title">Competitor analysis needs review</div>
                                    <div class="activity-time">1 day ago</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="scripts/theme-toggle.js"></script>
    <script src="scripts/api-client.js"></script>
    <script src="scripts/websocket-client.js"></script>
    <script src="scripts/services/content-service.js"></script>
    <script src="scripts/services/seo-service.js"></script>
    <script src="scripts/services/dashboard-service.js"></script>
    <script src="scripts/main.js"></script>
    <script src="scripts/app.js"></script>
    <script src="scripts/dashboard.js"></script>
</body>
</html>