/**
 * Dashboard Page JavaScript
 * Handles dashboard-specific functionality and data visualization
 */

class DashboardManager {
  constructor() {
    this.charts = {};
    this.refreshInterval = null;
    this.isLoading = false;
    this.data = {
      overview: null,
      analytics: null,
      recentActivity: [],
      notifications: []
    };
    
    this.init();
  }

  /**
   * Initialize dashboard
   */
  async init() {
    try {
      // Show loading state
      this.showLoading(true);
      
      // Wait for app to be ready
      if (!window.app || !window.app.isInitialized) {
        console.log('Waiting for app initialization...');
        setTimeout(() => this.init(), 100);
        return;
      }
      
      // Initialize UI components
      this.initializeUI();
      
      // Load dashboard data
      await this.loadDashboardData();
      
      // Setup refresh interval
      this.setupAutoRefresh();
      
      // Initialize charts
      this.initializeCharts();
      
      // Setup event listeners
      this.setupEventListeners();
      
      console.log('Dashboard initialized successfully');
    } catch (error) {
      console.error('Dashboard initialization error:', error);
      this.showError('Failed to load dashboard. Please refresh the page.');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * Initialize UI components
   */
  initializeUI() {
    // Add dashboard-specific UI elements
    this.elements = {
      contentCount: document.querySelector('[data-metric="content-count"]'),
      seoScore: document.querySelector('[data-metric="seo-score"]'),
      keywords: document.querySelector('[data-metric="keywords"]'),
      traffic: document.querySelector('[data-metric="traffic"]'),
      recentActivity: document.querySelector('.recent-activity-list'),
      notifications: document.querySelector('.notifications-list'),
      analyticsChart: document.querySelector('#analytics-chart'),
      performanceChart: document.querySelector('#performance-chart')
    };
  }

  /**
   * Load dashboard data
   */
  async loadDashboardData() {
    try {
      // First check API configuration
      await this.checkAPIConfiguration();
      
      // Check if we have dashboard service
      if (!window.app || !window.app.dashboardService) {
        console.warn('Dashboard service not available, using demo data');
        this.loadDemoData();
        return;
      }

      // Load data in parallel
      const [overview, analytics, notifications] = await Promise.all([
        this.loadOverview(),
        this.loadAnalytics(),
        this.loadNotifications()
      ]);

      // Update UI with loaded data
      this.updateDashboard(overview, analytics, notifications);
      
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // Load demo data as fallback
      this.loadDemoData();
    }
  }

  /**
   * Load overview data
   */
  async loadOverview() {
    try {
      const overview = await window.app.dashboardService.getOverview();
      this.data.overview = overview;
      return overview;
    } catch (error) {
      console.error('Error loading overview:', error);
      return this.getDemoOverview();
    }
  }

  /**
   * Load analytics data
   */
  async loadAnalytics() {
    try {
      const analytics = await window.app.dashboardService.getAnalytics();
      this.data.analytics = analytics;
      return analytics;
    } catch (error) {
      console.error('Error loading analytics:', error);
      return this.getDemoAnalytics();
    }
  }

  /**
   * Load notifications
   */
  async loadNotifications() {
    try {
      const notifications = await window.app.dashboardService.getNotifications();
      this.data.notifications = notifications;
      return notifications;
    } catch (error) {
      console.error('Error loading notifications:', error);
      return this.getDemoNotifications();
    }
  }

  /**
   * Load demo data for development/testing
   */
  loadDemoData() {
    const overview = this.getDemoOverview();
    const analytics = this.getDemoAnalytics();
    const notifications = this.getDemoNotifications();
    
    this.updateDashboard(overview, analytics, notifications);
  }

  /**
   * Get demo overview data
   */
  getDemoOverview() {
    return {
      contentCount: 156,
      seoScore: 87,
      keywords: 1243,
      traffic: '12.5K',
      contentGrowth: '+12%',
      seoImprovement: '+5%',
      keywordGrowth: '+23%',
      trafficGrowth: '+18%'
    };
  }

  /**
   * Get demo analytics data
   */
  getDemoAnalytics() {
    return {
      contentGeneration: {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        data: [12, 19, 15, 25, 22, 30, 28]
      },
      seoPerformance: {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
        data: [75, 78, 82, 87]
      },
      topKeywords: [
        { keyword: 'SEO optimization', volume: 5400, difficulty: 'Medium' },
        { keyword: 'Content marketing', volume: 4200, difficulty: 'Low' },
        { keyword: 'Digital marketing', volume: 8100, difficulty: 'High' },
        { keyword: 'Blog writing', volume: 2900, difficulty: 'Low' },
        { keyword: 'SEO tools', volume: 6300, difficulty: 'Medium' }
      ],
      recentActivity: [
        { action: 'Content Generated', title: 'Ultimate SEO Guide 2025', time: '5 minutes ago' },
        { action: 'SEO Analysis', title: 'Homepage Optimization', time: '1 hour ago' },
        { action: 'Keyword Research', title: 'E-commerce Keywords', time: '3 hours ago' },
        { action: 'Competitor Analysis', title: 'Top 5 Competitors', time: '5 hours ago' },
        { action: 'Content Updated', title: 'Blog Post Optimization', time: '1 day ago' }
      ]
    };
  }

  /**
   * Get demo notifications
   */
  getDemoNotifications() {
    return [
      {
        type: 'success',
        message: 'Content generation completed successfully',
        time: '2 minutes ago'
      },
      {
        type: 'info',
        message: 'New SEO recommendations available',
        time: '1 hour ago'
      },
      {
        type: 'warning',
        message: 'API usage at 80% of monthly limit',
        time: '3 hours ago'
      }
    ];
  }

  /**
   * Update dashboard UI
   */
  updateDashboard(overview, analytics, notifications) {
    // Update overview metrics
    if (overview && this.elements.contentCount) {
      this.updateMetrics(overview);
    }
    
    // Update recent activity
    if (analytics && analytics.recentActivity) {
      this.updateRecentActivity(analytics.recentActivity);
    }
    
    // Update notifications
    if (notifications) {
      this.updateNotifications(notifications);
    }
    
    // Update charts
    if (analytics) {
      this.updateCharts(analytics);
    }
  }

  /**
   * Update metric displays
   */
  updateMetrics(overview) {
    // Update content count
    if (this.elements.contentCount) {
      this.elements.contentCount.textContent = overview.contentCount || '0';
      const growthElement = this.elements.contentCount.parentElement.querySelector('.metric-change');
      if (growthElement) {
        growthElement.textContent = overview.contentGrowth || '+0%';
      }
    }
    
    // Update SEO score
    if (this.elements.seoScore) {
      this.elements.seoScore.textContent = overview.seoScore || '0';
      const growthElement = this.elements.seoScore.parentElement.querySelector('.metric-change');
      if (growthElement) {
        growthElement.textContent = overview.seoImprovement || '+0%';
      }
    }
    
    // Update keywords
    if (this.elements.keywords) {
      this.elements.keywords.textContent = overview.keywords || '0';
      const growthElement = this.elements.keywords.parentElement.querySelector('.metric-change');
      if (growthElement) {
        growthElement.textContent = overview.keywordGrowth || '+0%';
      }
    }
    
    // Update traffic
    if (this.elements.traffic) {
      this.elements.traffic.textContent = overview.traffic || '0';
      const growthElement = this.elements.traffic.parentElement.querySelector('.metric-change');
      if (growthElement) {
        growthElement.textContent = overview.trafficGrowth || '+0%';
      }
    }
  }

  /**
   * Update recent activity list
   */
  updateRecentActivity(activities) {
    if (!this.elements.recentActivity) return;
    
    this.elements.recentActivity.innerHTML = activities.map(activity => `
      <div class="activity-item">
        <div class="activity-icon">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="activity-content">
          <div class="activity-title">${activity.action}</div>
          <div class="activity-description">${activity.title}</div>
        </div>
        <div class="activity-time">${activity.time}</div>
      </div>
    `).join('');
  }

  /**
   * Update notifications
   */
  updateNotifications(notifications) {
    if (!this.elements.notifications) return;
    
    this.elements.notifications.innerHTML = notifications.map(notification => `
      <div class="notification-item ${notification.type}">
        <div class="notification-icon">
          ${this.getNotificationIcon(notification.type)}
        </div>
        <div class="notification-content">
          <div class="notification-message">${notification.message}</div>
          <div class="notification-time">${notification.time}</div>
        </div>
      </div>
    `).join('');
  }

  /**
   * Get notification icon based on type
   */
  getNotificationIcon(type) {
    const icons = {
      success: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>',
      info: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/><line x1="12" y1="16" x2="12" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/><line x1="12" y1="8" x2="12.01" y2="8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/></svg>',
      warning: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10.29 3.86L1.82 18C1.64539 18.3024 1.55296 18.6453 1.55199 18.9945C1.55101 19.3437 1.64151 19.6871 1.81442 19.9905C1.98734 20.2939 2.23673 20.5467 2.53771 20.7238C2.83868 20.901 3.18058 20.9962 3.53 21H20.47C20.8194 20.9962 21.1613 20.901 21.4623 20.7238C21.7633 20.5467 22.0127 20.2939 22.1856 19.9905C22.3585 19.6871 22.449 19.3437 22.448 18.9945C22.447 18.6453 22.3546 18.3024 22.18 18L13.71 3.86C13.5318 3.56611 13.2807 3.32313 12.9812 3.15449C12.6817 2.98585 12.3438 2.89726 12 2.89726C11.6562 2.89726 11.3183 2.98585 11.0188 3.15449C10.7193 3.32313 10.4682 3.56611 10.29 3.86Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><line x1="12" y1="9" x2="12" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round"/><line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round"/></svg>',
      error: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/><line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/><line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/></svg>'
    };
    
    return icons[type] || icons.info;
  }

  /**
   * Initialize charts
   */
  initializeCharts() {
    // Check if Chart.js is available
    if (typeof Chart === 'undefined') {
      console.warn('Chart.js not loaded, skipping chart initialization');
      return;
    }
    
    // Initialize analytics chart
    if (this.elements.analyticsChart) {
      this.initializeAnalyticsChart();
    }
    
    // Initialize performance chart
    if (this.elements.performanceChart) {
      this.initializePerformanceChart();
    }
  }

  /**
   * Initialize analytics chart
   */
  initializeAnalyticsChart() {
    const ctx = this.elements.analyticsChart.getContext('2d');
    this.charts.analytics = new Chart(ctx, {
      type: 'line',
      data: {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [{
          label: 'Content Generated',
          data: [12, 19, 15, 25, 22, 30, 28],
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  }

  /**
   * Initialize performance chart
   */
  initializePerformanceChart() {
    const ctx = this.elements.performanceChart.getContext('2d');
    this.charts.performance = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: ['Excellent', 'Good', 'Needs Improvement'],
        datasets: [{
          data: [65, 25, 10],
          backgroundColor: [
            'rgb(34, 197, 94)',
            'rgb(59, 130, 246)',
            'rgb(239, 68, 68)'
          ]
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    });
  }

  /**
   * Update charts with new data
   */
  updateCharts(analytics) {
    // Update analytics chart
    if (this.charts.analytics && analytics.contentGeneration) {
      this.charts.analytics.data.labels = analytics.contentGeneration.labels;
      this.charts.analytics.data.datasets[0].data = analytics.contentGeneration.data;
      this.charts.analytics.update();
    }
    
    // Update performance chart if data available
    if (this.charts.performance && analytics.seoPerformance) {
      // Convert SEO performance to chart data
      const latestScore = analytics.seoPerformance.data[analytics.seoPerformance.data.length - 1];
      const excellent = latestScore >= 80 ? latestScore : 0;
      const good = latestScore >= 60 && latestScore < 80 ? latestScore : 0;
      const needsImprovement = latestScore < 60 ? latestScore : 0;
      
      this.charts.performance.data.datasets[0].data = [excellent, good, needsImprovement];
      this.charts.performance.update();
    }
  }

  /**
   * Setup auto refresh
   */
  setupAutoRefresh() {
    // Refresh every 30 seconds
    this.refreshInterval = setInterval(() => {
      this.refreshDashboard();
    }, 30000);
  }

  /**
   * Refresh dashboard data
   */
  async refreshDashboard() {
    if (this.isLoading) return;
    
    try {
      await this.loadDashboardData();
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
    }
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Listen for content generation events
    if (window.wsClient) {
      window.wsClient.on('content_generation_complete', (data) => {
        this.handleContentGenerationComplete(data);
      });
      
      window.wsClient.on('dashboard_update', (data) => {
        this.handleDashboardUpdate(data);
      });
    }
    
    // Listen for app events
    if (window.app) {
      window.app.on('content:generated', () => {
        this.refreshDashboard();
      });
    }
    
    // Handle visibility change
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.pauseRefresh();
      } else {
        this.resumeRefresh();
      }
    });
  }

  /**
   * Handle content generation complete
   */
  handleContentGenerationComplete(data) {
    // Show notification
    if (window.app) {
      window.app.showNotification('Content generation completed!', 'success');
    }
    
    // Refresh dashboard
    this.refreshDashboard();
  }

  /**
   * Handle dashboard update from WebSocket
   */
  handleDashboardUpdate(data) {
    if (data.metrics) {
      this.updateMetrics(data.metrics);
    }
  }

  /**
   * Pause auto refresh
   */
  pauseRefresh() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }
  }

  /**
   * Resume auto refresh
   */
  resumeRefresh() {
    if (!this.refreshInterval) {
      this.setupAutoRefresh();
      this.refreshDashboard();
    }
  }

  /**
   * Show loading state
   */
  showLoading(isLoading) {
    this.isLoading = isLoading;
    
    // Update UI loading state
    const loader = document.querySelector('.dashboard-loader');
    if (loader) {
      loader.style.display = isLoading ? 'block' : 'none';
    }
    
    // Add loading class to dashboard
    const dashboard = document.querySelector('.dashboard-container');
    if (dashboard) {
      if (isLoading) {
        dashboard.classList.add('loading');
      } else {
        dashboard.classList.remove('loading');
      }
    }
  }

  /**
   * Show error message
   */
  showError(message) {
    const errorContainer = document.querySelector('.dashboard-error');
    if (errorContainer) {
      errorContainer.textContent = message;
      errorContainer.style.display = 'block';
    } else {
      // Create error element if it doesn't exist
      const dashboard = document.querySelector('.dashboard-container');
      if (dashboard) {
        const error = document.createElement('div');
        error.className = 'dashboard-error';
        error.textContent = message;
        dashboard.insertBefore(error, dashboard.firstChild);
      }
    }
  }

  /**
   * Check API configuration status
   */
  async checkAPIConfiguration() {
    try {
      const response = await fetch('/api/config/status');
      const result = await response.json();
      
      if (result.success && result.data) {
        const config = result.data;
        
        // Show configuration warnings if needed
        if (!config.overall.canGenerateContent) {
          this.showConfigurationWarning('OpenAI API key is required for content generation', config.missing);
        }
        
        // Store config status globally
        window.APP_CONFIG = window.APP_CONFIG || {};
        window.APP_CONFIG.API_STATUS = config;
        
        return config;
      }
    } catch (error) {
      console.warn('Could not check API configuration:', error);
    }
  }

  /**
   * Show configuration warning to user
   */
  showConfigurationWarning(message, missingItems) {
    const warningHtml = `
      <div class="config-warning" style="
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 16px;
        margin: 16px 0;
        color: #856404;
      ">
        <div style="display: flex; align-items: center; margin-bottom: 8px;">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" style="margin-right: 8px;">
            <path d="M10.29 3.86L1.82 18A2 2 0 0 0 3.64 21H20.36A2 2 0 0 0 22.18 18L13.71 3.86A2 2 0 0 0 10.29 3.86Z"/>
            <line x1="12" y1="9" x2="12" y2="13"/>
            <line x1="12" y1="17" x2="12.01" y2="17"/>
          </svg>
          <strong>Configuration Required</strong>
        </div>
        <p style="margin: 0 0 8px 0;">${message}</p>
        <div style="font-size: 14px;">
          <strong>Missing:</strong> ${missingItems.join(', ')}
          <br>
          <a href="/backend/OPENAI_SETUP.md" target="_blank" style="color: #856404; text-decoration: underline;">
            View Setup Instructions
          </a>
        </div>
      </div>
    `;
    
    const dashboard = document.querySelector('.dashboard-container');
    if (dashboard) {
      // Remove existing warning
      const existingWarning = dashboard.querySelector('.config-warning');
      if (existingWarning) existingWarning.remove();
      
      // Add new warning at the top
      dashboard.insertAdjacentHTML('afterbegin', warningHtml);
    }
  }

  /**
   * Cleanup on destroy
   */
  destroy() {
    // Clear refresh interval
    this.pauseRefresh();
    
    // Destroy charts
    Object.values(this.charts).forEach(chart => {
      if (chart) chart.destroy();
    });
    
    // Clear data
    this.data = {
      overview: null,
      analytics: null,
      recentActivity: [],
      notifications: []
    };
  }
}

// Initialize dashboard when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.dashboardManager = new DashboardManager();
  });
} else {
  window.dashboardManager = new DashboardManager();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DashboardManager;
}