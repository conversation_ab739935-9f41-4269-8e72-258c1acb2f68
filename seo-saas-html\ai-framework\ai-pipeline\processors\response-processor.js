/**
 * Response Processor - AI Response Processing and Validation System
 * Processes, validates, and enhances AI responses for optimal quality
 */

class ResponseProcessor {
    constructor(options = {}) {
        this.processors = new Map();
        this.validators = new Map();
        this.enhancers = new Map();
        
        // Configuration
        this.config = {
            enableValidation: options.enableValidation !== false,
            enableEnhancement: options.enableEnhancement !== false,
            enablePostProcessing: options.enablePostProcessing !== false,
            qualityThreshold: options.qualityThreshold || 0.8,
            ...options
        };
        
        this.initialize();
    }
    
    /**
     * Initialize response processor
     */
    async initialize() {
        try {
            // Initialize content processors
            this.initializeProcessors();
            
            // Initialize validators
            this.initializeValidators();
            
            // Initialize enhancers
            this.initializeEnhancers();
            
            console.log('ResponseProcessor initialized successfully');
        } catch (error) {
            console.error('Failed to initialize ResponseProcessor:', error);
            throw error;
        }
    }
    
    /**
     * Initialize content processors
     */
    initializeProcessors() {
        // Blog post processor
        this.processors.set('blog_post', {
            name: 'Blog Post Processor',
            process: (content, context) => {
                return this.processBlogPost(content, context);
            }
        });
        
        // Product description processor
        this.processors.set('product_description', {
            name: 'Product Description Processor',
            process: (content, context) => {
                return this.processProductDescription(content, context);
            }
        });
        
        // Meta description processor
        this.processors.set('meta_description', {
            name: 'Meta Description Processor',
            process: (content, context) => {
                return this.processMetaDescription(content, context);
            }
        });
        
        // Landing page processor
        this.processors.set('landing_page', {
            name: 'Landing Page Processor',
            process: (content, context) => {
                return this.processLandingPage(content, context);
            }
        });
    }
    
    /**
     * Initialize validators
     */
    initializeValidators() {
        // Content length validator
        this.validators.set('content_length', {
            name: 'Content Length Validator',
            validate: (content, context) => {
                const wordCount = this.countWords(content);
                const targetWordCount = context.content?.wordCount || 1000;
                const tolerance = 0.2; // 20% tolerance
                
                const minWords = targetWordCount * (1 - tolerance);
                const maxWords = targetWordCount * (1 + tolerance);
                
                if (wordCount < minWords) {
                    return {
                        valid: false,
                        message: `Content too short: ${wordCount} words (target: ${targetWordCount})`,
                        severity: 'warning'
                    };
                }
                
                if (wordCount > maxWords) {
                    return {
                        valid: false,
                        message: `Content too long: ${wordCount} words (target: ${targetWordCount})`,
                        severity: 'warning'
                    };
                }
                
                return { valid: true };
            }
        });
        
        // Keyword density validator
        this.validators.set('keyword_density', {
            name: 'Keyword Density Validator',
            validate: (content, context) => {
                const primaryKeyword = context.content?.primaryKeyword;
                if (!primaryKeyword) return { valid: true };
                
                const density = this.calculateKeywordDensity(content, primaryKeyword);
                const targetDensity = context.seo?.optimization?.keywordDensity?.target || 2.0;
                const tolerance = 0.5;
                
                if (density < targetDensity - tolerance || density > targetDensity + tolerance) {
                    return {
                        valid: false,
                        message: `Keyword density ${density.toFixed(1)}% (target: ${targetDensity}%)`,
                        severity: 'warning'
                    };
                }
                
                return { valid: true };
            }
        });
        
        // Heading structure validator
        this.validators.set('heading_structure', {
            name: 'Heading Structure Validator',
            validate: (content, context) => {
                const headings = this.extractHeadings(content);
                
                // Check for H1
                const h1Count = headings.filter(h => h.level === 1).length;
                if (h1Count !== 1) {
                    return {
                        valid: false,
                        message: `Invalid H1 count: ${h1Count} (should be 1)`,
                        severity: 'error'
                    };
                }
                
                // Check for proper hierarchy
                let previousLevel = 0;
                for (const heading of headings) {
                    if (heading.level > previousLevel + 1) {
                        return {
                            valid: false,
                            message: `Heading hierarchy skip: H${previousLevel} to H${heading.level}`,
                            severity: 'warning'
                        };
                    }
                    previousLevel = heading.level;
                }
                
                return { valid: true };
            }
        });
        
        // SEO compliance validator
        this.validators.set('seo_compliance', {
            name: 'SEO Compliance Validator',
            validate: (content, context) => {
                const issues = [];
                
                // Check for meta description
                if (!content.includes('meta description') && context.content?.type !== 'meta_description') {
                    issues.push('Missing meta description');
                }
                
                // Check for internal/external links
                const linkCount = (content.match(/\[.*?\]\(.*?\)/g) || []).length;
                if (linkCount < 2) {
                    issues.push('Insufficient links (internal/external)');
                }
                
                // Check for call-to-action
                const ctaKeywords = ['click', 'learn more', 'get started', 'contact', 'buy', 'download'];
                const hasCTA = ctaKeywords.some(keyword => 
                    content.toLowerCase().includes(keyword.toLowerCase())
                );
                
                if (!hasCTA) {
                    issues.push('Missing call-to-action');
                }
                
                if (issues.length > 0) {
                    return {
                        valid: false,
                        message: `SEO issues: ${issues.join(', ')}`,
                        severity: 'warning'
                    };
                }
                
                return { valid: true };
            }
        });
    }
    
    /**
     * Initialize enhancers
     */
    initializeEnhancers() {
        // Readability enhancer
        this.enhancers.set('readability', {
            name: 'Readability Enhancer',
            enhance: (content, context) => {
                return this.enhanceReadability(content);
            }
        });
        
        // SEO enhancer
        this.enhancers.set('seo', {
            name: 'SEO Enhancer',
            enhance: (content, context) => {
                return this.enhanceSEO(content, context);
            }
        });
        
        // Formatting enhancer
        this.enhancers.set('formatting', {
            name: 'Formatting Enhancer',
            enhance: (content, context) => {
                return this.enhanceFormatting(content);
            }
        });
    }
    
    /**
     * Process AI response
     */
    async processResponse(response, context) {
        try {
            let processedContent = response.result.content;
            const contentType = context.content?.type || 'blog_post';
            
            // Apply content-specific processing
            if (this.processors.has(contentType)) {
                const processor = this.processors.get(contentType);
                processedContent = processor.process(processedContent, context);
            }
            
            // Validate content if enabled
            const validationResults = [];
            if (this.config.enableValidation) {
                for (const [validatorId, validator] of this.validators) {
                    const result = validator.validate(processedContent, context);
                    if (!result.valid) {
                        validationResults.push({
                            validator: validator.name,
                            message: result.message,
                            severity: result.severity
                        });
                    }
                }
            }
            
            // Enhance content if enabled
            if (this.config.enableEnhancement) {
                for (const [enhancerId, enhancer] of this.enhancers) {
                    processedContent = enhancer.enhance(processedContent, context);
                }
            }
            
            // Calculate quality metrics
            const qualityMetrics = this.calculateQualityMetrics(processedContent, context);
            
            // Post-processing
            if (this.config.enablePostProcessing) {
                processedContent = this.postProcess(processedContent, context);
            }
            
            return {
                content: processedContent,
                originalContent: response.result.content,
                usage: response.result.usage,
                cost: response.result.cost,
                model: response.result.model,
                provider: response.providerId,
                validation: {
                    passed: validationResults.length === 0,
                    issues: validationResults
                },
                quality: qualityMetrics,
                metadata: {
                    wordCount: this.countWords(processedContent),
                    readabilityScore: this.calculateReadabilityScore(processedContent),
                    keywordDensity: this.calculateKeywordDensity(
                        processedContent, 
                        context.content?.primaryKeyword
                    ),
                    headingCount: this.extractHeadings(processedContent).length,
                    linkCount: (processedContent.match(/\[.*?\]\(.*?\)/g) || []).length
                },
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('Response processing error:', error);
            throw error;
        }
    }
    
    /**
     * Process blog post content
     */
    processBlogPost(content, context) {
        // Ensure proper structure
        let processed = content;
        
        // Add meta description if missing
        if (!processed.includes('**Meta Description:**')) {
            const metaDesc = this.generateMetaDescription(processed, context);
            processed = `**Meta Description:** ${metaDesc}\n\n${processed}`;
        }
        
        // Ensure proper heading hierarchy
        processed = this.fixHeadingHierarchy(processed);
        
        return processed;
    }
    
    /**
     * Process product description content
     */
    processProductDescription(content, context) {
        let processed = content;
        
        // Ensure key product information is highlighted
        processed = this.highlightProductFeatures(processed);
        
        // Add structured data suggestions
        processed += '\n\n**Structured Data Suggestions:**\n- Product schema markup\n- Review schema\n- Price schema';
        
        return processed;
    }
    
    /**
     * Process meta description content
     */
    processMetaDescription(content, context) {
        let processed = content.trim();
        
        // Ensure character limit
        if (processed.length > 160) {
            processed = processed.substring(0, 157) + '...';
        }
        
        // Ensure primary keyword is included
        const primaryKeyword = context.content?.primaryKeyword;
        if (primaryKeyword && !processed.toLowerCase().includes(primaryKeyword.toLowerCase())) {
            // Try to incorporate keyword naturally
            processed = `${primaryKeyword} - ${processed}`;
            if (processed.length > 160) {
                processed = processed.substring(0, 157) + '...';
            }
        }
        
        return processed;
    }
    
    /**
     * Process landing page content
     */
    processLandingPage(content, context) {
        let processed = content;
        
        // Ensure CTA is prominent
        processed = this.enhanceCallToAction(processed);
        
        // Add conversion tracking suggestions
        processed += '\n\n**Conversion Tracking:**\n- Add Google Analytics events\n- Implement conversion pixels\n- Set up A/B testing';
        
        return processed;
    }
    
    /**
     * Enhance readability
     */
    enhanceReadability(content) {
        let enhanced = content;
        
        // Break up long paragraphs
        enhanced = enhanced.replace(/([.!?])\s+([A-Z])/g, '$1\n\n$2');
        
        // Add bullet points for lists
        enhanced = enhanced.replace(/(\d+\.\s)/g, '\n$1');
        
        // Improve sentence flow
        enhanced = enhanced.replace(/\.\s+However,/g, '. However,');
        enhanced = enhanced.replace(/\.\s+Additionally,/g, '. Additionally,');
        
        return enhanced;
    }
    
    /**
     * Enhance SEO
     */
    enhanceSEO(content, context) {
        let enhanced = content;
        const primaryKeyword = context.content?.primaryKeyword;
        
        if (primaryKeyword) {
            // Ensure keyword appears in first paragraph
            const firstParagraph = enhanced.split('\n\n')[0];
            if (!firstParagraph.toLowerCase().includes(primaryKeyword.toLowerCase())) {
                enhanced = enhanced.replace(
                    /^([^.]+\.)/,
                    `$1 This comprehensive guide about ${primaryKeyword} will help you understand the key concepts.`
                );
            }
        }
        
        return enhanced;
    }
    
    /**
     * Enhance formatting
     */
    enhanceFormatting(content) {
        let enhanced = content;
        
        // Ensure consistent heading format
        enhanced = enhanced.replace(/^#+\s*/gm, (match) => {
            return match.replace(/#+/, (hashes) => '**H' + hashes.length + ':**');
        });
        
        // Format lists consistently
        enhanced = enhanced.replace(/^\*\s+/gm, '• ');
        enhanced = enhanced.replace(/^-\s+/gm, '• ');
        
        return enhanced;
    }
    
    /**
     * Post-process content
     */
    postProcess(content, context) {
        let processed = content;
        
        // Remove excessive whitespace
        processed = processed.replace(/\n{3,}/g, '\n\n');
        processed = processed.replace(/\s{2,}/g, ' ');
        
        // Ensure proper punctuation
        processed = processed.replace(/\s+([.!?])/g, '$1');
        processed = processed.replace(/([.!?])\s*([A-Z])/g, '$1 $2');
        
        return processed.trim();
    }
    
    /**
     * Calculate quality metrics
     */
    calculateQualityMetrics(content, context) {
        const wordCount = this.countWords(content);
        const targetWordCount = context.content?.wordCount || 1000;
        const readabilityScore = this.calculateReadabilityScore(content);
        const keywordDensity = this.calculateKeywordDensity(content, context.content?.primaryKeyword);
        
        // Calculate overall quality score
        let qualityScore = 0;
        
        // Word count score (25%)
        const wordCountRatio = Math.min(wordCount / targetWordCount, 1);
        qualityScore += wordCountRatio * 0.25;
        
        // Readability score (25%)
        qualityScore += (readabilityScore / 100) * 0.25;
        
        // Keyword optimization score (25%)
        const optimalDensity = 2.0;
        const densityScore = Math.max(0, 1 - Math.abs(keywordDensity - optimalDensity) / optimalDensity);
        qualityScore += densityScore * 0.25;
        
        // Structure score (25%)
        const headings = this.extractHeadings(content);
        const structureScore = Math.min(headings.length / 5, 1); // Optimal: 5+ headings
        qualityScore += structureScore * 0.25;
        
        return {
            overall: qualityScore,
            wordCount: wordCountRatio,
            readability: readabilityScore / 100,
            keywordOptimization: densityScore,
            structure: structureScore
        };
    }
    
    /**
     * Utility functions
     */
    countWords(text) {
        return text.trim().split(/\s+/).length;
    }
    
    calculateKeywordDensity(content, keyword) {
        if (!keyword) return 0;
        
        const words = content.toLowerCase().split(/\s+/);
        const keywordWords = keyword.toLowerCase().split(/\s+/);
        const keywordCount = words.filter(word => 
            keywordWords.some(kw => word.includes(kw))
        ).length;
        
        return (keywordCount / words.length) * 100;
    }
    
    calculateReadabilityScore(content) {
        // Simplified Flesch Reading Ease score
        const sentences = content.split(/[.!?]+/).length;
        const words = this.countWords(content);
        const syllables = this.countSyllables(content);
        
        if (sentences === 0 || words === 0) return 0;
        
        const score = 206.835 - (1.015 * (words / sentences)) - (84.6 * (syllables / words));
        return Math.max(0, Math.min(100, score));
    }
    
    countSyllables(text) {
        // Simplified syllable counting
        return text.toLowerCase().match(/[aeiouy]+/g)?.length || 0;
    }
    
    extractHeadings(content) {
        const headingRegex = /^(#{1,6})\s+(.+)$/gm;
        const headings = [];
        let match;
        
        while ((match = headingRegex.exec(content)) !== null) {
            headings.push({
                level: match[1].length,
                text: match[2].trim()
            });
        }
        
        return headings;
    }
    
    generateMetaDescription(content, context) {
        const firstSentence = content.split('.')[0];
        const keyword = context.content?.primaryKeyword || '';
        
        let metaDesc = `${keyword} - ${firstSentence}.`;
        if (metaDesc.length > 160) {
            metaDesc = metaDesc.substring(0, 157) + '...';
        }
        
        return metaDesc;
    }
    
    fixHeadingHierarchy(content) {
        // Implementation for fixing heading hierarchy
        return content;
    }
    
    highlightProductFeatures(content) {
        // Implementation for highlighting product features
        return content;
    }
    
    enhanceCallToAction(content) {
        // Implementation for enhancing call-to-action
        return content;
    }
    
    /**
     * Get processor statistics
     */
    getStats() {
        return {
            processors: this.processors.size,
            validators: this.validators.size,
            enhancers: this.enhancers.size,
            config: this.config
        };
    }
}

module.exports = ResponseProcessor;
