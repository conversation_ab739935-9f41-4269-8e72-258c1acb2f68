const express = require('express');
const router = express.Router();
const axios = require('axios');
const { verifyToken, checkSubscription, checkUsageLimit } = require('../middleware/auth');
const { createClient } = require('@supabase/supabase-js');
const OpenAIContentGenerator = require('../services/openaiContentGenerator');
const EnhancedContentGenerator = require('../services/enhancedContentGenerator');
const FirecrawlCacheManager = require('../services/firecrawlCacheManager');
const { contentResponseFormatter } = require('../middleware/responseFormatter');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Initialize Content Generators (with error handling)
let openaiGenerator = null;
let enhancedGenerator = null;
let cacheManager = null;

try {
  openaiGenerator = new OpenAIContentGenerator();
  enhancedGenerator = new EnhancedContentGenerator();
  cacheManager = new FirecrawlCacheManager();
} catch (error) {
  console.error('⚠️ Content generators initialization error:', error.message);
  console.log('📝 Content generation will not be available until OpenAI API key is configured');
}

// Test endpoint for response formatter
router.get('/test-formatter', (req, res) => {
  res.success({ message: 'Formatter working' }, 'Test successful');
});

// Valid content types
const VALID_CONTENT_TYPES = [
  'blog-post',
  'article',
  'product-page',
  'landing-page',
  'how-to-guide',
  'listicle',
  'review',
  'comparison',
  'case-study',
  'whitepaper',
  'comprehensive_travel_guide',
  'travel_guide',
  'category-page',
  'news-article'
];

// Content type validation middleware
const validateContentType = (req, res, next) => {
  const { contentType } = req.body;
  
  if (!contentType) {
    return res.status(400).json({
      success: false,
      error: 'Content type is required'
    });
  }
  
  if (!VALID_CONTENT_TYPES.includes(contentType)) {
    return res.status(400).json({
      success: false,
      error: `Invalid content type. Valid types are: ${VALID_CONTENT_TYPES.join(', ')}`
    });
  }
  
  next();
};

// Database test endpoint
router.get('/database/test-connection', async (req, res) => {
  try {
    console.log('🔍 Testing Supabase database connection...');

    // Test basic connection
    const { data, error } = await supabase
      .from('generated_content')
      .select('count', { count: 'exact', head: true });

    if (error) {
      console.error('❌ Database connection error:', error);
      return res.status(500).json({
        success: false,
        error: 'Database connection failed',
        details: error.message
      });
    }

    console.log('✅ Database connection successful');
    res.json({
      success: true,
      message: 'Database connection successful',
      timestamp: new Date().toISOString(),
      tableAccess: 'generated_content table accessible',
      recordCount: data || 0
    });

  } catch (error) {
    console.error('❌ Database test failed:', error);
    res.status(500).json({
      success: false,
      error: 'Database test failed',
      details: error.message
    });
  }
});

// Database schema validation endpoint
router.get('/database/schema', async (req, res) => {
  try {
    console.log('🔍 Validating database schema...');

    const tables = {};
    const expectedTables = [
      'generated_content',
      'users',
      'usage_tracking',
      'competitor_analysis_cache',
      'crawl_data_cache',
      'processed_insights_cache'
    ];

    // Test each expected table
    for (const tableName of expectedTables) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true });

        tables[tableName] = {
          exists: !error,
          accessible: !error,
          error: error?.message || null
        };
      } catch (tableError) {
        tables[tableName] = {
          exists: false,
          accessible: false,
          error: tableError.message
        };
      }
    }

    const accessibleTables = Object.values(tables).filter(t => t.accessible).length;
    const totalTables = expectedTables.length;

    res.json({
      success: true,
      message: 'Schema validation completed',
      timestamp: new Date().toISOString(),
      tables: tables,
      summary: {
        totalExpected: totalTables,
        accessible: accessibleTables,
        missing: totalTables - accessibleTables,
        healthScore: `${Math.round((accessibleTables / totalTables) * 100)}%`
      }
    });

  } catch (error) {
    console.error('❌ Schema validation failed:', error);
    res.status(500).json({
      success: false,
      error: 'Schema validation failed',
      details: error.message
    });
  }
});

// Test endpoint for OpenAI integration (no auth required)
router.post('/test-generate', validateContentType, async (req, res) => {
  try {
    console.log('🧪 Test endpoint called for OpenAI content generation');
    
    // Check if OpenAI generator is available
    if (!openaiGenerator) {
      return res.status(503).json({
        success: false,
        error: 'Content generation service unavailable',
        message: 'OpenAI API key is not configured. Please add OPENAI_API_KEY to your .env file.',
        documentation: '/backend/OPENAI_SETUP.md'
      });
    }

    const {
      keyword,
      contentType,
      tone,
      wordCount,
      industry,
      intent,
      targetAudience,
      secondaryKeywords,
      additionalInstructions
    } = req.body;

    if (!keyword || !contentType) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content type are required'
      });
    }

    // Prepare parameters for OpenAI content generation
    const contentParams = {
      keyword,
      contentType,
      tone: tone || 'professional',
      wordCount: wordCount || '800-1200',
      industry: industry || 'general',
      intent: intent || 'informational',
      targetAudience: targetAudience || 'general',
      secondaryKeywords: secondaryKeywords || '',
      additionalInstructions: additionalInstructions || '',
      targetUrl: req.body.targetUrl || null
    };

    console.log('🚀 Generating test content with OpenAI for keyword:', keyword);

    // Generate content using OpenAI
    const result = await openaiGenerator.generateContent(contentParams);

    console.log('🔍 OpenAI result structure:', JSON.stringify(result, null, 2));

    if (!result || !result.success) {
      throw new Error(result?.error || 'Content generation failed');
    }

    if (!result.data || !result.data.content) {
      throw new Error('Generated content is missing or invalid');
    }

    console.log('✅ Test content generation completed successfully');

    // Save to database for testing (using test user ID)
    let savedContent = null;
    try {
      const { data: dbSaveResult, error: saveError } = await supabase
        .from('generated_content')
        .insert({
          user_id: 'test-user-' + Date.now(), // Test user ID
          keyword,
          content_type: contentType,
          tone,
          word_count: result.data.wordCount || 0,
          content: result.data.content,
          metadata: {
            industry: industry || 'general',
            intent: intent || 'informational',
            targetAudience: targetAudience || 'general',
            secondaryKeywords: secondaryKeywords || '',
            additionalInstructions: additionalInstructions || ''
          },
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (saveError) {
        console.warn('⚠️ Failed to save content to database:', saveError.message);
      } else {
        savedContent = dbSaveResult;
        console.log('✅ Content saved to database with ID:', savedContent.id);
      }
    } catch (dbError) {
      console.warn('⚠️ Database save error:', dbError.message);
    }

    // Use standardized response format
    const formattedContent = contentResponseFormatter.contentGeneration(result.data, {
      keyword,
      contentType,
      tone,
      industry,
      test_mode: true,
      id: savedContent?.id || null,
      stored_at: savedContent?.created_at || null,
      database_saved: !!savedContent
    });

    // Use the response formatter if available, otherwise fallback to standard JSON
    if (res.success) {
      res.success(formattedContent, 'Content generated successfully');
    } else {
      res.json({
        success: true,
        data: formattedContent,
        message: 'Content generated successfully',
        metadata: {
          timestamp: new Date().toISOString(),
          version: '1.0.0'
        }
      });
    }
  } catch (error) {
    console.error('❌ Test content generation error:', error);

    let errorMessage = 'Failed to generate content';
    let statusCode = 500;

    if (error.message.includes('API key')) {
      errorMessage = 'Invalid OpenAI API configuration';
      statusCode = 503;
    } else if (error.message.includes('rate limit')) {
      errorMessage = 'OpenAI rate limit exceeded';
      statusCode = 429;
    } else if (error.message.includes('quota')) {
      errorMessage = 'OpenAI quota exceeded';
      statusCode = 503;
    }

    // Use the response formatter if available, otherwise fallback to standard JSON
    if (res.error) {
      res.error(errorMessage, statusCode, {
        test_mode: true,
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    } else {
      res.status(statusCode).json({
        success: false,
        error: errorMessage,
        metadata: {
          timestamp: new Date().toISOString(),
          version: '1.0.0',
          statusCode,
          test_mode: true
        },
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
});

// Generate content endpoint
router.post('/generate', verifyToken, checkUsageLimit, validateContentType, async (req, res) => {
  try {
    const {
      keyword,
      contentType,
      tone,
      wordCount,
      industry,
      location,
      searchEngine,
      intent,
      competitorData
    } = req.body;

    if (!keyword || !contentType) {
      return res.status(400).json({ error: 'Keyword and content type are required' });
    }

    // Prepare parameters for OpenAI content generation
    const contentParams = {
      keyword,
      contentType,
      tone: tone || 'professional',
      wordCount: wordCount || '800-1200',
      industry: industry || 'general',
      intent: intent || 'informational',
      targetAudience: req.body.targetAudience || 'general',
      secondaryKeywords: req.body.secondaryKeywords || '',
      additionalInstructions: req.body.additionalInstructions || '',
      competitorAnalysis: competitorData ? JSON.stringify(competitorData) : null,
      targetUrl: req.body.targetUrl || null
    };

    console.log('🚀 Generating content with OpenAI for keyword:', keyword);

    // Generate content using OpenAI
    const result = await openaiGenerator.generateContent(contentParams);

    if (!result.success) {
      throw new Error(result.error || 'Content generation failed');
    }

    const generatedContent = result.data;

    // Save to database
    const { data: savedContent, error: saveError } = await supabase
      .from('generated_content')
      .insert({
        user_id: req.user.id,
        keyword,
        content_type: contentType,
        content: generatedContent.content,
        metadata: {
          title: generatedContent.title,
          meta_description: generatedContent.meta_description,
          headings: generatedContent.headings,
          keywords_used: generatedContent.keywords_used,
          seo_recommendations: generatedContent.seo_recommendations,
          internal_links: generatedContent.internal_links,
          call_to_action: generatedContent.call_to_action,
          quality_score: generatedContent.quality_score,
          readability_score: generatedContent.readability_score,
          model_used: generatedContent.model_used,
          tokens_used: generatedContent.tokens_used,
          tone,
          wordCount,
          industry,
          location,
          searchEngine,
          intent
        },
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (saveError) {
      console.error('Save error:', saveError);
    }

    // Update usage tracking
    await updateUsageTracking(req.user.id, 'content_generations');

    console.log('✅ Content generation completed successfully');

    res.json({
      success: true,
      data: {
        title: generatedContent.title,
        content: generatedContent.content,
        meta_description: generatedContent.meta_description,
        headings: generatedContent.headings || [],
        keywords_used: generatedContent.keywords_used || [],
        seo_recommendations: generatedContent.seo_recommendations || [],
        internal_links: generatedContent.internal_links || [],
        call_to_action: generatedContent.call_to_action || '',
        quality_score: generatedContent.quality_score || 0,
        readability_score: generatedContent.readability_score || 60,
        word_count: generatedContent.word_count || 0,
        model_used: generatedContent.model_used,
        tokens_used: generatedContent.tokens_used,
        generated_at: generatedContent.generated_at,
        id: savedContent?.id
      },
      metadata: {
        keyword,
        contentType,
        tone,
        industry,
        parameters: contentParams
      }
    });
  } catch (error) {
    console.error('❌ Content generation error:', error);

    // Provide more specific error messages
    let errorMessage = 'Failed to generate content';
    let statusCode = 500;

    if (error.message.includes('API key')) {
      errorMessage = 'Invalid API configuration. Please contact support.';
      statusCode = 503;
    } else if (error.message.includes('rate limit')) {
      errorMessage = 'Rate limit exceeded. Please try again later.';
      statusCode = 429;
    } else if (error.message.includes('timeout')) {
      errorMessage = 'Request timeout. Please try again.';
      statusCode = 408;
    } else if (error.message.includes('quota')) {
      errorMessage = 'API quota exceeded. Please contact support.';
      statusCode = 503;
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Get user's generated content
router.get('/list', verifyToken, async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const { data: content, error, count } = await supabase
      .from('generated_content')
      .select('*', { count: 'exact' })
      .eq('user_id', req.user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw error;
    }

    res.json({
      content,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('List content error:', error);
    res.status(500).json({ error: 'Failed to retrieve content' });
  }
});

// Get single content item
router.get('/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;

    const { data: content, error } = await supabase
      .from('generated_content')
      .select('*')
      .eq('id', id)
      .eq('user_id', req.user.id)
      .single();

    if (error || !content) {
      return res.status(404).json({ error: 'Content not found' });
    }

    res.json(content);
  } catch (error) {
    console.error('Get content error:', error);
    res.status(500).json({ error: 'Failed to retrieve content' });
  }
});

// Helper function to build optimized prompts
function buildContentPrompt({ keyword, contentType, tone, wordCount, industry, intent, competitorData }) {
  const wordCountRange = {
    '500-800': { min: 500, max: 800 },
    '800-1200': { min: 800, max: 1200 },
    '1200-2000': { min: 1200, max: 2000 },
    '2000+': { min: 2000, max: 3000 }
  };

  const range = wordCountRange[wordCount] || wordCountRange['800-1200'];

  // E-E-A-T specific guidelines based on content type and industry
  const eatGuidelines = {
    'blog-post': 'Include personal insights, real examples, and actionable advice. Cite credible sources and include author expertise.',
    'article': 'Provide comprehensive coverage with expert analysis. Reference authoritative sources and industry standards.',
    'landing-page': 'Build trust through testimonials, certifications, and clear value propositions. Include contact information and credentials.',
    'product-description': 'Highlight unique features, benefits, and use cases. Include specifications and compatibility information.'
  };

  const industryExpertise = {
    'healthcare': 'medical studies, peer-reviewed research, healthcare professionals, FDA guidelines',
    'finance': 'financial regulations, market data, certified financial advisors, government sources',
    'technology': 'technical specifications, industry standards, expert reviews, official documentation',
    'legal': 'legal precedents, statutory requirements, licensed attorneys, court decisions',
    'education': 'academic research, educational institutions, certified educators, curriculum standards'
  };

  let prompt = `Create a comprehensive, expert-level ${contentType} about "${keyword}" for the ${industry} industry.

E-E-A-T OPTIMIZATION REQUIREMENTS:
- EXPERIENCE: Include real-world examples, case studies, and practical applications
- EXPERTISE: Demonstrate deep knowledge through technical details and industry insights
- AUTHORITATIVENESS: Reference credible sources, statistics, and established authorities
- TRUSTWORTHINESS: Use factual, well-researched information with proper citations

CONTENT SPECIFICATIONS:
- Word count: ${range.min}-${range.max} words
- Tone: ${tone}
- Search intent: ${intent}
- Industry focus: ${industry}

NLP OPTIMIZATION RULES:
- Use clear subject-verb-object sentence structure
- Avoid abstract terms like: meticulous, navigating, complexities, realm, bespoke, endeavor
- Write in direct, accessible language that's easily interpretable
- Use concrete, specific terms instead of vague descriptors
- Maintain readability score above 60 (Flesch Reading Ease)

CONTENT STRUCTURE:
- H1: Include primary keyword naturally
- H2-H3: Use semantic variations and related terms
- Optimal keyword density: 1-2% for primary keyword
- Include LSI keywords and entity variations naturally
- Structure for featured snippet opportunities`;

  // Add industry-specific expertise requirements
  if (industryExpertise[industry]) {
    prompt += `\n\nINDUSTRY EXPERTISE SOURCES:
Reference authoritative sources such as: ${industryExpertise[industry]}`;
  }

  // Add content-type specific E-E-A-T guidelines
  if (eatGuidelines[contentType]) {
    prompt += `\n\nCONTENT TYPE GUIDELINES:
${eatGuidelines[contentType]}`;
  }

  if (competitorData) {
    prompt += `\n\nCOMPETITOR INSIGHTS (use to exceed competition):
- Average word count: ${competitorData.avgWordCount} (aim to exceed by 10-20%)
- Average headings: ${competitorData.avgHeadings}
- Top keywords to include: ${competitorData.topKeywords?.slice(0, 10).map(k => k.word).join(', ')}
- Common entities: ${competitorData.commonEntities?.join(', ')}
- Content gaps to fill: Provide unique value not covered by competitors`;
  }

  prompt += `\n\nFORMATTING REQUIREMENTS:
- Start with an engaging hook that addresses user intent
- Use short paragraphs (2-3 sentences maximum)
- Include bullet points or numbered lists for scannability
- Add relevant statistics and data points
- Include actionable takeaways and next steps
- End with a strong conclusion that reinforces key points
- Naturally incorporate "${keyword}" with 1-2% density
- Use semantic variations throughout content

OUTPUT QUALITY STANDARDS:
- Provide unique insights not available elsewhere
- Include specific, actionable advice
- Use current, up-to-date information
- Maintain professional credibility throughout
- Ensure content serves user search intent completely`;

  return prompt;
}

// Helper function to update usage tracking
async function updateUsageTracking(userId, type) {
  const today = new Date().toISOString().split('T')[0];
  
  const { data: existing } = await supabase
    .from('usage_tracking')
    .select('*')
    .eq('user_id', userId)
    .eq('date', today)
    .single();

  if (existing) {
    await supabase
      .from('usage_tracking')
      .update({ 
        [type]: existing[type] + 1,
        updated_at: new Date().toISOString()
      })
      .eq('id', existing.id);
  } else {
    await supabase
      .from('usage_tracking')
      .insert({
        user_id: userId,
        date: today,
        [type]: 1,
        created_at: new Date().toISOString()
      });
  }
}

// Test endpoint without authentication for debugging
router.post('/test-generate', async (req, res) => {
  try {
    const {
      keyword,
      contentType = 'blog_post',
      tone = 'professional',
      wordCount = '800-1200',
      industry = 'general'
    } = req.body;

    if (!keyword) {
      return res.status(400).json({ error: 'Keyword is required' });
    }

    // Build the optimized prompt
    const prompt = `Create a high-quality ${contentType} about "${keyword}" for the ${industry} industry.

REQUIREMENTS:
- Word count: 800-1200 words
- Tone: ${tone}
- Include proper heading structure (H1, H2, H3)
- Optimize for SEO with natural keyword usage
- Follow E-E-A-T guidelines
- Make it engaging and informative

Write comprehensive, valuable content about "${keyword}".`;

    // Call Groq API
    const groqResponse = await axios.post(
      'https://api.groq.com/openai/v1/chat/completions',
      {
        model: 'llama-3.1-8b-instant',
        messages: [
          {
            role: 'system',
            content: 'You are an expert SEO content writer specializing in creating high-quality, optimized content that follows E-E-A-T guidelines and NLP best practices.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const generatedContent = groqResponse.data.choices[0].message.content;

    res.json({
      success: true,
      content: generatedContent,
      metadata: {
        keyword,
        contentType,
        tone,
        wordCount: generatedContent.split(' ').length,
        generatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Test content generation error:', error);
    res.status(500).json({ 
      success: false,
      error: 'Failed to generate content',
      details: error.message 
    });
  }
});

// Enhanced content generation with Firecrawl competitor analysis
router.post('/generate-enhanced', verifyToken, checkSubscription, checkUsageLimit, async (req, res) => {
  try {
    console.log('🚀 Enhanced content generation with Firecrawl analysis started');

    const {
      keyword,
      contentType,
      tone,
      wordCount,
      industry,
      location,
      searchEngine,
      intent,
      targetAudience,
      secondaryKeywords,
      additionalInstructions,
      useFirecrawl = true,
      forceRefresh = false
    } = req.body;

    if (!keyword || !contentType) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content type are required'
      });
    }

    // Prepare enhanced parameters
    const enhancedParams = {
      keyword,
      contentType,
      tone: tone || 'professional',
      wordCount: wordCount || '1250',
      industry: industry || 'general',
      intent: intent || 'informational',
      targetAudience: targetAudience || 'general',
      secondaryKeywords: secondaryKeywords || '',
      additionalInstructions: additionalInstructions || '',
      location: location || 'global',
      searchEngine: searchEngine || 'google.com',
      forceRefresh: forceRefresh,
      useFirecrawl: useFirecrawl
    };

    console.log('🎯 Generating enhanced content with competitor analysis for:', keyword);

    // Generate enhanced content with Firecrawl analysis
    const result = await enhancedGenerator.generateEnhancedContent(enhancedParams);

    if (!result.success) {
      throw new Error(result.error || 'Enhanced content generation failed');
    }

    const generatedContent = result.data;

    // Save enhanced content to database with competitive analysis
    const { data: savedContent, error: saveError } = await supabase
      .from('generated_content')
      .insert({
        user_id: req.user.id,
        keyword,
        content_type: contentType,
        content: generatedContent.content,
        metadata: {
          title: generatedContent.title,
          meta_description: generatedContent.meta_description,
          headings: generatedContent.headings,
          keywords_used: generatedContent.keywords_used,
          seo_recommendations: generatedContent.seo_recommendations,
          internal_links: generatedContent.internal_links,
          call_to_action: generatedContent.call_to_action,
          quality_score: generatedContent.quality_score,
          readability_score: generatedContent.readability_score,
          model_used: generatedContent.model_used,
          tokens_used: generatedContent.tokens_used,
          // Enhanced metadata with competitive analysis
          competitive_analysis: result.competitiveAnalysis,
          enhanced_seo_recommendations: result.enhancedSEORecommendations,
          competitor_comparison: result.competitorComparison,
          generation_type: 'enhanced_firecrawl',
          tone,
          wordCount,
          industry,
          location,
          searchEngine,
          intent
        },
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (saveError) {
      console.error('Save error:', saveError);
    }

    // Update usage tracking
    await updateUsageTracking(req.user.id, 'content_generations');

    console.log('✅ Enhanced content generation completed successfully');

    res.json({
      success: true,
      data: {
        title: generatedContent.title,
        content: generatedContent.content,
        meta_description: generatedContent.meta_description,
        headings: generatedContent.headings || [],
        keywords_used: generatedContent.keywords_used || [],
        seo_recommendations: generatedContent.seo_recommendations || [],
        internal_links: generatedContent.internal_links || [],
        call_to_action: generatedContent.call_to_action || '',
        quality_score: generatedContent.quality_score || 0,
        readability_score: generatedContent.readability_score || 60,
        word_count: generatedContent.word_count || 0,
        model_used: generatedContent.model_used,
        tokens_used: generatedContent.tokens_used,
        generated_at: generatedContent.generated_at,
        id: savedContent?.id
      },
      // Enhanced response with competitive insights
      competitiveAnalysis: result.competitiveAnalysis || {},
      enhancedSEORecommendations: result.enhancedSEORecommendations || [],
      competitorComparison: result.competitorComparison || {},
      metadata: {
        keyword,
        contentType,
        tone,
        industry,
        location,
        parameters: enhancedParams,
        generationType: 'enhanced_firecrawl'
      }
    });

  } catch (error) {
    console.error('❌ Enhanced content generation error:', error);

    // Provide more specific error messages
    let errorMessage = 'Failed to generate enhanced content';
    let statusCode = 500;

    if (error.message.includes('Firecrawl')) {
      errorMessage = 'Competitor analysis failed. Falling back to standard generation.';
      statusCode = 503;
    } else if (error.message.includes('API key')) {
      errorMessage = 'Invalid API configuration. Please contact support.';
      statusCode = 503;
    } else if (error.message.includes('rate limit')) {
      errorMessage = 'Rate limit exceeded. Please try again later.';
      statusCode = 429;
    } else if (error.message.includes('quota')) {
      errorMessage = 'API quota exceeded. Please contact support.';
      statusCode = 503;
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
      fallbackAvailable: true
    });
  }
});

// Test content history endpoint (no auth required for testing)
router.get('/test-history', async (req, res) => {
  try {
    console.log('🔍 Fetching content history for testing...');

    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const { data: content, error, count } = await supabase
      .from('generated_content')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('❌ Failed to fetch content history:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch content history',
        details: error.message
      });
    }

    console.log(`✅ Retrieved ${content?.length || 0} content items`);

    res.json({
      success: true,
      content: content || [],
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Content history error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch content history',
      details: error.message
    });
  }
});

// Get recent content (alternative endpoint - no auth for testing)
router.get('/test-recent', async (req, res) => {
  try {
    console.log('🔍 Fetching recent content...');

    const { data: content, error } = await supabase
      .from('generated_content')
      .select('id, keyword, content_type, created_at, word_count')
      .order('created_at', { ascending: false })
      .limit(5);

    if (error) {
      console.error('❌ Failed to fetch recent content:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch recent content',
        details: error.message
      });
    }

    res.json({
      success: true,
      content: content || [],
      count: content?.length || 0,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Recent content error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch recent content',
      details: error.message
    });
  }
});

// Enhanced content generation endpoint (no auth required for testing)
router.post('/enhanced-generate', async (req, res) => {
  try {
    const {
      keyword,
      contentType,
      tone = 'professional',
      industry = 'general',
      useFirecrawl = false,
      competitorAnalysis = false
    } = req.body;

    if (!keyword) {
      return res.status(400).json({
        success: false,
        error: 'Keyword is required'
      });
    }

    console.log(`🚀 Enhanced content generation for "${keyword}"`);

    // Use enhanced content generator
    const contentParams = {
      keyword,
      contentType,
      tone,
      industry,
      wordCount: 1250,
      useFirecrawl,
      competitorAnalysis
    };

    const result = await enhancedGenerator.generateContent(contentParams);

    if (!result.success) {
      throw new Error(result.error || 'Enhanced content generation failed');
    }

    res.json({
      success: true,
      content: result.content,
      competitorInsights: result.competitorInsights || null,
      firecrawlData: result.firecrawlData || null,
      seoRecommendations: result.seoRecommendations || [],
      metadata: {
        keyword,
        contentType,
        tone,
        industry,
        enhanced: true,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Enhanced content generation error:', error.message);
    res.status(500).json({
      success: false,
      error: 'Enhanced content generation failed',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;