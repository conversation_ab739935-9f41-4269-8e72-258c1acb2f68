const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

async function checkSchema() {
  try {
    // Try to get table info
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .limit(1);

    if (error) {
      console.error('Error checking schema:', error);
      
      // Try to create the table
      console.log('Attempting to create users table...');
      const { error: createError } = await supabase.rpc('create_users_table', {});
      
      if (createError) {
        console.error('Error creating table:', createError);
      } else {
        console.log('Users table created successfully');
      }
    } else {
      console.log('Users table exists, sample data:', data);
    }
  } catch (error) {
    console.error('Failed to check schema:', error);
  }
}

checkSchema();