const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middleware/auth');
const demoMode = require('../middleware/demoMode');
const { createClient } = require('@supabase/supabase-js');
const AdvancedAnalyticsEngine = require('../services/advancedAnalyticsEngine');
const ContentEditorDashboard = require('../services/contentEditorDashboard');
const logger = require('../services/logger');

// Initialize analytics engines
const analyticsEngine = new AdvancedAnalyticsEngine();
const contentDashboard = new ContentEditorDashboard();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Dashboard overview endpoint - Real-time metrics summary
router.get('/overview', demoMode, verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Return demo data if in demo mode
    if (req.user.isDemo) {
      return res.json({
        success: true,
        timestamp: new Date().toISOString(),
        user: {
          id: userId,
          totalProjects: 5,
          activeProjects: 3,
          totalGenerations: 47,
          avgWordCount: 1250,
          avgPrecisionScore: 92.5
        },
        metrics: {
          projects: {
            total: 5,
            active: 3,
            inactive: 2,
            growth: '+25%'
          },
          content: {
            totalGenerations: 47,
            avgWordCount: 1250,
            avgPrecisionScore: 92.5,
            successRate: 98.3
          },
          performance: {
            avgProcessingTime: '18.2s',
            systemUptime: '99.9%',
            errorRate: '0.3%',
            apiHealth: 'excellent'
          }
        },
        trends: {
          dailyUsage: {
            [new Date().toISOString().split('T')[0]]: 5,
            [new Date(Date.now() - 86400000).toISOString().split('T')[0]]: 8,
            [new Date(Date.now() - 172800000).toISOString().split('T')[0]]: 6
          },
          projectsThisMonth: 3,
          contentThisWeek: 22
        }
      });
    }
    
    // Get user's projects count
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('id, created_at, status')
      .eq('user_id', userId);

    if (projectsError) {
      logger.error('Projects query error:', projectsError);
    }

    // Get user's content generations count (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
    const { data: recentContent, error: contentError } = await supabase
      .from('generated_content')
      .select('id, created_at, word_count, precision_score')
      .eq('user_id', userId)
      .gte('created_at', thirtyDaysAgo);

    if (contentError) {
      logger.error('Content query error:', contentError);
    }

    // Calculate analytics metrics
    const totalProjects = projects?.length || 0;
    const activeProjects = projects?.filter(p => p.status === 'active').length || 0;
    const totalGenerations = recentContent?.length || 0;
    const avgWordCount = recentContent?.length > 0 
      ? Math.round(recentContent.reduce((sum, c) => sum + (c.word_count || 0), 0) / recentContent.length)
      : 0;
    const avgPrecisionScore = recentContent?.length > 0
      ? Math.round(recentContent.reduce((sum, c) => sum + (c.precision_score || 0), 0) / recentContent.length * 100) / 100
      : 0;

    // Get real-time system metrics from dashboard
    const systemMetrics = await contentDashboard.getRealTimeMetrics();
    
    // Generate daily usage trends
    const usageByDay = {};
    recentContent?.forEach(content => {
      const day = content.created_at.split('T')[0];
      usageByDay[day] = (usageByDay[day] || 0) + 1;
    });

    const overview = {
      success: true,
      timestamp: new Date().toISOString(),
      user: {
        id: userId,
        totalProjects,
        activeProjects,
        totalGenerations,
        avgWordCount,
        avgPrecisionScore
      },
      metrics: {
        projects: {
          total: totalProjects,
          active: activeProjects,
          inactive: totalProjects - activeProjects,
          growth: totalProjects > 0 ? '+12%' : '0%' // Mock growth for now
        },
        content: {
          totalGenerations,
          avgWordCount,
          avgPrecisionScore,
          successRate: totalGenerations > 0 ? 95.2 : 0 // Mock success rate
        },
        performance: {
          avgProcessingTime: systemMetrics?.avgProcessingTime || '28.5s',
          systemUptime: systemMetrics?.uptime || '99.9%',
          errorRate: systemMetrics?.errorRate || '0.8%',
          apiHealth: 'excellent'
        }
      },
      trends: {
        dailyUsage: usageByDay,
        projectsThisMonth: activeProjects,
        contentThisWeek: recentContent?.filter(c => 
          new Date(c.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        ).length || 0
      }
    };

    res.json(overview);

  } catch (error) {
    logger.error('Dashboard overview error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch dashboard overview'
    });
  }
});

// Comprehensive analytics endpoint
router.get('/analytics', demoMode, verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { timeRange = '30d', includeDetails = false } = req.query;

    // Calculate date range
    const timeRanges = {
      '7d': 7,
      '30d': 30,
      '90d': 90,
      '1y': 365
    };
    const days = timeRanges[timeRange] || 30;
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString();

    // Get comprehensive user data
    const [projectsResult, contentResult] = await Promise.all([
      supabase
        .from('projects')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', startDate),
      supabase
        .from('generated_content')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', startDate)
    ]);

    const projects = projectsResult.data || [];
    const content = contentResult.data || [];

    // Generate advanced analytics using the analytics engine
    const analyticsData = await analyticsEngine.generateComprehensiveAnalytics({
      projects,
      content,
      timeRange,
      userId
    });

    // Get performance insights
    const performanceMetrics = await contentDashboard.getPerformanceInsights(userId, timeRange);

    // Calculate trend analysis
    const trendAnalysis = await analyticsEngine.calculateTrends(content, timeRange);

    const analytics = {
      success: true,
      timeRange,
      dataPoints: content.length,
      analytics: {
        overview: analyticsData.overview,
        performance: performanceMetrics,
        trends: trendAnalysis,
        insights: analyticsData.insights
      },
      charts: {
        contentGeneration: analyticsData.charts.contentGeneration,
        precisionScores: analyticsData.charts.precisionScores,
        wordCountDistribution: analyticsData.charts.wordCountDistribution,
        projectActivity: analyticsData.charts.projectActivity
      }
    };

    if (includeDetails === 'true') {
      analytics.rawData = {
        projects: projects.slice(0, 10), // Limit for performance
        content: content.slice(0, 20)
      };
    }

    res.json(analytics);

  } catch (error) {
    logger.error('Dashboard analytics error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch analytics data'
    });
  }
});

// Project-specific metrics endpoint
router.get('/projects/metrics', verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { projectId } = req.query;

    let projectsQuery = supabase
      .from('projects')
      .select(`
        id, name, description, status, created_at, updated_at,
        keywords, settings
      `)
      .eq('user_id', userId);

    if (projectId) {
      projectsQuery = projectsQuery.eq('id', projectId);
    }

    const { data: projects, error: projectsError } = await projectsQuery;

    if (projectsError) {
      throw projectsError;
    }

    // Get content for these projects
    const projectIds = projects.map(p => p.id);
    const { data: projectContent, error: contentError } = await supabase
      .from('generated_content')
      .select('*')
      .in('project_id', projectIds);

    if (contentError) {
      throw contentError;
    }

    // Calculate metrics for each project
    const projectMetrics = projects.map(project => {
      const content = projectContent.filter(c => c.project_id === project.id);
      
      return {
        project: {
          id: project.id,
          name: project.name,
          status: project.status,
          created: project.created_at,
          keywords: project.keywords?.length || 0
        },
        metrics: {
          totalContent: content.length,
          avgWordCount: content.length > 0 
            ? Math.round(content.reduce((sum, c) => sum + (c.word_count || 0), 0) / content.length)
            : 0,
          avgPrecisionScore: content.length > 0
            ? Math.round(content.reduce((sum, c) => sum + (c.precision_score || 0), 0) / content.length * 100) / 100
            : 0,
          lastActivity: content.length > 0 
            ? Math.max(...content.map(c => new Date(c.created_at).getTime()))
            : new Date(project.created_at).getTime(),
          contentTypes: [...new Set(content.map(c => c.content_type))],
          performance: {
            successRate: content.length > 0 ? (content.filter(c => c.precision_score > 0.8).length / content.length * 100).toFixed(1) : 0,
            avgProcessingTime: '24.2s', // Mock for now
            errorCount: content.filter(c => c.precision_score < 0.5).length
          }
        }
      };
    });

    res.json({
      success: true,
      totalProjects: projects.length,
      projects: projectMetrics,
      summary: {
        totalContent: projectContent.length,
        avgContentPerProject: projects.length > 0 ? Math.round(projectContent.length / projects.length) : 0,
        mostActiveProject: projectMetrics.sort((a, b) => b.metrics.totalContent - a.metrics.totalContent)[0]?.project.name || null
      }
    });

  } catch (error) {
    logger.error('Project metrics error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch project metrics'
    });
  }
});

// Real-time precision monitoring endpoint
router.get('/precision', verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Get recent precision data
    const { data: recentContent, error } = await supabase
      .from('generated_content')
      .select('id, keyword, precision_score, word_count, target_word_count, created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) {
      throw error;
    }

    // Get real-time precision metrics from dashboard
    const precisionMetrics = await contentDashboard.getPrecisionMetrics();

    // Calculate precision statistics
    const precisionStats = {
      totalGenerations: recentContent.length,
      averagePrecision: recentContent.length > 0 
        ? Math.round(recentContent.reduce((sum, c) => sum + (c.precision_score || 0), 0) / recentContent.length * 100) / 100
        : 0,
      highPrecisionCount: recentContent.filter(c => c.precision_score >= 0.95).length,
      mediumPrecisionCount: recentContent.filter(c => c.precision_score >= 0.8 && c.precision_score < 0.95).length,
      lowPrecisionCount: recentContent.filter(c => c.precision_score < 0.8).length,
      precisionTrend: 'improving', // Mock trend
      complianceRate: recentContent.length > 0 
        ? Math.round(recentContent.filter(c => c.precision_score >= 0.95).length / recentContent.length * 100)
        : 0
    };

    // Get real-time component status
    const componentStatus = await contentDashboard.getComponentHealth();

    res.json({
      success: true,
      timestamp: new Date().toISOString(),
      precision: {
        statistics: precisionStats,
        realtimeMetrics: precisionMetrics,
        componentHealth: componentStatus,
        recentGenerations: recentContent.slice(0, 10).map(c => ({
          id: c.id,
          keyword: c.keyword,
          precision: c.precision_score,
          wordCount: c.word_count,
          targetWordCount: c.target_word_count,
          timestamp: c.created_at,
          status: c.precision_score >= 0.95 ? 'excellent' : c.precision_score >= 0.8 ? 'good' : 'needs_improvement'
        }))
      }
    });

  } catch (error) {
    logger.error('Precision monitoring error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch precision metrics'
    });
  }
});

// Performance trends endpoint
router.get('/performance', verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { timeRange = '7d' } = req.query;

    // Get performance data from content dashboard
    const performanceData = await contentDashboard.getPerformanceTrends(userId, timeRange);
    
    // Get system-wide performance metrics
    const systemPerformance = await analyticsEngine.getSystemPerformance();

    res.json({
      success: true,
      timeRange,
      performance: {
        user: performanceData,
        system: systemPerformance,
        trends: {
          processingTime: performanceData.avgProcessingTime || [],
          successRate: performanceData.successRate || [],
          throughput: performanceData.throughput || [],
          errorRate: performanceData.errorRate || []
        },
        benchmarks: {
          targetProcessingTime: '30s',
          targetSuccessRate: '95%',
          targetThroughput: '100/hour',
          targetErrorRate: '< 2%'
        }
      }
    });

  } catch (error) {
    logger.error('Performance trends error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch performance data'
    });
  }
});

// System health endpoint
router.get('/health', async (req, res) => {
  try {
    // Get comprehensive system health
    const systemHealth = await contentDashboard.getSystemHealth();
    const analyticsHealth = await analyticsEngine.getHealthStatus();

    res.json({
      success: true,
      timestamp: new Date().toISOString(),
      system: {
        status: 'operational',
        uptime: systemHealth.uptime,
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development'
      },
      components: {
        database: systemHealth.database,
        analytics: analyticsHealth,
        contentEngine: systemHealth.contentEngine,
        precisionEngine: systemHealth.precisionEngine
      },
      performance: {
        responseTime: systemHealth.responseTime,
        memoryUsage: systemHealth.memoryUsage,
        cpuUsage: systemHealth.cpuUsage
      }
    });

  } catch (error) {
    logger.error('System health error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get system health'
    });
  }
});

module.exports = router;