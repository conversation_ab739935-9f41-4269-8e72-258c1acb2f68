(function(i){function e(){var f=i();return f.default||f}if(typeof exports=="object"&&typeof module=="object")module.exports=e();else if(typeof define=="function"&&define.amd)define(e);else{var t=typeof globalThis<"u"?globalThis:typeof global<"u"?global:typeof self<"u"?self:this||{};t.prettierPlugins=t.prettierPlugins||{},t.prettierPlugins.flow=e()}})(function(){"use strict";var hA0=Object.create;var g5=Object.defineProperty;var yA0=Object.getOwnPropertyDescriptor;var gA0=Object.getOwnPropertyNames;var _A0=Object.getPrototypeOf,wA0=Object.prototype.hasOwnProperty;var bA0=(a0,W0)=>()=>(W0||a0((W0={exports:{}}).exports,W0),W0.exports),lY=(a0,W0)=>{for(var bx in W0)g5(a0,bx,{get:W0[bx],enumerable:!0})},pY=(a0,W0,bx,Yx)=>{if(W0&&typeof W0=="object"||typeof W0=="function")for(let x1 of gA0(W0))!wA0.call(a0,x1)&&x1!==bx&&g5(a0,x1,{get:()=>W0[x1],enumerable:!(Yx=yA0(W0,x1))||Yx.enumerable});return a0};var TA0=(a0,W0,bx)=>(bx=a0!=null?hA0(_A0(a0)):{},pY(W0||!a0||!a0.__esModule?g5(bx,"default",{value:a0,enumerable:!0}):bx,a0)),EA0=a0=>pY(g5({},"__esModule",{value:!0}),a0);var kY=bA0(mj=>{(function(a0){typeof globalThis!="object"&&(this?W0():(a0.defineProperty(a0.prototype,"_T_",{configurable:!0,get:W0}),_T_));function W0(){var bx=this||self;bx.globalThis=bx,delete a0.prototype._T_}})(Object);(function(a0){"use strict";var W0="loc",bx=70416,Yx=69748,x1=163,D1=92159,Ie=43587,k2="labeled_statement",Rr="&=",_1="int_of_string",Ce=110591,Qs=92909,Qa=11559,$o="regexp",_5=43301,Qp=11703,w5=122654,Za=255,Ej="%ni",b5=68252,Aj=232,T5=42785,Kn="declare_variable",Zp="while",E5=66938,A5=70301,S5=124907,x4=126515,Sj=218,Hn="pattern_identifier",P5=67643,Vn="export_source",I5=216,C5=64279,Pj="Out_of_memory",N5=113788,Ij="comments",O5=126624,Cj="win32",$n="object_key_bigint_literal",Nj=185,Oj=283,r4=123214,Wo="constructor",j5=69955,Wn="import_declaration",D5=68437,F5="Failure",e4="Unix.Unix_error",R5=64255,M5=42539,L5=110579,Qn="export_default_declaration",Zn="jsx_attribute_name",t4=11727,q5=43002,n4=126500,x7="component_param_pattern",jj="collect_comments_opt",r7="match_unary_pattern",e7="keyof_type",Dj="Invalid binary/octal ",Fj="range",U5=170,xs="false",B5=43798,Rj=", characters ",t7="object_type_property_getter",X5=65547,G5=126467,Y5=65007,J5=42237,z5=8318,K5=71215,n7="object_property_type",u7="type_alias",H5=67742,i7="function_body",Mj=304,V5=68111,u4=120745,$5=71959,i4=43880,Lj="Match_failure",qj=280,f7="type_cast",lt=109,rs="void",W5="generator",Q5=125124,Z5=101589,f4=94179,Uj=">>>",c4=70404,c7="optional_indexed_access_type",W1="argument",a7="object_property",s7="object_type_property",xy=67004,ry=42783,ey=68850,Bj="@",ty=43741,ny=43487,a4="object",Xj="end",s4=126571,uy=71956,iy=208,fy=126566,cy=67702,Gj="EEXIST",o7="this_expression",Yj=203,ay=11507,sy=113807,o4=119893,oy=42735,Ql="rest",v7="null_literal",Zl="protected",vy=43615,m2=8231,ly=68149,py=73727,ky=72348,my=92995,Qo=224,dy=11686,hy=43013,l7="assignment_pattern",yy=12329,p7="function_type",Zo=192,k7="jsx_element_name",gy=70018,m7="catch_clause_pattern",v4=126540,d7="template_literal",_y=120654,wy=68497,by=67679,h7="readonly_type",Ty=68735,Ey="<",l4=": No such file or directory",Ay=66915,Sy="chain",Jj="!",y7="object_type",Py=43712,p4=64297,Iy=183969,Cy=43503,Ny=67591,xv=65278,Oy=67669,g7="for_of_assignment_pattern",x6="`",jy=11502,_7="catch_body",Dy=42191,Zs=-744106340,Fy=182,rv=":",zj="a string",Ry=65663,My=66978,Ly=71947,k4=43519,qy=71086,Uy=125258,By=12538,w7="expression_or_spread",Kj="Printexc.handle_uncaught_exception",m4=69956,d4=120122,h4=247,Hj=231,Xy=" : flags Open_rdonly and Open_wronly are not compatible",b7="statement_fork_point",Vj=710,$j=-692038429,ze="static",Gy=55203,Yy=64324,Jy=64111,Wj="!==",zy=120132,Ky=124903,r6="class",Hy=222,T7="pattern_number_literal",es="kind",Vy=71903,E7="variable_declarator",A7="typeof_expression",$y=126627,Wy=70084,Qj=228,y4=70480,S7="class_private_field",Qy=239,g4=120713,on=65535,P7="private_name",Zy=43137,I7="remote_identifier",x9=70161,C7="label_identifier",r9="src/parser/statement_parser.ml",e9=8335,t9=19903,n9=64310,ev="_",N7="for_init_declaration",Zj="infer",u9=64466,i9=43018,xD="tokens",f9=92735,c9=66954,a9=65473,s9=70285,O7="sequence",o9="compare: functional value",v9=69890,e6=1e3,l9=65487,p9=42653,rD="\\\\",eD="%=",j7="match_member_pattern_base",k9=72367,D7="function_rest_param",tD="/static/",m9=124911,d9=65276,_4=126558,h9=11498,nD=137,F7="export_default_declaration_decl",y9="cases",w4=126602,R7="jsx_child",Ke="continue",g9=42962,uD="importKind",o1=122,C3="Literal",M7="pattern_object_property_identifier_key",_9=42508,xo="in",w9=55238,b9=67071,T9=70831,E9=72161,A9=67462,iD="<<=",S9=43009,P9=66383,b4=67827,I9=72202,C9=69839,N9=66775,fD="-=",tv=8202,O9=70105,j9=120538,L7="for_in_left_declaration",D9="rendersType",T4=126563,F9=70708,E4=126523,cD=166,q7="match_",aD=202,R9=110951,ts="component",A4=126552,M9=66977,sD=213,U7="enum_member_identifier",oD=210,B7="enum_bigint_body",vD=">=",L9=126495,q9="specifiers",lD=-88,U9="=",B9=65338,t6="members",X9=123535,G9=43702,Y9=72767,nv="get",J9=126633,S4=126536,z9=94098,K9="types",H9=113663,pD="Internal Error: Found private field in object props",X7="jsx_element",V9=70366,$9=110959,P4=120655,kD="trailingComments",mD=282,ro=24029,W9=-100,$2="yield",G7="binding_pattern",dD=275,Y7="typeof_identifier",hD="ENOTEMPTY",Q9=-104,yD=295,I4=126468,Z9=1255,xg=120628,J7="pattern_object_property_string_literal_key",rg=8521,gD="leadingComments",_D=8204,eo="@ ",eg=70319,ns="left",wD=188,C4="case",tg=19967,N4=42622,ng=43492,ug=113770,ig=42774,O4=183,j4=8468,z7="class_implements",D4=126579,N3="string",bD=211,t2=-48,fg=69926,cg=123213,K7="if_consequent_statement",ag=124927,O3="number",sg=126546,og=68119,vg=70726,F4=70750,lg=65489,TD="SpreadElement",ED="callee",AD=193,pg=70492,kg=71934,SD=164,mg=110580,dg=12320,R4="any",ve="/",H7="type_guard",C1="body",PD=178,Ne="pattern",ID="comment_bounds",CD=297,V7="binding_type_identifier",M4=187,$7="pattern_array_rest_element_pattern",L4="@])",hg=12543,yg=11623,ND="start",gg=67871,le="interface",_g=8449,wg=67637,bg=42961,q4=120085,Tg=126463,OD="alternate",jD=-1053382366,Eg=70143,DD="--",Ag=68031,W7="jsx_expression",Q7="type_identifier_reference",U4=11647,Sg="proto",Ft="identifier",Pg=43696,Rt="raw",Ig=126529,Cg=11564,B4=126557,Ng=64911,X4=67592,Og=43493,jg=215,Dg=110588,n6=461894857,Fg=92927,Rg=67861,Mg=119980,Lg=43042,qg=66965,Ug=67391,j3="computed",FD="unreachable jsxtext",Bg=71167,Xg=42559,Gg=72966,RD=180,MD=197,G4=64319,LD=169,qD="*",uv=129,Yg=66335,u6="meta",Jg=43388,Y4=94178,pt="optional",J4="unknown",zg=120121,Kg=123180,z4=8469,Hg=68220,UD="|",Vg=43187,$g=94207,Wg=124895,K4=120513,Qg=42527,iv=8286,Zg=94177,i6="var",Z7="component_type_param",x_=66421,BD=285,r_=92991,e_=68415,xu="comment",ru="match_pattern_array_element",fv=244,H4="^",t_=173791,XD=136,n_=42890,u_="ENOTDIR",i_="??",f_=43711,c_=66303,a_=113800,s_=42239,o_=12703,eu="variance_opt",tu="+",GD=">>>=",V4="mixed",v_=65613,l_=73029,p_=68191,YD="*=",$4=8487,k_=8477,nu="toplevel_statement_list",W4="never",Q4="do",to=125,m_=72249,JD="Pervasives.do_at_exit",zD="visit_trailing_comment",uu="jsx_closing_element",iu="jsx_namespaced_name",d_=124908,h_=126651,fu="component_declaration",y_=15,cu="interface_type",au="function_type_return_annotation",g_=64109,Z4=65595,xk=126560,__=110927,KD=301,rk=65598,ek=8488,us="`.",HD=154,VD=175,tk="package",nk="else",uk=120771,w_=68023,$D="fd ",cv=8238,ik=888960333,fk=119965,b_=42655,su="match_object_pattern",T_=11710,E_=119993,ou="boolean_literal",WD=290,vu="statement_list",lu="function_param",pu="match_as_pattern",ku="pattern_object_property_bigint_literal_key",ck=69959,A_=120485,QD=240,S_=191456,mu="declare_enum",ak=120597,sk=70281,du="type_annotation",hu="spread_element",ok=126544,P_=120069,is="key",I_=43583,C_="out",N_=`
`,ZD="**=",yu="pattern_object_property_pattern",O_="e",j_=72712,xF="Internal Error: Found object private prop",D_="ENOENT",F_=-42,gu="jsx_opening_attribute",R_=67646,_u="component_type",M_=64296,L_=43887,rF="Division_by_zero",eF="EnumDefaultedMember",wu="typeof_member_identifier",q_=43792,bu="match_member_pattern_property",Tu="declare_export_declaration_decl",U_=93026,Eu="type_annotation_hint",B_=42887,X_=43881,G_=43761,vk=8526,D3=119,Y_=43866,J_=72847,z_=8348,pe=101,K_=94026,lk=72272,tF="src/parser/flow_lexer.ml",H_=120744,av=8191,F3="implies",pk=255,kk=11711,Au="match_unary_pattern_argument",V_=71235,mk=68116,nF=261,E1=100,Su="match_expression",Pu="enum_body",dk=1114111,Iu="assignment",$_=71955,hk=43260,Cu="pattern_array_e",W_=126583,uF="prefix",Nu="class_body",f6="shorthand",yk=171,Q_=66256,gk=-97,iF=" =",Z_=94032,xw=42606,Ou="match_case",rw=71839,_k=120134,ew=55291,tw=92862,nw=43019,uw=126543,R3="function",iw=111355,fw=11389,cw=70753,aw=43249,sw=64829,wk="line",ju="function_declaration",bk="undefined",fF="([^/]+)",ow=110947,vw=70002,cF="Cygwin",Du="as_expression",lw=12591,Tk=64285,pw=2048,kw=73112,Ek=126589,aF=225,Ak=43259,mw=72817,Sk=64318,dw=172,sF=209,Fu="match_binding_pattern",Ru=" ",Mu="import_source",c6="delete",oF="Enum `",Pk=126553,hw=67001,sv="default",yw=11630,Ik=206,Lu="enum_bigint_member",gw=67504,Ck=67593,_w=113791,vF="MatchObjectPatternProperty",ww=69572,qu="typeof_type",lF=212,pF="%i",Uu="function_this_param",bw=72329,no="0x",ov=8239,Tw=75075,kF=57343,Bu="pattern_bigint_literal",Ew=12341,mF=201,vv="hook",dF=": closedir failed",Aw=42959,Nk=119970,hF=278,Sw=43560,yF="||=",Xu="member_private_name",Pw=120570,Gu="object_key_identifier",Ok=223,gF="Not_found",_F=230,Yu="jsx_element_name_member_expression",Ju="string_literal",Iw=120596,Cw=43807,Nw=69687,Ow=63743,jk=72192,zu="member_property",jw=43262,Ku="class_declaration",wF="renders*",bF="%Li",Dw=126578,Hu="jsx_attribute",M3=254,Oe="empty",a6="label",Vu="object_internal_slot_property_type",Dk=120133,Fw=43359,He="predicate",TF="??=",Rw=43697,Mw=-43,$u="default_opt",EF="the start of a statement",Lw=67826,Wu="object_",Qu="class_element",Fk=11631,Rk=70855,Zu="opaque_type",xi="number_literal",AF=", ",Mk=8319,Lk=120004,qk=133,ri="type_params",ei="pattern_object_rest_property",W2="import",qw=72e3,Uw=67413,Bw=12343,Xw=70080,ti="intersection_type",d2=-36,Gw=70005,Uk="properties",Yw=11679,Jw=8483,zw=110587,SF=43520,ni="computed_key",PF=207,ui="class_identifier",Kw="Invalid number ",ii="function_param_pattern",lv=12288,Hw=113817,Vw=70730,$w=178207,Bk=71236,IF=167,fi="object_indexer_property_type",Ww=64286,CF="TypeAnnotation",NF=220,ci="type_identifier",ai="spread_property",si="jsx_attribute_value_expression",Qw=126519,Xk=70108,Gk=126,Yk=42999,uo="prototype",Zw=" : flags Open_text and Open_binary are not compatible",OF="**",Jk=43823,xb=": Not a directory",oi="render_type",zk=72349,L3="test",rb=43776,eb=92879,tb=11263,jF=241,nb=93052,vi="nullable_type",ub=43704,ib=64321,DF="Property",fb=72191,FF=165,s6="instanceof",cb=69247,Ve="name",Kk=126634,ab=8516,Hk="typeArguments",sb=71127,li="jsx_spread_attribute",ob=66559,vb=44031,lb=43645,n2=8233,pb=71494,kb="opaque",Vk=72967,mb=70106,pi="logical",RF="@[%s =@ ",o6="0o",$k=126554,db=71351,Wk=8484,hb=72242,Qk=120687,q3=252,yb=183983,v6="%S",ki="function_this_param_type",Zk="decorators",gb=43255,mi="catch_clause",$e="-",_b=67711,MF=": file descriptor already closed",xm=64311,rm=120539,wb="arguments",em=73062,bb=173823,Tb=42124,Eb=72095,Ab=125259,Sb=42969,tm=70280,LF=12520,Pb=69749,Ib=70066,di="binary",hi="for_in_statement",Cb=43010,qF="^=",Nb=126570,yi="for_statement",nm=126584,gi="function_return_annotation",Ob=72144,jb=8505,_i="class_expression",Db=120076,Fb=69807,Rb=40981,Mb=-24976191,Lb=72768,qb=126550,um='"',wi="call_type_arg",UF="f",pv="this",im=126628,BF="===",XF=56320,bi="declare_module_exports",Ub=120512,vn=105,Bb=119974,Xb=71450,Gb=71942,GF=195,fm=120629,YF="/=",JF=">>",Ti="declare_interface",zF=4096,Ei="pattern_array_rest_element",Yb=71338,cm=126520,Ai="as_const_expression",KF="Popping lex mode from empty stack",HF="renders?",Jb=68405,Si="member",Pi="class_extends",kv=12287,am=126590,zb=66377,io="async",Ii="pattern_array_element",U3=240,Kb=69864,mv="readonly",Hb=70460,Vb=120779,$b=66378,Ci="new_",sm=126551,Ni="pattern_object_rest_property_pattern",Oi="for_statement_init",Wb=43595,om=68296,Qb=120712,Zb=64217,xT=69295,VF="||",rT=";",eT=70461,tT=66939,$F="collect_comments",WF=279,ji="generic_type",nT=68295,uT=44002,vm=72162,Di="object_call_property_type",lm=8305,pm=119995,km="with",Fi="class_property",QF="qualification",Ri="jsx_attribute_name_namespaced",Mi="if_statement",Li="typeof_qualified_identifier",ZF=238,iT=65615,xR=176,u2="expression",mm=126559,qi="jsx_attribute_value",Ui="<2>",Bi="component_param",dm="Map.bal",hm=132,fT=70412,cT=70440,rR="<<",ym="finally",eR="v",Xi="syntax_opt",Gi="meta_property",aT=12447,sT=67514,gm=12448,Yi="object_mapped_type_property",dv="operator",tR="closedir",Ji="unary_expression",oT=126588,vT=70851,zi="export_batch_specifier",B3="renders",nR=226,lT=73111,uR=221,tx="",pT=66927,kT=64967,mT="elements",dT=67640,hT=43754,Ki="declare_export_declaration",yT=-26065557,gT=65855,l6="boolean",fs="typeof",_T=124902,iR=139,wT=65629,fR=224,bT=43123,_m=70449,TT=12735,q1=107,wm=11719,cR="!=",Hi="call_type_args",X3="asserts",fo=-46,ET="namespace",Vi="match_pattern",$i="for_of_statement_lhs",bm=126504,AT=69505,Tm="for",ST=72703,Em=120127,Am=43471,PT=93047,aR="Undefined_recursive_module",sR=2147483647,Wi="template_literal_element",oR="Unexpected ",IT=101631,CT=65497,Sm=68120,Qi="import_default_specifier",ln="array",vR="expressions",NT=110930,lR=204,Zi="while_",xf="function_rest_param_type",co=63,OT=77808,pR="Unexpected token `",br=114,rf="pattern_object_p",jT=65140,DT=123190,ef="pattern_object_property_number_literal_key",p6="enum",tf="conditional_type",ke=113,nf="array_type",kR="minus",FT=43790,uf="do_while",RT=11567,MT=11694,k6=256,LT=119976,ff="component_body",U2=111,qT=177976,mR=-56,Pm=67644,UT=73439,m6=951901561,dR="?",hR=")",Im=43867,Cm=65575,BT=69445,yR="FunctionTypeParam",Nm=119996,XT=65019,cf="conditional",GT=11505,gR=135,YT=71295,JT=12799,zT=67382,af="type_guard_annotation",sf="object_key_computed",pn=123,of="pattern_object_property_key",KT=119892,HT=67505,VT=66962,vf="with_",$T=43273,lf="interface_declaration",Om="bool",WT=71945,QT="declaration",ZT=11519,d6=">",xE=66771,jm="}",_R=8472,rE=43014,pf="declare_function",Jr=127,eE="RestElement",tE=190,nE=8467,wR="module",Dm=126522,bR="Sys_blocked_io",kf="jsx_opening_element",mf="object_key_number_literal",TR="|=",ER="mixins",AR=205,SR=217,Fm="if",PR="+=",df="match_object_pattern_property_key",hf="match_rest_pattern",yf="export_named_declaration_specifier",Rm="try",Mm="_bigarr02",uE=70479,kn="right",iE=245,fE=11718,gf="tuple_labeled_element",IR="TypeParameterInstantiation",cE="mkdir",aE=71999,sE=870530776,CR="@[",NR=-908856609,OR=331416730,oE=11670,vE=66735,lE=43709,Lm=43642,pE=67002,kE=69375,_f="function_body_any",mE=119807,jR="Assert_failure",wf="function_identifier",dE=65479,h6=131,hv="new",bf="for_of_left_declaration",hE=120084,yE=100343,gE=73030,qm=70452,DR=134,_E=253,wE=42954,FR=227,Tf="jsx_member_expression_object",Ef="class_property_value",bE=120144,RR=314,TE=66994,G3="set",EE=126498,Af="tuple_element",Sf="arg_list",AE=65481,SE=8511,PE=42964,IE=11492,Y3=-25,Um=126555,CE=71039,NE="exportKind",Pf="program",OE=70187,MR=173,Mt="as",J3=124,LR="visit_leading_comment",jE=110575,If="class_",DE=72440,FE=67897,qR=235,RE=8543,ME=141,Cf=120,Nf="match_object_pattern_property",y6=1024,LE=101640,UR=1027,BR=236,z3=246,XR="(",qE=66511,Of="regexp_literal",UE=65574,BE=43513,XE=43695,GR="&&",Bm=11558,GE=66503,YE=93071,jf="pattern_expression",JE=65381,Xm=126538,zE=12292,Df="import_namespace_specifier",KE=67583,HE=120137,VE=69622,$E=120770,WE=71131,yv=8287,QE=110590,ZE=65135,xA="Fatal error: exception ",g6=118,rA=181,Gm=11687,h2="camlinternalFormat.ml",eA=72959,tA=249,Ff="union_type",YR=8206,nA=73064,uA=70271,iA=92728,Ym=65344,Jm=11695,Rf="class_decorator",JR="the end of an expression statement (`;`)",fA=177983,cA=8457,zR=931,aA=66499,sA=94175,KR="#",oA="Identifier",Mf="for_in_statement_lhs",Lf="pattern_string_literal",zm=70302,Km=126496,vA=66461,lA=82943,Hm=8450,pA=72271,kA=70853,mA="of",HR="Stack_overflow",_6="hasUnknownMembers",w6="a",qf="variable_declarator_pattern",dA=73061,hA=77711,Vm=64317,yA=73097,VR=269,Uf="enum_declaration",gA=66966,_A=189,wA=119964,Bf="type_param",mn=782176664,$m=65535,$R=-10,bA=64433,Wm=43815,Qm=94031,Zm=73065,TA=69958,x8="property",Xf="jsx_children",Gf="member_property_identifier",EA=42537,ao="const",AA=70278,Yf="enum_string_member",b6="local",Jf="jsx_element_name_identifier",SA=68223,r8="",PA=119967,e8=119994,IA=66993,zf="jsx_member_expression_identifier",t8="explicitType",CA=67589,NA=65597,OA="exported",jA=94111,DA=113775,Kf="object_spread_property_type",FA=64847,Hf="component_identifier",Vf="class_implements_interface",WR=162,QR=243,RA=12783,ZR=`Fatal error: exception %s
`,n8=120093,T6="column",$f="component_rest_param",MA=70451,LA=70312,qA=69967,u8=70279,UA=66463,BA=92975,i8=70286,Wf="pattern_object_property_computed_key",Qf="object_key_string_literal",XA="jsError",Zf="type_args",GA=8304,xM="==",gv=115,xc="declare_component",YA=120092,JA=43638,zA=66811,KA=43334,HA=66863,VA=77823,rM=143,rc="optional_call",$A=126562,f8=70162,We=104,WA=66963,_v="await",c8=70107,Q2="0",QA=72250,ZA=8507,eM=291,xS=100351,a8="AssignmentPattern",ec="type",tM="%u",rS="NonNullExpression",tc="function_expression_or_method",eS=43470,nM=242,uM="camlinternalMod.ml",nc="match_or_pattern",tS=72750,nS=69414,uS=65370,uc="syntax",iM=32752,iS=42963,fM="End_of_file",fS=12294,cS=8471,cM="elementType",aS=43782,aM="++",sS=43641,oS=71944,vS=126601,lS=78894,pS=-45,wv="null",kS=177,sM="satisfies",mS=131071,ic="import_specifier",fc="class_method",cc="type_",dS=126514,hS=8454,oM="inexact",yS=67807,gS=8525,_S=65470,wS=71352,ac="tuple_spread_element",vM=219,bS="abstract",TS=73458,Qe="return",E6=65536,s8=126548,sc="array_element",ES=-*********,AS=186,o8="catch",oc="infer_type",SS=12295,lM="Invalid legacy octal ",PS=69762,IS=43311,CS=65437,vc="variable_declaration",pM=-*********,lc="function_params",kM=307,NS=64316,mM=311,v8=11565,dM="infinity",OS="@]",jS=65908,pc="extends",DS=66204,FS=43784,RS=11742,l8=126503,Ze="debugger",MS=70457,cs=-86,A6=*********,LS=68786,p8="keyof",k8=69415,qS=12686,dn=*********,kc="declare_type_alias",hM="the",yM=233,mc="jsx_element_name_namespaced",US=72283,gM=161,dc="class_static_block",hc="function_param_type",Lt=128,BS=-*********,m8=126591,_M="Sys_error",XS=74649,GS=74862,S6="is",YS=43738,JS=68479,wM=196,d8=70854,yc="enum_boolean_member",h8=72163,zS=92783,bM=281,gc="component_param_name",KS=68863,hn=32768,TM=2048,HS=64284,EM="@{",VS="\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",y8=8455,_c="update_expression",$S=65500,P6="from",WS=68447,g8=12592,QS=92766,AM=">>=",B2=110,ZS=66431,xP=43586,wc="jsx_identifier",rP=" : file already exists",U1=128,eP=71958,tP=66717,bc="enum_boolean_body",nP=64262,$r="id",Tc="component_renders_annotation",uP=42888,iP=8584,fP=73008,SM=306,Ec="enum_symbol_body",Ac="declare_namespace",_8=72713,cP=55215,Sc="object_property_value_type",Pc="match_wildcard_pattern",Ic="for_in_assignment_pattern",w8=8485,aP=43395,PM=229,as="true",sP=43743,Cc="enum_number_member",IM=234,oP=72969,CM="expected *",E2=102,NM=200,I6="symbol",bv="source",Nc="tparam_const_modifier",vP=43714,Oc="jsx_fragment",jc="jsx_attribute_name_identifier",C6="public",lP=43442,Dc="pattern_object_property",pP=65786,kP=70783,mP=43713,dP=72160,OM="*-/",Fc="export_named_specifier",Rc="arrow_function",hP=122623,b8=70006,jM="${",yP=43814,Mc="generic_qualified_identifier_type",DM=199,Lc="jsx_spread_child",T8=8489,E8=184,FM=2047,gP=66955,qc="try_catch",_P=70497,RM=313,MM=237,wP=67431,bP=125183,LM=-602162310,yn="params",TP="consequent",EP=68029,AP=67829,SP=68095,Uc="enum_string_body",PP=93823,IP=68351,CP=65495,Bc="declare_module",Xc="match_as_pattern_target",Gc="body_expression",NP=66175,A8=191,S8=70441,P8=65141,I8="&",Yc="super_expression",C8=126564,OP=72105,tS0="fs",xt="throw",jP=68287,DP=67839,Tv=116,FP=110882,RP=69404,MP=123197,Ev=65279,K3="src/parser/type_parser.ml",LP=68115,N8=126547,O8=126556,qP=73055,Jc="member_property_expression",zc="enum_defaulted_member",UP=43071,BP=11726,Kc="component_type_rest_param",XP=68607,Hc="object_key",GP=160,Z2="variance",YP=70655,JP=70414,H3="super",zP=123583,KP=65594,N6="method",HP=73648,O6=121,VP=93951,Vc="pattern_array_element_pattern",$P=43764,WP=42993,j8=120145,QP=74879,qM=168,D8=8486,ZP=72001,$c="tagged_template",Wc="module_ref_literal",xI=65312,Av="implements",rI=43700,eI=120003,UM="Invalid_argument",BM=262,Qc=16777215,tI=83526,F8=69744,R8=12336,Zc="switch_case",XM=-61,xa="optional_member",nI=64274,M8=64322,L8=126530,uI=71998,q8=72970,iI=13311,fI=73647,cI=120074,V3="let",GM="global",ra="expression_statement",ea="component_type_params",aI=512,sI=69634,oI=67461,vI=123627,lI=64913,YM="children",JM="PropertyDefinition",zM=1026,KM="%li",ta="declare_class",pI=43258,na="indexed_access_type",kI=124926,ss=112,mI="b",ua="predicate_expression",ia="if_alternate_statement",j6="private",HM=-594953737,VM=140,dI="nan",hI=72103,U8=11735,fa="statement",yI="rmdir",B8=66512,gI="match",_I=198,wI=11734,ca="import_named_specifier",bI=69599,TI=68799,EI=194559,aa="match_array_pattern",$M=174,sa="function_",oa="bigint_literal",f1=248,X8=67638,G8=126539,AI=11557,WM=214,SI=5760,rt="break",gn="block",va="match_member_pattern",PI=123565,II=66815,A1="value",QM=1039100673,CI=69746,NI=70448,OI=74751,la="init",jI=69551,Y8=65548,pa="jsx_member_expression",J8=68096,Sv=108,z8=126521,DI=71487,ka="match_statement",FI=178205,RI=12548,ZM=" : is a directory",_n=".",MI=12348,$3=-835925911,xe="typeParameters",LI=66855,i2="typeAnnotation",Pv="bigint",ma="jsx_attribute_value_literal",qI=194,xL="T_JSX_TEXT",UI=68466,K8=126537,rL=67714067,BI=69487,H8="export",XI=43822,V8=126499,GI=55242,da="member_type_identifier",eL=138,YI=71679,D6=130,JI=12438,zI=119969,$8=12539,KI=119972,tL=",",HI=71423,VI="index out of bounds",kt=106,W3="%d",nL="T_RENDERS_QUESTION",W8=120571,Q8="returnType",$I=69423,Z8=120070,uL="%",Q3=117,iL=179,WI="EBADF",QI=93759,xd=64325,ha="component_params",ZI=66517,xC=67423,rC=605857695,eC=43518,fL=251,ya="for_of_statement",tC=71983,cL="~",nC=12442,et="switch",uC=66207,rd=126535,aL="&&=",iC=69289,fC=71723,ga="generic_identifier_type",cC=126619,_a="object_type_property_setter",aC=70418,sL="<=",sC=125251,oC=11702,wa="enum_number_body",Z3=250,vC=124910,lC=69297,pC=67455,kC=42511,ba="ts_satisfies",mC=68324,ed="an identifier",dC=126534,wn=103,hC=120126,oL=274,xl=449540197,F6="declare",yC=68899,gC=126502,Ta="function_expression",vL=142,_C=123135,wC=67967,bC=120487,TC=120686,Ea="export_named_declaration",EC=66348,td=119981,AC=12352,Aa="tuple_type",SC=68680,nd="target",Sa="call";function BY(x,r,e,t,u){if(t<=r)for(var i=1;i<=u;i++)e[t+i]=x[r+i];else for(var i=u;i>=1;i--)e[t+i]=x[r+i];return 0}function XY(x){for(var r=[0];x!==0;){for(var e=x[1],t=1;t<e.length;t++)r.push(e[t]);x=x[2]}return r}function GY(x,r,e){var t=new Array(e+1);t[0]=0;for(var u=1,i=r+1;u<=e;u++,i++)t[u]=x[i];return t}function ud(x,r,e){return x[1]===r?(x[1]=e,1):0}function YY(x,r){var e=x[1];return x[1]+=r,e}function rl(x){return x[1]}function lL(x){var r=a0.process;if(r&&r.env&&r.env[x]!=null)return r.env[x];if(a0.jsoo_static_env&&a0.jsoo_static_env[x])return a0.jsoo_static_env[x]}var PC=0;(function(){var x=lL("OCAMLRUNPARAM");if(x!==void 0)for(var r=x.split(tL),e=0;e<r.length;e++)if(r[e]==mI){PC=1;break}else if(r[e].startsWith("b="))PC=+r[e].slice(2);else continue})();var re=[0];function JY(x,r){return(!x.js_error||r||x[0]==f1)&&(x.js_error=new a0.Error("Js exception containing backtrace")),x}function z0(x,r){return PC?JY(x,r):x}function zY(x,r){throw z0([0,x,r])}function nS0(x){return x}function IC(x,r){zY(x,r)}function f2(x){IC(re.Invalid_argument,x)}function pL(x){switch(x){case 7:case 10:case 11:return 2;default:return 1}}function kL(x,r){var e;switch(x){case 0:e=Float32Array;break;case 1:e=Float64Array;break;case 2:e=Int8Array;break;case 3:e=Uint8Array;break;case 4:e=Int16Array;break;case 5:e=Uint16Array;break;case 6:e=Int32Array;break;case 7:e=Int32Array;break;case 8:e=Int32Array;break;case 9:e=Int32Array;break;case 10:e=Float32Array;break;case 11:e=Float64Array;break;case 12:e=Uint8Array;break}e||f2("Bigarray.create: unsupported kind");var t=new e(r*pL(x));return t}function id(x){for(var r=x.length,e=1,t=0;t<r;t++)x[t]<0&&f2("Bigarray.create: negative dimension"),e=e*x[t];return e}var mL=Math.pow(2,-24);function dL(x){throw x}function hL(){dL(re.Division_by_zero)}function nr(x,r,e){this.lo=x&Qc,this.mi=r&Qc,this.hi=e&on}nr.prototype.caml_custom="_j",nr.prototype.copy=function(){return new nr(this.lo,this.mi,this.hi)},nr.prototype.ucompare=function(x){return this.hi>x.hi?1:this.hi<x.hi?-1:this.mi>x.mi?1:this.mi<x.mi?-1:this.lo>x.lo?1:this.lo<x.lo?-1:0},nr.prototype.compare=function(x){var r=this.hi<<16,e=x.hi<<16;return r>e?1:r<e?-1:this.mi>x.mi?1:this.mi<x.mi?-1:this.lo>x.lo?1:this.lo<x.lo?-1:0},nr.prototype.neg=function(){var x=-this.lo,r=-this.mi+(x>>24),e=-this.hi+(r>>24);return new nr(x,r,e)},nr.prototype.add=function(x){var r=this.lo+x.lo,e=this.mi+x.mi+(r>>24),t=this.hi+x.hi+(e>>24);return new nr(r,e,t)},nr.prototype.sub=function(x){var r=this.lo-x.lo,e=this.mi-x.mi+(r>>24),t=this.hi-x.hi+(e>>24);return new nr(r,e,t)},nr.prototype.mul=function(x){var r=this.lo*x.lo,e=(r*mL|0)+this.mi*x.lo+this.lo*x.mi,t=(e*mL|0)+this.hi*x.lo+this.mi*x.mi+this.lo*x.hi;return new nr(r,e,t)},nr.prototype.isZero=function(){return(this.lo|this.mi|this.hi)==0},nr.prototype.isNeg=function(){return this.hi<<16<0},nr.prototype.and=function(x){return new nr(this.lo&x.lo,this.mi&x.mi,this.hi&x.hi)},nr.prototype.or=function(x){return new nr(this.lo|x.lo,this.mi|x.mi,this.hi|x.hi)},nr.prototype.xor=function(x){return new nr(this.lo^x.lo,this.mi^x.mi,this.hi^x.hi)},nr.prototype.shift_left=function(x){return x=x&63,x==0?this:x<24?new nr(this.lo<<x,this.mi<<x|this.lo>>24-x,this.hi<<x|this.mi>>24-x):x<48?new nr(0,this.lo<<x-24,this.mi<<x-24|this.lo>>48-x):new nr(0,0,this.lo<<x-48)},nr.prototype.shift_right_unsigned=function(x){return x=x&63,x==0?this:x<24?new nr(this.lo>>x|this.mi<<24-x,this.mi>>x|this.hi<<24-x,this.hi>>x):x<48?new nr(this.mi>>x-24|this.hi<<48-x,this.hi>>x-24,0):new nr(this.hi>>x-48,0,0)},nr.prototype.shift_right=function(x){if(x=x&63,x==0)return this;var r=this.hi<<16>>16;if(x<24)return new nr(this.lo>>x|this.mi<<24-x,this.mi>>x|r<<24-x,this.hi<<16>>x>>>16);var e=this.hi<<16>>31;return x<48?new nr(this.mi>>x-24|this.hi<<48-x,this.hi<<16>>x-24>>16,e&on):new nr(this.hi<<16>>x-32,e,e)},nr.prototype.lsl1=function(){this.hi=this.hi<<1|this.mi>>23,this.mi=(this.mi<<1|this.lo>>23)&Qc,this.lo=this.lo<<1&Qc},nr.prototype.lsr1=function(){this.lo=(this.lo>>>1|this.mi<<23)&Qc,this.mi=(this.mi>>>1|this.hi<<23)&Qc,this.hi=this.hi>>>1},nr.prototype.udivmod=function(x){for(var r=0,e=this.copy(),t=x.copy(),u=new nr(0,0,0);e.ucompare(t)>0;)r++,t.lsl1();for(;r>=0;)r--,u.lsl1(),e.ucompare(t)>=0&&(u.lo++,e=e.sub(t)),t.lsr1();return{quotient:u,modulus:e}},nr.prototype.div=function(x){var r=this;x.isZero()&&hL();var e=r.hi^x.hi;r.hi&hn&&(r=r.neg()),x.hi&hn&&(x=x.neg());var t=r.udivmod(x).quotient;return e&hn&&(t=t.neg()),t},nr.prototype.mod=function(x){var r=this;x.isZero()&&hL();var e=r.hi;r.hi&hn&&(r=r.neg()),x.hi&hn&&(x=x.neg());var t=r.udivmod(x).modulus;return e&hn&&(t=t.neg()),t},nr.prototype.toInt=function(){return this.lo|this.mi<<24},nr.prototype.toFloat=function(){return(this.hi<<16)*Math.pow(2,32)+this.mi*Math.pow(2,24)+this.lo},nr.prototype.toArray=function(){return[this.hi>>8,this.hi&Za,this.mi>>16,this.mi>>8&Za,this.mi&Za,this.lo>>16,this.lo>>8&Za,this.lo&Za]},nr.prototype.lo32=function(){return this.lo|(this.mi&Za)<<24},nr.prototype.hi32=function(){return this.mi>>>8&on|this.hi<<16};function KY(x,r){return new nr(x&Qc,x>>>24&Za|(r&on)<<8,r>>>16&on)}function CC(x){return x.hi32()}function NC(x){return x.lo32()}function R6(){f2(VI)}var HY=Mm;function so(x,r,e,t){this.kind=x,this.layout=r,this.dims=e,this.data=t}so.prototype.caml_custom=HY,so.prototype.offset=function(x){var r=0;if(typeof x=="number"&&(x=[x]),x instanceof Array||f2("bigarray.js: invalid offset"),this.dims.length!=x.length&&f2("Bigarray.get/set: bad number of dimensions"),this.layout==0)for(var e=0;e<this.dims.length;e++)(x[e]<0||x[e]>=this.dims[e])&&R6(),r=r*this.dims[e]+x[e];else for(var e=this.dims.length-1;e>=0;e--)(x[e]<1||x[e]>this.dims[e])&&R6(),r=r*this.dims[e]+(x[e]-1);return r},so.prototype.get=function(x){switch(this.kind){case 7:var r=this.data[x*2+0],e=this.data[x*2+1];return KY(r,e);case 10:case 11:var t=this.data[x*2+0],u=this.data[x*2+1];return[M3,t,u];default:return this.data[x]}},so.prototype.set=function(x,r){switch(this.kind){case 7:this.data[x*2+0]=NC(r),this.data[x*2+1]=CC(r);break;case 10:case 11:this.data[x*2+0]=r[1],this.data[x*2+1]=r[2];break;default:this.data[x]=r;break}return 0},so.prototype.fill=function(x){switch(this.kind){case 7:var r=NC(x),e=CC(x);if(r==e)this.data.fill(r);else for(var t=0;t<this.data.length;t++)this.data[t]=t%2==0?r:e;break;case 10:case 11:var u=x[1],i=x[2];if(u==i)this.data.fill(u);else for(var t=0;t<this.data.length;t++)this.data[t]=t%2==0?u:i;break;default:this.data.fill(x);break}},so.prototype.compare=function(x,r){if(this.layout!=x.layout||this.kind!=x.kind){var e=this.kind|this.layout<<8,t=x.kind|x.layout<<8;return t-e}if(this.dims.length!=x.dims.length)return x.dims.length-this.dims.length;for(var u=0;u<this.dims.length;u++)if(this.dims[u]!=x.dims[u])return this.dims[u]<x.dims[u]?-1:1;switch(this.kind){case 0:case 1:case 10:case 11:for(var i,c,u=0;u<this.data.length;u++){if(i=this.data[u],c=x.data[u],i<c)return-1;if(i>c)return 1;if(i!=c){if(!r)return NaN;if(i==i)return 1;if(c==c)return-1}}break;case 7:for(var u=0;u<this.data.length;u+=2){if(this.data[u+1]<x.data[u+1])return-1;if(this.data[u+1]>x.data[u+1])return 1;if(this.data[u]>>>0<x.data[u]>>>0)return-1;if(this.data[u]>>>0>x.data[u]>>>0)return 1}break;case 2:case 3:case 4:case 5:case 6:case 8:case 9:case 12:for(var u=0;u<this.data.length;u++){if(this.data[u]<x.data[u])return-1;if(this.data[u]>x.data[u])return 1}break}return 0};function el(x,r,e,t){this.kind=x,this.layout=r,this.dims=e,this.data=t}el.prototype=new so,el.prototype.offset=function(x){return typeof x!="number"&&(x instanceof Array&&x.length==1?x=x[0]:f2("Ml_Bigarray_c_1_1.offset")),(x<0||x>=this.dims[0])&&R6(),x},el.prototype.get=function(x){return this.data[x]},el.prototype.set=function(x,r){return this.data[x]=r,0},el.prototype.fill=function(x){return this.data.fill(x),0};function OC(x,r,e,t){var u=pL(x);return id(e)*u!=t.length&&f2("length doesn't match dims"),r==0&&e.length==1&&u==1?new el(x,r,e,t):new so(x,r,e,t)}function yL(x){return x.slice(1)}function VY(x,r,e){var t=yL(e),u=kL(x,id(t));return OC(x,r,t,u)}function M6(x,r,e){return x.set(x.offset(r),e),0}function L6(x,r,e){var t=String.fromCharCode;if(r==0&&e<=zF&&e==x.length)return t.apply(null,x);for(var u=tx;0<e;r+=y6,e-=y6)u+=t.apply(null,x.slice(r,r+Math.min(e,y6)));return u}function fd(x){for(var r=new Uint8Array(x.l),e=x.c,t=e.length,u=0;u<t;u++)r[u]=e.charCodeAt(u);for(t=x.l;u<t;u++)r[u]=0;return x.c=r,x.t=4,r}function os(x,r,e,t,u){if(u==0)return 0;if(t==0&&(u>=e.l||e.t==2&&u>=e.c.length))e.c=x.t==4?L6(x.c,r,u):r==0&&x.c.length==u?x.c:x.c.substr(r,u),e.t=e.c.length==e.l?0:2;else if(e.t==2&&t==e.c.length)e.c+=x.t==4?L6(x.c,r,u):r==0&&x.c.length==u?x.c:x.c.substr(r,u),e.t=e.c.length==e.l?0:2;else{e.t!=4&&fd(e);var i=x.c,c=e.c;if(x.t==4)if(t<=r)for(var v=0;v<u;v++)c[t+v]=i[r+v];else for(var v=u-1;v>=0;v--)c[t+v]=i[r+v];else{for(var s=Math.min(u,i.length-r),v=0;v<s;v++)c[t+v]=i.charCodeAt(r+v);for(;v<u;v++)c[t+v]=0}}return 0}function tl(x,r){if(x==0)return tx;if(r.repeat)return r.repeat(x);for(var e=tx,t=0;;){if(x&1&&(e+=r),x>>=1,x==0)return e;r+=r,t++,t==9&&r.slice(0,1)}}function cd(x){x.t==2?x.c+=tl(x.l-x.c.length,"\0"):x.c=L6(x.c,0,x.c.length),x.t=0}function jC(x){if(x.length<24){for(var r=0;r<x.length;r++)if(x.charCodeAt(r)>Jr)return!1;return!0}else return!/[^\x00-\x7f]/.test(x)}function gL(x){for(var r=tx,e=tx,t,u,i,c,v=0,s=x.length;v<s;v++){if(u=x.charCodeAt(v),u<Lt){for(var l=v+1;l<s&&(u=x.charCodeAt(l))<Lt;l++);if(l-v>aI?(e.substr(0,1),r+=e,e=tx,r+=x.slice(v,l)):e+=x.slice(v,l),l==s)break;v=l}c=1,++v<s&&((i=x.charCodeAt(v))&-64)==U1&&(t=i+(u<<6),u<fR?(c=t-12416,c<Lt&&(c=1)):(c=2,++v<s&&((i=x.charCodeAt(v))&-64)==U1&&(t=i+(t<<6),u<QD?(c=t-925824,(c<TM||c>=55295&&c<57344)&&(c=2)):(c=3,++v<s&&((i=x.charCodeAt(v))&-64)==U1&&u<245&&(c=i-63447168+(t<<6),(c<65536||c>1114111)&&(c=3)))))),c<4?(v-=c,e+="\uFFFD"):c>on?e+=String.fromCharCode(55232+(c>>10),XF+(c&1023)):e+=String.fromCharCode(c),e.length>y6&&(e.substr(0,1),r+=e,e=tx)}return r+e}function vs(x,r,e){this.t=x,this.c=r,this.l=e}vs.prototype.toString=function(){switch(this.t){case 9:return this.c;default:cd(this);case 0:if(jC(this.c))return this.t=9,this.c;this.t=8;case 8:return this.c}},vs.prototype.toUtf16=function(){var x=this.toString();return this.t==9?x:gL(x)},vs.prototype.slice=function(){var x=this.t==4?this.c.slice():this.c;return new vs(this.t,x,this.l)};function _L(x){return new vs(0,x,x.length)}function uS0(x){return x}function qt(x){return _L(x)}function Pa(x,r,e,t,u){return os(qt(x),r,e,t,u),0}function nl(x){return new nr(x[7]<<0|x[6]<<8|x[5]<<16,x[4]<<0|x[3]<<8|x[2]<<16,x[1]<<0|x[0]<<8)}function me(x,r){switch(x.t&6){default:if(r>=x.c.length)return 0;case 0:return x.c.charCodeAt(r);case 4:return x.c[r]}}function DC(){f2(VI)}function $Y(x,r){r>>>0>=x.l-7&&DC();for(var e=new Array(8),t=0;t<8;t++)e[7-t]=me(x,r+t);return nl(e)}function zr(x,r,e){if(e&=Za,x.t!=4){if(r==x.c.length)return x.c+=String.fromCharCode(e),r+1==x.l&&(x.t=0),0;fd(x)}return x.c[r]=e,0}function ls(x,r,e){return r>>>0>=x.l&&DC(),zr(x,r,e)}function ul(x){return x.toArray()}function WY(x,r,e){r>>>0>=x.l-7&&DC();for(var t=ul(e),u=0;u<8;u++)zr(x,r+7-u,t[u]);return 0}function Ia(x,r){var e=x.l>=0?x.l:x.l=x.length,t=r.length,u=e-t;if(u==0)return x.apply(null,r);if(u<0){var i=x.apply(null,r.slice(0,e));return typeof i!="function"?i:Ia(i,r.slice(e))}else{switch(u){case 1:{var i=function(s){for(var l=new Array(t+1),p=0;p<t;p++)l[p]=r[p];return l[t]=s,x.apply(null,l)};break}case 2:{var i=function(s,l){for(var p=new Array(t+2),d=0;d<t;d++)p[d]=r[d];return p[t]=s,p[t+1]=l,x.apply(null,p)};break}default:var i=function(){for(var v=arguments.length==0?1:arguments.length,s=new Array(r.length+v),l=0;l<r.length;l++)s[l]=r[l];for(var l=0;l<arguments.length;l++)s[r.length+l]=arguments[l];return Ia(x,s)}}return i.l=u,i}}function N1(x,r){return r>>>0>=x.length-1&&R6(),x}function QY(x){return isFinite(x)?Math.abs(x)>=22250738585072014e-324?0:x!=0?1:2:isNaN(x)?4:3}function ZY(x){return x==iE?1:0}var xJ=Math.log2&&Math.log2(11235582092889474e291)==1020;function rJ(x){if(xJ)return Math.floor(Math.log2(x));var r=0;if(x==0)return-1/0;if(x>=1)for(;x>=2;)x/=2,r++;else for(;x<1;)x*=2,r--;return r}function FC(x){var r=new Float32Array(1);r[0]=x;var e=new Int32Array(r.buffer);return e[0]|0}function mt(x,r,e){return new nr(x,r,e)}function ad(x){if(!isFinite(x))return isNaN(x)?mt(1,0,iM):x>0?mt(0,0,iM):mt(0,0,65520);var r=x==0&&1/x==-1/0?hn:x>=0?0:hn;r&&(x=-x);var e=rJ(x)+1023;e<=0?(e=0,x/=Math.pow(2,-zM)):(x/=Math.pow(2,e-UR),x<16&&(x*=2,e-=1),e==0&&(x/=2));var t=Math.pow(2,24),u=x|0;x=(x-u)*t;var i=x|0;x=(x-i)*t;var c=x|0;return u=u&y_|r|e<<4,mt(c,i,u)}function wL(x,r,e){if(x.write(32,r.dims.length),x.write(32,r.kind|r.layout<<8),r.caml_custom==Mm)for(var t=0;t<r.dims.length;t++)r.dims[t]<on?x.write(16,r.dims[t]):(x.write(16,on),x.write(32,0),x.write(32,r.dims[t]));else for(var t=0;t<r.dims.length;t++)x.write(32,r.dims[t]);switch(r.kind){case 2:case 3:case 12:for(var t=0;t<r.data.length;t++)x.write(8,r.data[t]);break;case 4:case 5:for(var t=0;t<r.data.length;t++)x.write(16,r.data[t]);break;case 6:for(var t=0;t<r.data.length;t++)x.write(32,r.data[t]);break;case 8:case 9:x.write(8,0);for(var t=0;t<r.data.length;t++)x.write(32,r.data[t]);break;case 7:for(var t=0;t<r.data.length/2;t++)for(var u=ul(r.get(t)),i=0;i<8;i++)x.write(8,u[i]);break;case 1:for(var t=0;t<r.data.length;t++)for(var u=ul(ad(r.get(t))),i=0;i<8;i++)x.write(8,u[i]);break;case 0:for(var t=0;t<r.data.length;t++){var u=FC(r.get(t));x.write(32,u)}break;case 10:for(var t=0;t<r.data.length/2;t++){var i=r.get(t);x.write(32,FC(i[1])),x.write(32,FC(i[2]))}break;case 11:for(var t=0;t<r.data.length/2;t++){for(var c=r.get(t),u=ul(ad(c[1])),i=0;i<8;i++)x.write(8,u[i]);for(var u=ul(ad(c[2])),i=0;i<8;i++)x.write(8,u[i])}break}e[0]=(4+r.dims.length)*4,e[1]=(4+r.dims.length)*8}function RC(x){var r=new Int32Array(1);r[0]=x;var e=new Float32Array(r.buffer);return e[0]}function MC(x){var r=x.lo,e=x.mi,t=x.hi,u=(t&32767)>>4;if(u==FM)return(r|e|t&y_)==0?t&hn?-1/0:1/0:NaN;var i=Math.pow(2,-24),c=(r*i+e)*i+(t&y_);return u>0?(c+=16,c*=Math.pow(2,u-UR)):c*=Math.pow(2,-zM),t&hn&&(c=-c),c}function ee(x){re.Failure||(re.Failure=[f1,F5,-3]),IC(re.Failure,x)}function bL(x,r,e){var t=x.read32s();(t<0||t>16)&&ee("input_value: wrong number of bigarray dimensions");var u=x.read32s(),i=u&Za,c=u>>8&1,v=[];if(e==Mm)for(var s=0;s<t;s++){var l=x.read16u();if(l==on){var p=x.read32u(),d=x.read32u();p!=0&&ee("input_value: bigarray dimension overflow in 32bit"),l=d}v.push(l)}else for(var s=0;s<t;s++)v.push(x.read32u());var T=id(v),b=kL(i,T),C=OC(i,c,v,b);switch(i){case 2:for(var s=0;s<T;s++)b[s]=x.read8s();break;case 3:case 12:for(var s=0;s<T;s++)b[s]=x.read8u();break;case 4:for(var s=0;s<T;s++)b[s]=x.read16s();break;case 5:for(var s=0;s<T;s++)b[s]=x.read16u();break;case 6:for(var s=0;s<T;s++)b[s]=x.read32s();break;case 8:case 9:var N=x.read8u();N&&ee("input_value: cannot read bigarray with 64-bit OCaml ints");for(var s=0;s<T;s++)b[s]=x.read32s();break;case 7:for(var J=new Array(8),s=0;s<T;s++){for(var I=0;I<8;I++)J[I]=x.read8u();var F=nl(J);C.set(s,F)}break;case 1:for(var J=new Array(8),s=0;s<T;s++){for(var I=0;I<8;I++)J[I]=x.read8u();var L=MC(nl(J));C.set(s,L)}break;case 0:for(var s=0;s<T;s++){var L=RC(x.read32s());C.set(s,L)}break;case 10:for(var s=0;s<T;s++){var X=RC(x.read32s()),q=RC(x.read32s());C.set(s,[M3,X,q])}break;case 11:for(var J=new Array(8),s=0;s<T;s++){for(var I=0;I<8;I++)J[I]=x.read8u();for(var X=MC(nl(J)),I=0;I<8;I++)J[I]=x.read8u();var q=MC(nl(J));C.set(s,[M3,X,q])}break}return r[0]=(4+t)*4,OC(i,c,v,b)}function TL(x,r,e){return x.compare(r,e)}function EL(x,r){return Math.imul(x,r)}function ps(x,r){return r=EL(r,-862048943),r=r<<15|r>>>17,r=EL(r,461845907),x^=r,x=x<<13|x>>>19,(x+(x<<2)|0)+-430675100|0}function eJ(x,r){return x=ps(x,NC(r)),x=ps(x,CC(r)),x}function AL(x,r){return eJ(x,ad(r))}function SL(x){var r=id(x.dims),e=0;switch(x.kind){case 2:case 3:case 12:r>k6&&(r=k6);var t=0,u=0;for(u=0;u+4<=x.data.length;u+=4)t=x.data[u+0]|x.data[u+1]<<8|x.data[u+2]<<16|x.data[u+3]<<24,e=ps(e,t);switch(t=0,r&3){case 3:t=x.data[u+2]<<16;case 2:t|=x.data[u+1]<<8;case 1:t|=x.data[u+0],e=ps(e,t)}break;case 4:case 5:r>U1&&(r=U1);var t=0,u=0;for(u=0;u+2<=x.data.length;u+=2)t=x.data[u+0]|x.data[u+1]<<16,e=ps(e,t);(r&1)!=0&&(e=ps(e,x.data[u]));break;case 6:r>64&&(r=64);for(var u=0;u<r;u++)e=ps(e,x.data[u]);break;case 8:case 9:r>64&&(r=64);for(var u=0;u<r;u++)e=ps(e,x.data[u]);break;case 7:r>32&&(r=32),r*=2;for(var u=0;u<r;u++)e=ps(e,x.data[u]);break;case 10:r*=2;case 0:r>64&&(r=64);for(var u=0;u<r;u++)e=AL(e,x.data[u]);break;case 11:r*=2;case 1:r>32&&(r=32);for(var u=0;u<r;u++)e=AL(e,x.data[u]);break}return e}function tJ(x,r){return r[0]=4,x.read32s()}function nJ(x,r){switch(x.read8u()){case 1:return r[0]=4,x.read32s();case 2:ee("input_value: native integer value too large");default:ee("input_value: ill-formed native integer")}}function uJ(x,r){for(var e=new Array(8),t=0;t<8;t++)e[t]=x.read8u();return r[0]=8,nl(e)}function iJ(x,r,e){for(var t=ul(r),u=0;u<8;u++)x.write(8,t[u]);e[0]=8,e[1]=8}function fJ(x,r,e){return x.compare(r)}function cJ(x){return x.lo32()^x.hi32()}var PL={_j:{deserialize:uJ,serialize:iJ,fixed_length:8,compare:fJ,hash:cJ},_i:{deserialize:tJ,fixed_length:4},_n:{deserialize:nJ,fixed_length:4},_bigarray:{deserialize:function(x,r){return bL(x,r,"_bigarray")},serialize:wL,compare:TL,hash:SL},_bigarr02:{deserialize:function(x,r){return bL(x,r,Mm)},serialize:wL,compare:TL,hash:SL}};function LC(x){return PL[x.caml_custom]&&PL[x.caml_custom].compare}function IL(x,r,e,t){var u=LC(r);if(u){var i=e>0?u(r,x,t):u(x,r,t);if(t&&i!=i)return e;if(+i!=+i)return+i;if((i|0)!=0)return i|0}return e}function qC(x){return typeof x=="string"&&!/[^\x00-\xff]/.test(x)}function UC(x){return x instanceof vs}function CL(x){if(typeof x=="number")return e6;if(UC(x))return q3;if(qC(x))return 1252;if(x instanceof Array&&x[0]===x[0]>>>0&&x[0]<=pk){var r=x[0]|0;return r==M3?0:r}else{if(x instanceof String)return LF;if(typeof x=="string")return LF;if(x instanceof Number)return e6;if(x&&x.caml_custom)return Z9;if(x&&x.compare)return 1256;if(typeof x=="function")return 1247;if(typeof x=="symbol")return 1251}return 1001}function je(x,r){return x<r?-1:x==r?0:1}function sx(x,r){return x<r?-1:x>r?1:0}function aJ(x,r){return x.t&6&&cd(x),r.t&6&&cd(r),x.c<r.c?-1:x.c>r.c?1:0}function sd(x,r,e){for(var t=[];;){if(!(e&&x===r)){var u=CL(x);if(u==Z3){x=x[1];continue}var i=CL(r);if(i==Z3){r=r[1];continue}if(u!==i)return u==e6?i==Z9?IL(x,r,-1,e):-1:i==e6?u==Z9?IL(r,x,1,e):1:u<i?-1:1;switch(u){case 247:f2(o9);break;case 248:var v=je(x[2],r[2]);if(v!=0)return v|0;break;case 249:f2(o9);break;case 250:f2("equal: got Forward_tag, should not happen");break;case 251:f2("equal: abstract value");break;case 252:if(x!==r){var v=aJ(x,r);if(v!=0)return v|0}break;case 253:f2("equal: got Double_tag, should not happen");break;case 254:f2("equal: got Double_array_tag, should not happen");break;case 255:f2("equal: got Custom_tag, should not happen");break;case 1247:f2(o9);break;case 1255:var c=LC(x);if(c!=LC(r))return x.caml_custom<r.caml_custom?-1:1;c||f2("compare: abstract value");var v=c(x,r,e);if(v!=v)return e?-1:v;if(v!==(v|0))return-1;if(v!=0)return v|0;break;case 1256:var v=x.compare(r,e);if(v!=v)return e?-1:v;if(v!==(v|0))return-1;if(v!=0)return v|0;break;case 1e3:if(x=+x,r=+r,x<r)return-1;if(x>r)return 1;if(x!=r){if(!e)return NaN;if(x==x)return 1;if(r==r)return-1}break;case 1001:if(x<r)return-1;if(x>r)return 1;if(x!=r){if(!e)return NaN;if(x==x)return 1;if(r==r)return-1}break;case 1251:if(x!==r)return e?1:NaN;break;case 1252:var x=x,r=r;if(x!==r){if(x<r)return-1;if(x>r)return 1}break;case 12520:var x=x.toString(),r=r.toString();if(x!==r){if(x<r)return-1;if(x>r)return 1}break;case 246:case 254:default:if(ZY(u)){f2("compare: continuation value");break}if(x.length!=r.length)return x.length<r.length?-1:1;x.length>1&&t.push(x,r,1);break}}if(t.length==0)return 0;var s=t.pop();r=t.pop(),x=t.pop(),s+1<x.length&&t.push(x,r,s+1),x=x[s],r=r[s]}}function NL(x,r){return sd(x,r,!0)}function sJ(){return[0]}function I1(x){return x<0&&f2("Bytes.create"),new vs(x?2:9,tx,x)}var od=[0];function oJ(x,r){return od!==x?0:(od=r,1)}function OL(x){return od}function vJ(x){od=x}function il(x,r){return+(sd(x,r,!1)==0)}function lJ(x,r,e,t){if(e>0)if(r==0&&(e>=x.l||x.t==2&&e>=x.c.length))t==0?(x.c=tx,x.t=2):(x.c=tl(e,String.fromCharCode(t)),x.t=e==x.l?0:2);else for(x.t!=4&&fd(x),e+=r;r<e;r++)x.c[r]=t;return 0}function BC(x){var r;if(x=x,r=+x,x.length>0&&r===r||(x=x.replace(/_/g,tx),r=+x,x.length>0&&r===r||/^[+-]?nan$/i.test(x)))return r;var e=/^ *([+-]?)0x([0-9a-f]+)\.?([0-9a-f]*)(p([+-]?[0-9]+))?/i.exec(x);if(e){var t=e[3].replace(/0+$/,tx),u=parseInt(e[1]+e[2]+t,16),i=(e[5]|0)-4*t.length;return r=u*Math.pow(2,i),r}if(/^\+?inf(inity)?$/i.test(x))return 1/0;if(/^-inf(inity)?$/i.test(x))return-1/0;ee("float_of_string")}function XC(x){x=x;var r=x.length;r>31&&f2("format_int: format too long");for(var e={justify:tu,signstyle:$e,filler:Ru,alternate:!1,base:0,signedconv:!1,width:0,uppercase:!1,sign:1,prec:-1,conv:UF},t=0;t<r;t++){var u=x.charAt(t);switch(u){case"-":e.justify=$e;break;case"+":case" ":e.signstyle=u;break;case"0":e.filler=Q2;break;case"#":e.alternate=!0;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(e.width=0;u=x.charCodeAt(t)-48,u>=0&&u<=9;)e.width=e.width*10+u,t++;t--;break;case".":for(e.prec=0,t++;u=x.charCodeAt(t)-48,u>=0&&u<=9;)e.prec=e.prec*10+u,t++;t--;case"d":case"i":e.signedconv=!0;case"u":e.base=10;break;case"x":e.base=16;break;case"X":e.base=16,e.uppercase=!0;break;case"o":e.base=8;break;case"e":case"f":case"g":e.signedconv=!0,e.conv=u;break;case"E":case"F":case"G":e.signedconv=!0,e.uppercase=!0,e.conv=u.toLowerCase();break}}return e}function GC(x,r){x.uppercase&&(r=r.toUpperCase());var e=r.length;x.signedconv&&(x.sign<0||x.signstyle!=$e)&&e++,x.alternate&&(x.base==8&&(e+=1),x.base==16&&(e+=2));var t=tx;if(x.justify==tu&&x.filler==Ru)for(var u=e;u<x.width;u++)t+=Ru;if(x.signedconv&&(x.sign<0?t+=$e:x.signstyle!=$e&&(t+=x.signstyle)),x.alternate&&x.base==8&&(t+=Q2),x.alternate&&x.base==16&&(t+=x.uppercase?"0X":no),x.justify==tu&&x.filler==Q2)for(var u=e;u<x.width;u++)t+=Q2;if(t+=r,x.justify==$e)for(var u=e;u<x.width;u++)t+=Ru;return t}function YC(x,r){function e(p,d){if(Math.abs(p)<1)return p.toFixed(d);var T=parseInt(p.toString().split(tu)[1]);return T>20?(T-=20,p/=Math.pow(10,T),p+=new Array(T+1).join(Q2),d>0&&(p=p+_n+new Array(d+1).join(Q2)),p):p.toFixed(d)}var t,u=XC(x),i=u.prec<0?6:u.prec;if((r<0||r==0&&1/r==-1/0)&&(u.sign=-1,r=-r),isNaN(r))t=dI,u.filler=Ru;else if(!isFinite(r))t="inf",u.filler=Ru;else switch(u.conv){case"e":var t=r.toExponential(i),c=t.length;t.charAt(c-3)==O_&&(t=t.slice(0,c-1)+Q2+t.slice(c-1));break;case"f":t=e(r,i);break;case"g":i=i||1,t=r.toExponential(i-1);var v=t.indexOf(O_),s=+t.slice(v+1);if(s<-4||r>=1e21||r.toFixed(0).length>i){for(var c=v-1;t.charAt(c)==Q2;)c--;t.charAt(c)==_n&&c--,t=t.slice(0,c+1)+t.slice(v),c=t.length,t.charAt(c-3)==O_&&(t=t.slice(0,c-1)+Q2+t.slice(c-1));break}else{var l=i;if(s<0)l-=s+1,t=r.toFixed(l);else for(;t=r.toFixed(l),t.length>i+1;)l--;if(l){for(var c=t.length-1;t.charAt(c)==Q2;)c--;t.charAt(c)==_n&&c--,t=t.slice(0,c+1)}}break}return GC(u,t)}function vd(x,r){if(x==W3)return tx+r;var e=XC(x);r<0&&(e.signedconv?(e.sign=-1,r=-r):r>>>=0);var t=r.toString(e.base);if(e.prec>=0){e.filler=Ru;var u=e.prec-t.length;u>0&&(t=tl(u,Q2)+t)}return GC(e,t)}var jL=0;function Ca(){return jL++}function DL(){return[0]}var ld=[];function Kx(x,r,e){var t=x[1],u=ld[e];if(u===void 0)for(var i=ld.length;i<e;i++)ld[i]=0;else if(t[u]===r)return t[u-1];for(var c=3,v=t[1]*2+1,s;c<v;)s=c+v>>1|1,r<t[s+1]?v=s-2:c=s;return ld[e]=c+1,r==t[c+1]?t[c]:0}function pJ(x){for(var r=tx,e=r,t,u,i=0,c=x.length;i<c;i++){if(t=x.charCodeAt(i),t<Lt){for(var v=i+1;v<c&&(t=x.charCodeAt(v))<Lt;v++);if(v-i>aI?(e.substr(0,1),r+=e,e=tx,r+=x.slice(i,v)):e+=x.slice(i,v),v==c)break;i=v}t<TM?(e+=String.fromCharCode(192|t>>6),e+=String.fromCharCode(Lt|t&co)):t<55296||t>=kF?e+=String.fromCharCode(fR|t>>12,Lt|t>>6&co,Lt|t&co):t>=56319||i+1==c||(u=x.charCodeAt(i+1))<XF||u>kF?e+="\xEF\xBF\xBD":(i++,t=(t<<10)+u-56613888,e+=String.fromCharCode(QD|t>>18,Lt|t>>12&co,Lt|t>>6&co,Lt|t&co)),e.length>y6&&(e.substr(0,1),r+=e,e=tx)}return r+e}function Ut(x){return jC(x)?x:pJ(x)}function kJ(x,r,e){if(!isFinite(x))return isNaN(x)?Ut(dI):Ut(x>0?dM:"-infinity");var t=x==0&&1/x==-1/0?1:x>=0?0:1;t&&(x=-x);var u=0;if(x!=0)if(x<1)for(;x<1&&u>-1022;)x*=2,u--;else for(;x>=2;)x/=2,u++;var i=u<0?tx:tu,c=tx;if(t)c=$e;else switch(e){case 43:c=tu;break;case 32:c=Ru;break;default:break}if(r>=0&&r<13){var v=Math.pow(2,r*4);x=Math.round(x*v)/v}var s=x.toString(16);if(r>=0){var l=s.indexOf(_n);if(l<0)s+=_n+tl(r,Q2);else{var p=l+1+r;s.length<p?s+=tl(p-s.length,Q2):s=s.substr(0,p)}}return Ut(c+no+s+"p"+i+u.toString(10))}function mJ(x){return+x.isZero()}function q6(x){return new nr(x&Qc,x>>24&Qc,x>>31&on)}function dJ(x){return x.toInt()}function hJ(x){return+x.isNeg()}function JC(x){return x.neg()}function FL(x,r){var e=XC(x);e.signedconv&&hJ(r)&&(e.sign=-1,r=JC(r));var t=tx,u=q6(e.base),i="0123456789abcdef";do{var c=r.udivmod(u);r=c.quotient,t=i.charAt(dJ(c.modulus))+t}while(!mJ(r));if(e.prec>=0){e.filler=Ru;var v=e.prec-t.length;v>0&&(t=tl(v,Q2)+t)}return GC(e,t)}function Ux(x){return x.length}function J0(x,r){return x.charCodeAt(r)}function RL(x,r){return x.add(r)}function ML(x,r){return x.mul(r)}function zC(x,r){return x.ucompare(r)<0}function LL(x){var r=0,e=Ux(x),t=10,u=1;if(e>0)switch(J0(x,r)){case 45:r++,u=-1;break;case 43:r++,u=1;break}if(r+1<e&&J0(x,r)==48)switch(J0(x,r+1)){case 120:case 88:t=16,r+=2;break;case 111:case 79:t=8,r+=2;break;case 98:case 66:t=2,r+=2;break;case 117:case 85:r+=2;break}return[r,u,t]}function pd(x){return x>=48&&x<=57?x-48:x>=65&&x<=90?x-55:x>=97&&x<=o1?x-87:-1}function Iv(x){var r=LL(x),e=r[0],t=r[1],u=r[2],i=q6(u),c=new nr(Qc,268435455,on).udivmod(i).quotient,v=J0(x,e),s=pd(v);(s<0||s>=u)&&ee(_1);for(var l=q6(s);;)if(e++,v=J0(x,e),v!=95){if(s=pd(v),s<0||s>=u)break;zC(c,l)&&ee(_1),s=q6(s),l=RL(ML(i,l),s),zC(l,s)&&ee(_1)}return e!=Ux(x)&&ee(_1),u==10&&zC(new nr(0,0,hn),l)&&ee(_1),t<0&&(l=JC(l)),l}function qL(x,r){return x.or(r)}function kd(x){return x.toFloat()}function dt(x){var r=LL(x),e=r[0],t=r[1],u=r[2],i=Ux(x),c=-1>>>0,v=e<i?J0(x,e):0,s=pd(v);(s<0||s>=u)&&ee(_1);var l=s;for(e++;e<i;e++)if(v=J0(x,e),v!=95){if(s=pd(v),s<0||s>=u)break;l=u*l+s,l>c&&ee(_1)}return e!=i&&ee(_1),l=t*l,u==10&&(l|0)!=l&&ee(_1),l|0}function Vx(x){return jC(x)?x:gL(x)}function yJ(x){for(var r={},e=1;e<x.length;e++){var t=x[e];r[Vx(t[1])]=t[2]}return r}var md=Ia;function gJ(x){return x.l>=0?x.l:x.l=x.length}function _J(x){return function(){for(var r=gJ(x),e=new Array(r),t=0;t<r;t++)e[t]=arguments[t];return md(x,e)}}function KC(x,r,e){return x[0]==r?(x[0]=e,1):0}function wJ(x){return KC(x,fv,z3),0}function bJ(x){return x instanceof Array&&x[0]==x[0]>>>0&&KC(x,z3,fv)?0:1}function TJ(x){return KC(x,fv,Z3),0}function EJ(x,r){return+(sd(x,r,!1)<0)}function UL(x){return x}function AJ(x,r){return x.get(x.offset(r))}function SJ(x,r){return x.xor(r)}function PJ(x,r){return x.shift_right_unsigned(r)}function IJ(x,r){return x.shift_left(r)}function dd(x){function r(q,J){return IJ(q,J)}function e(q,J){return PJ(q,J)}function t(q,J){return qL(q,J)}function u(q,J){return SJ(q,J)}function i(q,J){return RL(q,J)}function c(q,J){return ML(q,J)}function v(q,J){return t(r(q,J),e(q,64-J))}function s(q,J){return AJ(q,J)}function l(q,J,e0){return M6(q,J,e0)}var p=Iv(UL("0xd1342543de82ef95")),d=Iv(UL("0xdaba0b6eb09322e3")),T,L,X,b=x,C=s(b,0),N=s(b,1),I=s(b,2),F=s(b,3);T=i(N,I),T=c(u(T,e(T,32)),d),T=c(u(T,e(T,32)),d),T=u(T,e(T,32)),l(b,1,i(c(N,p),C));var L=I,X=F;return X=u(X,L),L=v(L,24),L=u(u(L,X),r(X,16)),X=v(X,37),l(b,2,L),l(b,3,X),T}function oo(e,r){e<0&&R6();var e=e+1|0,t=new Array(e);t[0]=0;for(var u=1;u<e;u++)t[u]=r;return t}function CJ(){var x=new ArrayBuffer(64),r=new Uint32Array(x),e=new Uint8Array(x);return{len:0,w:new Uint32Array([1732584193,4023233417,2562383102,271733878]),b32:r,b8:e}}var hd=function(){function x(c,v){return c+v|0}function r(c,v,s,l,p,d){return v=x(x(v,c),x(l,d)),x(v<<p|v>>>32-p,s)}function e(c,v,s,l,p,d,T){return r(v&s|~v&l,c,v,p,d,T)}function t(c,v,s,l,p,d,T){return r(v&l|s&~l,c,v,p,d,T)}function u(c,v,s,l,p,d,T){return r(v^s^l,c,v,p,d,T)}function i(c,v,s,l,p,d,T){return r(s^(v|~l),c,v,p,d,T)}return function(c,v){var s=c[0],l=c[1],p=c[2],d=c[3];s=e(s,l,p,d,v[0],7,3614090360),d=e(d,s,l,p,v[1],12,3905402710),p=e(p,d,s,l,v[2],17,606105819),l=e(l,p,d,s,v[3],22,3250441966),s=e(s,l,p,d,v[4],7,4118548399),d=e(d,s,l,p,v[5],12,1200080426),p=e(p,d,s,l,v[6],17,2821735955),l=e(l,p,d,s,v[7],22,4249261313),s=e(s,l,p,d,v[8],7,1770035416),d=e(d,s,l,p,v[9],12,2336552879),p=e(p,d,s,l,v[10],17,4294925233),l=e(l,p,d,s,v[11],22,2304563134),s=e(s,l,p,d,v[12],7,1804603682),d=e(d,s,l,p,v[13],12,4254626195),p=e(p,d,s,l,v[14],17,2792965006),l=e(l,p,d,s,v[15],22,1236535329),s=t(s,l,p,d,v[1],5,4129170786),d=t(d,s,l,p,v[6],9,3225465664),p=t(p,d,s,l,v[11],14,643717713),l=t(l,p,d,s,v[0],20,3921069994),s=t(s,l,p,d,v[5],5,3593408605),d=t(d,s,l,p,v[10],9,38016083),p=t(p,d,s,l,v[15],14,3634488961),l=t(l,p,d,s,v[4],20,3889429448),s=t(s,l,p,d,v[9],5,568446438),d=t(d,s,l,p,v[14],9,3275163606),p=t(p,d,s,l,v[3],14,4107603335),l=t(l,p,d,s,v[8],20,1163531501),s=t(s,l,p,d,v[13],5,2850285829),d=t(d,s,l,p,v[2],9,4243563512),p=t(p,d,s,l,v[7],14,1735328473),l=t(l,p,d,s,v[12],20,2368359562),s=u(s,l,p,d,v[5],4,4294588738),d=u(d,s,l,p,v[8],11,2272392833),p=u(p,d,s,l,v[11],16,1839030562),l=u(l,p,d,s,v[14],23,4259657740),s=u(s,l,p,d,v[1],4,2763975236),d=u(d,s,l,p,v[4],11,1272893353),p=u(p,d,s,l,v[7],16,4139469664),l=u(l,p,d,s,v[10],23,3200236656),s=u(s,l,p,d,v[13],4,681279174),d=u(d,s,l,p,v[0],11,3936430074),p=u(p,d,s,l,v[3],16,3572445317),l=u(l,p,d,s,v[6],23,76029189),s=u(s,l,p,d,v[9],4,3654602809),d=u(d,s,l,p,v[12],11,3873151461),p=u(p,d,s,l,v[15],16,530742520),l=u(l,p,d,s,v[2],23,3299628645),s=i(s,l,p,d,v[0],6,4096336452),d=i(d,s,l,p,v[7],10,1126891415),p=i(p,d,s,l,v[14],15,2878612391),l=i(l,p,d,s,v[5],21,4237533241),s=i(s,l,p,d,v[12],6,1700485571),d=i(d,s,l,p,v[3],10,2399980690),p=i(p,d,s,l,v[10],15,4293915773),l=i(l,p,d,s,v[1],21,2240044497),s=i(s,l,p,d,v[8],6,1873313359),d=i(d,s,l,p,v[15],10,4264355552),p=i(p,d,s,l,v[6],15,2734768916),l=i(l,p,d,s,v[13],21,1309151649),s=i(s,l,p,d,v[4],6,4149444226),d=i(d,s,l,p,v[11],10,3174756917),p=i(p,d,s,l,v[2],15,718787259),l=i(l,p,d,s,v[9],21,3951481745),c[0]=x(s,c[0]),c[1]=x(l,c[1]),c[2]=x(p,c[2]),c[3]=x(d,c[3])}}();function NJ(x,r,e){var t=x.len&co,u=0;if(x.len+=e,t){var i=64-t;if(e<i){x.b8.set(r.subarray(0,e),t);return}x.b8.set(r.subarray(0,i),t),hd(x.w,x.b32),e-=i,u+=i}for(;e>=64;)x.b8.set(r.subarray(u,u+64),0),hd(x.w,x.b32),e-=64,u+=64;e&&x.b8.set(r.subarray(u,u+e),0)}function OJ(x){var r=x.len&co;if(x.b8[r]=Lt,r++,r>56){for(var e=r;e<64;e++)x.b8[e]=0;hd(x.w,x.b32);for(var e=0;e<56;e++)x.b8[e]=0}else for(var e=r;e<56;e++)x.b8[e]=0;x.b32[14]=x.len<<3,x.b32[15]=x.len>>29&536870911,hd(x.w,x.b32);for(var t=new Uint8Array(16),u=0;u<4;u++)for(var e=0;e<4;e++)t[u*4+e]=x.w[u]>>8*e&255;return t}function HC(x){return x.t!=4&&fd(x),x.c}function jJ(x){return L6(x,0,x.length)}function DJ(x,r,e){var t=CJ(),u=HC(x);return NJ(t,u.subarray(r,r+e),e),jJ(OJ(t))}function FJ(x,r,e){return DJ(qt(x),r,e)}function Bt(x){return x.l}function RJ(){return 0}function Mr(x){IC(re.Sys_error,x)}var ks=new Array;function bn(x){var r=ks[x];return r.opened||Mr("Cannot flush a closed channel"),!r.buffer||r.buffer_curr==0||(r.output?r.output(L6(r.buffer,0,r.buffer_curr)):r.file.write(r.offset,r.buffer,0,r.buffer_curr),r.offset+=r.buffer_curr,r.buffer_curr=0),0}function BL(){}function iS0(x){for(var r=Ux(x),e=new Uint8Array(r),t=0;t<r;t++)e[t]=J0(x,t);return e}function Tn(x,r){this.fs={},this.fd=x,this.flags=r}Tn.prototype=new BL,Tn.prototype.constructor=Tn,Tn.prototype.truncate=function(x){try{this.fs.ftruncateSync(this.fd,x|0)}catch(r){Mr(r.toString())}},Tn.prototype.length=function(){try{return this.fs.fstatSync(this.fd).size}catch(x){Mr(x.toString())}},Tn.prototype.write=function(x,r,e,t){try{this.flags.isCharacterDevice?this.fs.writeSync(this.fd,r,e,t):this.fs.writeSync(this.fd,r,e,t,x)}catch(u){Mr(u.toString())}return 0},Tn.prototype.read=function(x,r,e,t){try{if(this.flags.isCharacterDevice)var u=this.fs.readSync(this.fd,r,e,t);else var u=this.fs.readSync(this.fd,r,e,t,x);return u}catch(i){Mr(i.toString())}},Tn.prototype.close=function(){try{return this.fs.closeSync(this.fd),0}catch(x){Mr(x.toString())}};function MJ(x,r){if(r.name)try{var e={},t=e.openSync(r.name,"rs");return new Tn(t,r)}catch{}return new Tn(x,r)}var yd=new Array(3);function U6(){return typeof a0.process<"u"&&typeof a0.process.versions<"u"&&typeof a0.process.versions.node<"u"}function LJ(){function x(e){if(e.charAt(0)===ve)return[tx,e.substring(1)]}function r(e){var t=/^([a-zA-Z]:|[\\/]{2}[^\\/]+[\\/]+[^\\/]+)?([\\/])?([\s\S]*?)$/,u=t.exec(e),i=u[1]||tx,c=!!(i&&i.charAt(1)!==rv);if(u[2]||c){var v=u[1]||tx,s=u[2]||tx;return[v,e.substring(v.length+s.length)]}}return U6()&&a0.process&&a0.process.platform&&a0.process.platform===Cj?r:x}var VC=LJ();function XL(x){return x.slice(-1)!==ve?x+ve:x}if(U6()&&a0.process&&a0.process.cwd)var B6=a0.process.cwd().replace(/\\/g,ve);else var B6="/static";B6=XL(B6);function qJ(x){x=Vx(x),VC(x)||(x=B6+x);for(var r=VC(x),e=r[1].split(ve),t=[],u=0;u<e.length;u++)switch(e[u]){case"..":t.length>1&&t.pop();break;case".":break;case"":break;default:t.push(e[u]);break}return t.unshift(r[0]),t.orig=x,t}var UJ=["E2BIG","EACCES","EAGAIN",WI,"EBUSY","ECHILD","EDEADLK","EDOM",Gj,"EFAULT","EFBIG","EINTR","EINVAL","EIO","EISDIR","EMFILE","EMLINK","ENAMETOOLONG","ENFILE","ENODEV",D_,"ENOEXEC","ENOLCK","ENOMEM","ENOSPC","ENOSYS",u_,hD,"ENOTTY","ENXIO","EPERM","EPIPE","ERANGE","EROFS","ESPIPE","ESRCH","EXDEV","EWOULDBLOCK","EINPROGRESS","EALREADY","ENOTSOCK","EDESTADDRREQ","EMSGSIZE","EPROTOTYPE","ENOPROTOOPT","EPROTONOSUPPORT","ESOCKTNOSUPPORT","EOPNOTSUPP","EPFNOSUPPORT","EAFNOSUPPORT","EADDRINUSE","EADDRNOTAVAIL","ENETDOWN","ENETUNREACH","ENETRESET","ECONNABORTED","ECONNRESET","ENOBUFS","EISCONN","ENOTCONN","ESHUTDOWN","ETOOMANYREFS","ETIMEDOUT","ECONNREFUSED","EHOSTDOWN","EHOSTUNREACH","ELOOP","EOVERFLOW"];function ms(x,r,e,t){var u=UJ.indexOf(x);u<0&&(t==null&&(t=-9999),u=[0,t]);var i=[u,Ut(r||tx),Ut(e||tx)];return i}var GL={};function vo(x){return GL[x]}function ds(x,r){throw z0([0,x].concat(r))}function $C(x){return x instanceof Uint8Array||(x=new Uint8Array(x)),new vs(4,x,x.length)}function YL(x){Mr(x+l4)}function de(x){this.data=x}de.prototype=new BL,de.prototype.constructor=de,de.prototype.truncate=function(x){var r=this.data;this.data=I1(x|0),os(r,0,this.data,0,x)},de.prototype.length=function(){return Bt(this.data)},de.prototype.write=function(x,r,e,t){var u=this.length();if(x+t>=u){var i=I1(x+t),c=this.data;this.data=i,os(c,0,this.data,0,u)}return os($C(r),e,this.data,x,t),0},de.prototype.read=function(x,r,e,t){var u=this.length();if(x+t>=u&&(t=u-x),t){var i=I1(t|0);os(this.data,x,i,0,t),r.set(HC(i),e)}return t};function Cv(x,r,e){this.file=r,this.name=x,this.flags=e}Cv.prototype.err_closed=function(){Mr(this.name+MF)},Cv.prototype.length=function(){if(this.file)return this.file.length();this.err_closed()},Cv.prototype.write=function(x,r,e,t){if(this.file)return this.file.write(x,r,e,t);this.err_closed()},Cv.prototype.read=function(x,r,e,t){if(this.file)return this.file.read(x,r,e,t);this.err_closed()},Cv.prototype.close=function(){this.file=void 0};function A2(x,r){this.content={},this.root=x,this.lookupFun=r}A2.prototype.nm=function(x){return this.root+x},A2.prototype.create_dir_if_needed=function(x){for(var r=x.split(ve),e=tx,t=0;t<r.length-1;t++)e+=r[t]+ve,!this.content[e]&&(this.content[e]=Symbol("directory"))},A2.prototype.slash=function(x){return/\/$/.test(x)?x:x+ve},A2.prototype.lookup=function(x){if(!this.content[x]&&this.lookupFun){var r=this.lookupFun(this.root,x);r!==0&&(this.create_dir_if_needed(x),this.content[x]=new de(qt(r[1])))}},A2.prototype.exists=function(x){if(x==tx)return 1;var r=this.slash(x);return this.content[r]?1:(this.lookup(x),this.content[x]?1:0)},A2.prototype.isFile=function(x){return this.exists(x)&&!this.is_dir(x)?1:0},A2.prototype.mkdir=function(x,r,e){var t=e&&vo(e4);this.exists(x)&&(t?ds(t,ms(Gj,cE,this.nm(x))):Mr(x+": File exists"));var u=/^(.*)\/[^/]+/.exec(x);u=u&&u[1]||tx,this.exists(u)||(t?ds(t,ms(D_,cE,this.nm(u))):Mr(u+l4)),this.is_dir(u)||(t?ds(t,ms(u_,cE,this.nm(u))):Mr(u+xb)),this.create_dir_if_needed(this.slash(x))},A2.prototype.rmdir=function(x,r){var e=r&&vo(e4),t=x==tx?tx:this.slash(x),u=new RegExp(H4+t+fF);this.exists(x)||(e?ds(e,ms(D_,yI,this.nm(x))):Mr(x+l4)),this.is_dir(x)||(e?ds(e,ms(u_,yI,this.nm(x))):Mr(x+xb));for(var i in this.content)i.match(u)&&(e?ds(e,ms(hD,yI,this.nm(x))):Mr(this.nm(x)+": Directory not empty"));delete this.content[t]},A2.prototype.readdir=function(x){var r=x==tx?tx:this.slash(x);this.exists(x)||Mr(x+l4),this.is_dir(x)||Mr(x+xb);var e=new RegExp(H4+r+fF),t={},u=[];for(var i in this.content){var c=i.match(e);c&&!t[c[1]]&&(t[c[1]]=!0,u.push(c[1]))}return u},A2.prototype.opendir=function(x,r){var e=r&&vo(e4),t=this.readdir(x),u=!1,i=0;return{readSync:function(){if(u&&(e?ds(e,ms(WI,tR,this.nm(x))):Mr(x+dF)),i==t.length)return null;var c=t[i];return i++,{name:c}},closeSync:function(){u&&(e?ds(e,ms(WI,tR,this.nm(x))):Mr(x+dF)),u=!0,t=[]}}},A2.prototype.is_dir=function(x){if(x==tx)return!0;var r=this.slash(x);return this.content[r]?1:0},A2.prototype.unlink=function(x){var r=!!this.content[x];return delete this.content[x],r},A2.prototype.open=function(x,r){var e;return r.rdonly&&r.wronly&&Mr(this.nm(x)+Xy),r.text&&r.binary&&Mr(this.nm(x)+Zw),this.lookup(x),this.content[x]?(this.is_dir(x)&&Mr(this.nm(x)+ZM),r.create&&r.excl&&Mr(this.nm(x)+rP),e=this.content[x],r.truncate&&e.truncate()):r.create?(this.create_dir_if_needed(x),this.content[x]=new de(I1(0)),e=this.content[x]):YL(this.nm(x)),new Cv(this.nm(x),e,r)},A2.prototype.open=function(x,r){var e;return r.rdonly&&r.wronly&&Mr(this.nm(x)+Xy),r.text&&r.binary&&Mr(this.nm(x)+Zw),this.lookup(x),this.content[x]?(this.is_dir(x)&&Mr(this.nm(x)+ZM),r.create&&r.excl&&Mr(this.nm(x)+rP),e=this.content[x],r.truncate&&e.truncate()):r.create?(this.create_dir_if_needed(x),this.content[x]=new de(I1(0)),e=this.content[x]):YL(this.nm(x)),new Cv(this.nm(x),e,r)},A2.prototype.register=function(x,r){var e;if(this.content[x]&&Mr(this.nm(x)+rP),UC(r)&&(e=new de(r)),qC(r))e=new de(qt(r));else if(r instanceof Array)e=new de($C(r));else if(typeof r=="string")e=new de(_L(r));else if(r.toString){var t=qt(Ut(r.toString()));e=new de(t)}e?(this.create_dir_if_needed(x),this.content[x]=e):Mr(this.nm(x)+" : registering file with invalid content type")},A2.prototype.constructor=A2;function c2(x){this.fs={},this.root=x}c2.prototype.nm=function(x){return this.root+x},c2.prototype.exists=function(x){try{return this.fs.existsSync(this.nm(x))?1:0}catch{return 0}},c2.prototype.isFile=function(x){try{return this.fs.statSync(this.nm(x)).isFile()?1:0}catch(r){Mr(r.toString())}},c2.prototype.mkdir=function(x,r,e){try{return this.fs.mkdirSync(this.nm(x),{mode:r}),0}catch(t){this.raise_nodejs_error(t,e)}},c2.prototype.rmdir=function(x,r){try{return this.fs.rmdirSync(this.nm(x)),0}catch(e){this.raise_nodejs_error(e,r)}},c2.prototype.readdir=function(x,r){try{return this.fs.readdirSync(this.nm(x))}catch(e){this.raise_nodejs_error(e,r)}},c2.prototype.is_dir=function(x){try{return this.fs.statSync(this.nm(x)).isDirectory()?1:0}catch(r){Mr(r.toString())}},c2.prototype.unlink=function(x,r){try{var e=this.fs.existsSync(this.nm(x))?1:0;return this.fs.unlinkSync(this.nm(x)),e}catch(t){this.raise_nodejs_error(t,r)}},c2.prototype.open=function(x,r,e){var t={},u=0;for(var i in r)switch(i){case"rdonly":u|=t.O_RDONLY;break;case"wronly":u|=t.O_WRONLY;break;case"append":u|=t.O_WRONLY|t.O_APPEND;break;case"create":u|=t.O_CREAT;break;case"truncate":u|=t.O_TRUNC;break;case"excl":u|=t.O_EXCL;break;case"binary":u|=t.O_BINARY;break;case"text":u|=t.O_TEXT;break;case"nonblock":u|=t.O_NONBLOCK;break}try{var c=this.fs.openSync(this.nm(x),u),v=this.fs.lstatSync(this.nm(x)).isCharacterDevice();return r.isCharacterDevice=v,new Tn(c,r)}catch(s){this.raise_nodejs_error(s,e)}},c2.prototype.rename=function(x,r,e){try{this.fs.renameSync(this.nm(x),this.nm(r))}catch(t){this.raise_nodejs_error(t,e)}},c2.prototype.stat=function(x,r){try{var e=this.fs.statSync(this.nm(x));return this.stats_from_js(e)}catch(t){this.raise_nodejs_error(t,r)}},c2.prototype.lstat=function(x,r){try{var e=this.fs.lstatSync(this.nm(x));return this.stats_from_js(e)}catch(t){this.raise_nodejs_error(t,r)}},c2.prototype.symlink=function(x,r,e,t){try{return this.fs.symlinkSync(this.nm(r),this.nm(e),x?"dir":"file"),0}catch(u){this.raise_nodejs_error(u,t)}},c2.prototype.readlink=function(x,r){try{var e=this.fs.readlinkSync(this.nm(x),"utf8");return Ut(e)}catch(t){this.raise_nodejs_error(t,r)}},c2.prototype.opendir=function(x,r){try{return this.fs.opendirSync(this.nm(x))}catch(e){this.raise_nodejs_error(e,r)}},c2.prototype.raise_nodejs_error=function(x,r){var e=vo(e4);if(r&&e){var t=ms(x.code,x.syscall,x.path,x.errno);ds(e,t)}else Mr(x.toString())},c2.prototype.stats_from_js=function(x){var r;return x.isFile()?r=0:x.isDirectory()?r=1:x.isCharacterDevice()?r=2:x.isBlockDevice()?r=3:x.isSymbolicLink()?r=4:x.isFIFO()?r=5:x.isSocket()&&(r=6),[0,x.dev,x.ino,r,x.mode,x.nlink,x.uid,x.gid,x.rdev,x.size,x.atimeMs,x.mtimeMs,x.ctimeMs]},c2.prototype.constructor=c2;function JL(x){var r=VC(x);if(r)return r[0]+ve}var gd=JL(B6)||ee("unable to compute caml_root"),fl=[];U6()?fl.push({path:gd,device:new c2(gd)}):fl.push({path:gd,device:new A2(gd)}),fl.push({path:tD,device:new A2(tD)});function BJ(e){for(var r=qJ(e),e=r.join(ve),t=XL(e),u,i=0;i<fl.length;i++){var c=fl[i];t.search(c.path)==0&&(!u||u.path.length<c.path.length)&&(u={path:c.path,device:c.device,rest:e.substring(c.path.length,e.length)})}if(!u&&U6()){var v=JL(e);if(v&&v.match(/^[a-zA-Z]:\/$/)){var c={path:v,device:new c2(v)};fl.push(c),u={path:c.path,device:c.device,rest:e.substring(c.path.length,e.length)}}}if(u)return u;Mr("no device found for "+t)}function X6(x,r){de.call(this,I1(0)),this.log=function(e){return 0},x==1&&typeof console.log=="function"?this.log=console.log:x==2&&typeof console.error=="function"?this.log=console.error:typeof console.log=="function"&&(this.log=console.log),this.flags=r}X6.prototype.length=function(){return 0},X6.prototype.write=function(x,r,e,t){if(this.log){t>0&&e>=0&&e+t<=r.length&&r[e+t-1]==10&&t--;var u=I1(t);return os($C(r),e,u,0,t),this.log(u.toUtf16()),0}Mr(this.fd+MF)},X6.prototype.read=function(x,r,e,t){Mr(this.fd+": file descriptor is write only")},X6.prototype.close=function(){this.log=void 0};function _d(x,r){return r==null&&(r=yd.length),yd[r]=x,r|0}function fS0(x,r,e){for(var t={};r;){switch(r[1]){case 0:t.rdonly=1;break;case 1:t.wronly=1;break;case 2:t.append=1;break;case 3:t.create=1;break;case 4:t.truncate=1;break;case 5:t.excl=1;break;case 6:t.binary=1;break;case 7:t.text=1;break;case 8:t.nonblock=1;break}r=r[2]}t.rdonly&&t.wronly&&Mr(x+Xy),t.text&&t.binary&&Mr(x+Zw);var u=BJ(x),i=u.device.open(u.rest,t);return _d(i,void 0)}(function(){function x(r,e){return U6()?MJ(r,e):new X6(r,e)}_d(x(0,{rdonly:1,altname:"/dev/stdin",isCharacterDevice:!0}),0),_d(x(1,{buffered:2,wronly:1,isCharacterDevice:!0}),1),_d(x(2,{buffered:2,wronly:1,isCharacterDevice:!0}),2)})();function XJ(x){var r=yd[x];r.flags.wronly&&Mr($D+x+" is writeonly");var e=null,t={file:r,offset:r.flags.append?r.length():0,fd:x,opened:!0,out:!1,buffer_curr:0,buffer_max:0,buffer:new Uint8Array(E6),refill:e};return ks[t.fd]=t,t.fd}function zL(x){var r=yd[x];r.flags.rdonly&&Mr($D+x+" is readonly");var e=r.flags.buffered!==void 0?r.flags.buffered:1,t={file:r,offset:r.flags.append?r.length():0,fd:x,opened:!0,out:!0,buffer_curr:0,buffer:new Uint8Array(E6),buffered:e};return ks[t.fd]=t,t.fd}function GJ(){for(var x=0,r=0;r<ks.length;r++)ks[r]&&ks[r].opened&&ks[r].out&&(x=[0,ks[r].fd,x]);return x}function YJ(x,r,e,t){var u=ks[x];if(u.opened||Mr("Cannot output to a closed channel"),r=r.subarray(e,e+t),u.buffer_curr+r.length>u.buffer.length){var i=new Uint8Array(u.buffer_curr+r.length);i.set(u.buffer),u.buffer=i}switch(u.buffered){case 0:u.buffer.set(r,u.buffer_curr),u.buffer_curr+=r.length,bn(x);break;case 1:u.buffer.set(r,u.buffer_curr),u.buffer_curr+=r.length,u.buffer_curr>=u.buffer.length&&bn(x);break;case 2:var c=r.lastIndexOf(10);c<0?(u.buffer.set(r,u.buffer_curr),u.buffer_curr+=r.length,u.buffer_curr>=u.buffer.length&&bn(x)):(u.buffer.set(r.subarray(0,c+1),u.buffer_curr),u.buffer_curr+=c+1,bn(x),u.buffer.set(r.subarray(c+1),u.buffer_curr),u.buffer_curr+=r.length-c-1);break}return 0}function JJ(x,u,e,t){var u=HC(u);return YJ(x,u,e,t)}function WC(x,r,e,t){return JJ(x,qt(r),e,t)}function KL(x,r){var e=String.fromCharCode(r);return WC(x,e,0,1),0}function Nv(x,r){return+(sd(x,r,!1)!=0)}function QC(x,r){var e=new Array(r+1);e[0]=x;for(var t=1;t<=r;t++)e[t]=0;return e}function Ov(x){return x instanceof Array&&x[0]==x[0]>>>0?x[0]:UC(x)||qC(x)?q3:x instanceof Function||typeof x=="function"?h4:x&&x.caml_custom?pk:e6}function zJ(x){var r={};if(x)for(var e=1;e<x.length;e++)r[Vx(x[e][1])]=x[e][2];return r}function Xt(x,r,e){if(e){var t=e;if(a0.toplevelReloc)x=md(a0.toplevelReloc,[t]);else if(re.symbols){re.symidx||(re.symidx=zJ(re.symbols));var u=re.symidx[t];u>=0?x=u:ee("caml_register_global: cannot locate "+t)}}re[x+1]=r,e&&(re[e]=r)}function ZC(x,r){return GL[x]=r,0}function KJ(x){return x[2]=jL++,x}function Tr(x,r){return x===r?1:0}function HJ(){f2(VI)}function B1(x,r){return r>>>0>=Ux(x)&&HJ(),J0(x,r)}function P(x,r){return 1-Tr(x,r)}function S2(x){return x.t&6&&cd(x),x.c}function VJ(){return 2147483647/4|0}var $J=a0.process&&a0.process.platform&&a0.process.platform==Cj?cF:"Unix";function WJ(){return[0,$J,32,0]}function QJ(){dL(re.Not_found)}function HL(x){var r=lL(Vx(x));return r===void 0&&QJ(),Ut(r)}function ZJ(){if(a0.crypto){if(a0.crypto.getRandomValues){var x=a0.crypto.getRandomValues(new Int32Array(4));return[0,x[0],x[1],x[2],x[3]]}else if(a0.crypto.randomBytes){var x=new Int32Array(a0.crypto.randomBytes(16).buffer);return[0,x[0],x[1],x[2],x[3]]}}var r=new Date().getTime(),e=r^4294967295*Math.random();return[0,e]}function wd(x){for(var r=1;x&&x.joo_tramp;)x=x.joo_tramp.apply(null,x.joo_args),r++;return x}function z1(x,r){return{joo_tramp:x,joo_args:r}}function qr(x,r){if(r.fun)return x.fun=r.fun,0;if(typeof r=="function")return x.fun=r,0;for(var e=r.length;e--;)x[e]=r[e];return 0}function X1(x){{if(x instanceof Array)return x;var r;return a0.RangeError&&x instanceof a0.RangeError&&x.message&&x.message.match(/maximum call stack/i)||a0.InternalError&&x instanceof a0.InternalError&&x.message&&x.message.match(/too much recursion/i)?r=re.Stack_overflow:x instanceof a0.Error&&vo(XA)?r=[0,vo(XA),x]:r=[0,re.Failure,Ut(String(x))],x instanceof a0.Error&&(r.js_error=x),r}}function xz(x){switch(x[2]){case-8:case-11:case-12:return 1;default:return 0}}function rz(x){var r=tx;if(x[0]==0){if(r+=x[1][1],x.length==3&&x[2][0]==0&&xz(x[1]))var t=x[2],e=1;else var e=2,t=x;r+=XR;for(var u=e;u<t.length;u++){u>e&&(r+=AF);var i=t[u];typeof i=="number"?r+=i.toString():i instanceof vs||typeof i=="string"?r+=um+i.toString()+um:r+=ev}r+=hR}else x[0]==f1&&(r+=x[1]);return r}function VL(x){if(x instanceof Array&&(x[0]==0||x[0]==f1)){var r=vo(Kj);if(r)md(r,[x,!1]);else{var e=rz(x),t=vo(JD);if(t&&md(t,[0]),console.error(xA+e),x.js_error)throw x.js_error}}else throw x}function ez(){var x=a0.process;x&&x.on?x.on("uncaughtException",function(r,e){VL(r),x.exit(2)}):a0.addEventListener&&a0.addEventListener("error",function(r){r.error&&VL(r.error)})}ez();function h(x,r){return(x.l>=0?x.l:x.l=x.length)==1?x(r):Ia(x,[r])}function k(x,r,e){return(x.l>=0?x.l:x.l=x.length)==2?x(r,e):Ia(x,[r,e])}function xx(x,r,e,t){return(x.l>=0?x.l:x.l=x.length)==3?x(r,e,t):Ia(x,[r,e,t])}function G6(x,r,e,t,u){return(x.l>=0?x.l:x.l=x.length)==4?x(r,e,t,u):Ia(x,[r,e,t,u])}function hs(x,r,e,t,u,i){return(x.l>=0?x.l:x.l=x.length)==5?x(r,e,t,u,i):Ia(x,[r,e,t,u,i])}function tz(x,r,e,t,u,i,c){return(x.l>=0?x.l:x.l=x.length)==6?x(r,e,t,u,i,c):Ia(x,[r,e,t,u,i,c])}function nz(x,r,e,t,u,i,c,v){return(x.l>=0?x.l:x.l=x.length)==7?x(r,e,t,u,i,c,v):Ia(x,[r,e,t,u,i,c,v])}var j=void 0,xN=[f1,Pj,-1],$L=[f1,_M,-2],En=[f1,F5,-3],bd=[f1,UM,-4],Na=[f1,gF,-7],WL=[f1,Lj,-8],QL=[f1,HR,-9],Nr=[f1,jR,-11],Y6=[f1,aR,-12],uz=[4,0,0,0,[12,45,[4,0,0,0,0]]],rN=[0,[11,'File "',[2,0,[11,'", line ',[4,0,0,0,[11,Rj,[4,0,0,0,[12,45,[4,0,0,0,[11,": ",[2,0,0]]]]]]]]]],'File "%s", line %d, characters %d-%d: %s'],cl=[0,0,[0,0,0],[0,0,0]],jv=[0,0,0,0,0,1,0,0,0],ZL=[0,"first_leading","last_trailing"],xq=[0,Sf,ln,sc,nf,Rc,Ai,Du,Iu,l7,oa,di,G7,V7,gn,Gc,ou,rt,Sa,wi,Hi,_7,mi,m7,If,Nu,Ku,Rf,Qu,_i,Pi,ui,z7,Vf,fc,S7,Fi,Ef,dc,xu,ff,fu,Hf,Bi,gc,x7,ha,Tc,$f,_u,Z7,ea,Kc,ni,cf,tf,Ke,Ze,ta,xc,mu,Ki,Tu,pf,Ti,Bc,bi,Ac,kc,Kn,$u,uf,Oe,B7,Lu,Pu,bc,yc,Uf,zc,U7,wa,Cc,Uc,Yf,Ec,zi,Qn,F7,Ea,yf,Fc,Vn,u2,w7,ra,Ic,L7,hi,Mf,N7,g7,bf,ya,$i,yi,Oi,sa,i7,_f,ju,Ta,tc,wf,lu,ii,hc,lc,D7,xf,gi,Uu,ki,p7,au,ga,Mc,ji,Ft,ia,K7,Mi,W2,Wn,Qi,ca,Df,Mu,ic,na,oc,le,lf,cu,ti,Hu,Zn,jc,Ri,qi,si,ma,R7,Xf,uu,X7,k7,Jf,Yu,mc,W7,Oc,wc,pa,zf,Tf,iu,gu,kf,li,Lc,e7,C7,k2,pi,q7,aa,pu,Xc,Fu,Ou,Su,va,j7,bu,su,Nf,df,nc,Vi,ru,hf,ka,r7,Au,Pc,Si,Xu,zu,Jc,Gf,da,Gi,Wc,Ci,v7,vi,xi,Wu,Di,fi,Vu,Hc,$n,sf,Gu,mf,Qf,Yi,a7,n7,Sc,Kf,y7,s7,t7,_a,Zu,rc,c7,xa,Ne,Cu,Ii,Vc,Ei,$7,Bu,jf,Hn,T7,rf,Dc,ku,Wf,M7,of,ef,yu,J7,ei,Ni,Lf,He,ua,P7,Pf,h7,Of,I7,oi,Qe,O7,hu,ai,fa,b7,vu,Ju,Yc,et,Zc,uc,Xi,$c,d7,Wi,o7,xt,nu,Nc,qc,ba,Af,gf,ac,Aa,cc,u7,du,Eu,Zf,f7,H7,af,ci,Q7,Bf,ri,A7,Y7,wu,Li,qu,Ji,Ff,_c,vc,E7,qf,Z2,eu,Zi,vf,$2],An=[0,0,0];Xt(11,Y6,aR),Xt(10,Nr,jR),Xt(9,[f1,bR,$R],bR),Xt(8,QL,HR),Xt(7,WL,Lj),Xt(6,Na,gF),Xt(5,[f1,rF,-6],rF),Xt(4,[f1,fM,-5],fM),Xt(3,bd,UM),Xt(2,En,F5),Xt(1,$L,_M),Xt(0,xN,Pj);function G1(x){if(typeof x=="number")return 0;switch(x[0]){case 0:return[0,G1(x[1])];case 1:return[1,G1(x[1])];case 2:return[2,G1(x[1])];case 3:return[3,G1(x[1])];case 4:return[4,G1(x[1])];case 5:return[5,G1(x[1])];case 6:return[6,G1(x[1])];case 7:return[7,G1(x[1])];case 8:var r=x[1];return[8,r,G1(x[2])];case 9:var e=x[1];return[9,e,e,G1(x[3])];case 10:return[10,G1(x[1])];case 11:return[11,G1(x[1])];case 12:return[12,G1(x[1])];case 13:return[13,G1(x[1])];default:return[14,G1(x[1])]}}function he(x,r){if(typeof x=="number")return r;switch(x[0]){case 0:return[0,he(x[1],r)];case 1:return[1,he(x[1],r)];case 2:return[2,he(x[1],r)];case 3:return[3,he(x[1],r)];case 4:return[4,he(x[1],r)];case 5:return[5,he(x[1],r)];case 6:return[6,he(x[1],r)];case 7:return[7,he(x[1],r)];case 8:var e=x[1];return[8,e,he(x[2],r)];case 9:var t=x[2],u=x[1];return[9,u,t,he(x[3],r)];case 10:return[10,he(x[1],r)];case 11:return[11,he(x[1],r)];case 12:return[12,he(x[1],r)];case 13:return[13,he(x[1],r)];default:return[14,he(x[1],r)]}}function O1(x,r){if(typeof x=="number")return r;switch(x[0]){case 0:return[0,O1(x[1],r)];case 1:return[1,O1(x[1],r)];case 2:var e=x[1];return[2,e,O1(x[2],r)];case 3:var t=x[1];return[3,t,O1(x[2],r)];case 4:var u=x[3],i=x[2],c=x[1];return[4,c,i,u,O1(x[4],r)];case 5:var v=x[3],s=x[2],l=x[1];return[5,l,s,v,O1(x[4],r)];case 6:var p=x[3],d=x[2],T=x[1];return[6,T,d,p,O1(x[4],r)];case 7:var b=x[3],C=x[2],N=x[1];return[7,N,C,b,O1(x[4],r)];case 8:var I=x[3],F=x[2],L=x[1];return[8,L,F,I,O1(x[4],r)];case 9:var X=x[1];return[9,X,O1(x[2],r)];case 10:return[10,O1(x[1],r)];case 11:var q=x[1];return[11,q,O1(x[2],r)];case 12:var J=x[1];return[12,J,O1(x[2],r)];case 13:var e0=x[2],W=x[1];return[13,W,e0,O1(x[3],r)];case 14:var x0=x[2],i0=x[1];return[14,i0,x0,O1(x[3],r)];case 15:return[15,O1(x[1],r)];case 16:return[16,O1(x[1],r)];case 17:var f0=x[1];return[17,f0,O1(x[2],r)];case 18:var r0=x[1];return[18,r0,O1(x[2],r)];case 19:return[19,O1(x[1],r)];case 20:var v0=x[2],o0=x[1];return[20,o0,v0,O1(x[3],r)];case 21:var w0=x[1];return[21,w0,O1(x[2],r)];case 22:return[22,O1(x[1],r)];case 23:var t0=x[1];return[23,t0,O1(x[2],r)];default:var s0=x[2],h0=x[1];return[24,h0,s0,O1(x[3],r)]}}function Sx(x){throw z0([0,En,x],1)}function X2(x){throw z0([0,bd,x],1)}function Td(x){return 0<=x?x:-x|0}var iz=as,fz=xs;function Jx(x,r){var e=Ux(x),t=Ux(r),u=I1(e+t|0);return Pa(x,0,u,0,e),Pa(r,0,u,e,t),S2(u)}function Gx(x,r){if(!x)return r;var e=x[2],t=x[1];if(!e)return[0,t,r];var u=e[2],i=e[1];if(!u)return[0,t,[0,i,r]];for(var c=[0,u[1],ro],v=c,s=1,l=u[2];;){if(l){var p=l[2],d=l[1];if(p){var T=p[2],b=p[1];if(T){var C=[0,T[1],ro],N=T[2];v[1+s]=[0,d,[0,b,C]];var v=C,s=1,l=N;continue}v[1+s]=[0,d,[0,b,r]]}else v[1+s]=[0,d,r]}else v[1+s]=r;return[0,t,[0,i,c]]}}XJ(0);var rq=zL(1),Sn=zL(2),cz="output_substring";function J6(x,r){WC(x,r,0,Ux(r))}function eq(x,r,e,t){return 0<=e&&0<=t&&(Ux(r)-t|0)>=e?WC(x,r,e,t):X2(cz)}function tq(x){return J6(Sn,x),KL(Sn,10),bn(Sn)}var eN=[0,function(x){for(var r=GJ(0);;){if(!r)return 0;var e=r[2],t=r[1];try{bn(t)}catch(c){var u=X1(c);if(u[1]!==$L)throw z0(u,0)}var r=e}}],nq=[0,function(x){}];function tN(x){return h(nq[1],0),h(rl(eN),0)}ZC(JD,tN);var uq=WJ(0)[1],z6=(4*VJ(0)|0)-1|0;function Ed(x,r){return r?[0,h(x,r[1])]:0}function K6(x){return x?1:0}function iq(x){return 25<x+gk>>>0?x:x-32|0}var az="hd",sz="tl",oz="List.iter2";function ys(x){for(var r=0,e=x;;){if(!e)return r;var r=r+1|0,e=e[2]}}function H6(x){return x?x[1]:Sx(az)}function fq(x){return x?x[2]:Sx(sz)}function al(x,r){for(var e=x,t=r;;){if(!e)return t;var u=[0,e[1],t],e=e[2],t=u}}function cx(x){return al(x,0)}function V6(x){if(!x)return 0;var r=x[1];return Gx(r,V6(x[2]))}function Pn(x,r){if(!r)return 0;var e=r[2],t=r[1];if(!e)return[0,x(t),0];for(var u=e[2],i=e[1],c=x(t),v=[0,x(i),ro],s=v,l=1,p=u;;){if(p){var d=p[2],T=p[1];if(d){var b=d[2],C=d[1],N=x(T),I=[0,x(C),ro];s[1+l]=[0,N,I];var s=I,l=1,p=b;continue}s[1+l]=[0,x(T),0]}else s[1+l]=0;return[0,c,v]}}function Ad(x,r){for(var e=0,t=r;;){if(!t)return e;var u=t[2],e=[0,x(t[1]),e],t=u}}function P2(x,r){for(var e=r;;){if(!e)return 0;var t=e[2];h(x,e[1]);var e=t}}function y2(x,r,e){for(var t=r,u=e;;){if(!u)return t;var i=u[2],t=k(x,t,u[1]),u=i}}function nN(x,r,e){if(!r)return e;var t=r[1];return x(t,nN(x,r[2],e))}function cq(x,r,e){for(var t=r,u=e;;){if(t){if(u){var i=u[2],c=t[2];x(t[1],u[1]);var t=c,u=i;continue}}else if(!u)return;return X2(oz)}}function sl(x,r){for(var e=r;;){if(!e)return 0;var t=e[2],u=h(x,e[1]);if(u)return u;var e=t}}function uN(x,r){for(var e=r;;){if(!e)return 0;var t=e[2],u=NL(e[1],x)===0?1:0;if(u)return u;var e=t}}function $6(x,r){for(var e=r;;){if(!e)return 0;var t=e[2],u=e[1];if(x(u))for(var i=[0,u,ro],c=i,v=1,s=t;;){if(!s)return c[1+v]=0,i;var l=s[2],p=s[1];if(x(p)){var d=[0,p,ro];c[1+v]=d;var c=d,v=1,s=l}else var s=l}else var e=t}}var vz="String.sub / Bytes.sub",lz="Bytes.blit",pz="String.blit / Bytes.blit_string";function Dv(x,r){var e=I1(x);return lJ(e,0,x,r),e}function aq(x,r,e){if(0<=r&&0<=e&&(Bt(x)-e|0)>=r){var t=I1(e);return os(x,r,t,0,e),t}return X2(vz)}function ol(x,r,e){return S2(aq(x,r,e))}function sq(x,r,e,t,u){if(0<=u&&0<=r&&(Bt(x)-u|0)>=r&&0<=t&&(Bt(e)-u|0)>=t){os(x,r,e,t,u);return}return X2(lz)}function In(x,r,e,t,u){if(0<=u&&0<=r&&(Ux(x)-u|0)>=r&&0<=t&&(Bt(e)-u|0)>=t){Pa(x,r,e,t,u);return}return X2(pz)}var kz="String.concat",mz=tx;function Sd(x,r){return S2(Dv(x,r))}function I2(x,r,e){return S2(aq(qt(x),r,e))}function oq(x,r){if(!r)return mz;var e=Ux(x);x:{r:{for(var t=0,u=r,i=0;u;){var c=u[1];if(!u[2])break r;var v=(Ux(c)+e|0)+t|0,s=u[2],l=t<=v?v:X2(kz),t=l,u=s}var p=t;break x}var p=Ux(c)+t|0}for(var d=I1(p),T=i,b=r;;){if(b){var C=b[1];if(b[2]){var N=b[2];Pa(C,0,d,T,Ux(C)),Pa(x,0,d,T+Ux(C)|0,e);var T=(T+Ux(C)|0)+e|0,b=N;continue}Pa(C,0,d,T,Ux(C))}return S2(d)}}function vq(x){var r=qt(x);if(Bt(r)===0)var e=r;else{var t=Bt(r),u=I1(t);os(r,0,u,0,t),zr(u,0,iq(me(r,0)));var e=u}return S2(e)}function lq(x,r){var e=[0,0],t=[0,Ux(r)],u=Ux(r)-1|0;if(u>=0)for(var i=u;;){if(J0(r,i)===x){var c=e[1];e[1]=[0,I2(r,i+1|0,(t[1]-i|0)-1|0),c],t[1]=i}var v=i-1|0;if(i===0)break;var i=v}var s=e[1];return[0,I2(r,0,t[1]),s]}function Pd(x,r){return $Y(qt(x),r)}var dz="Array.blit";function pq(x,r,e,t,u){if(0<=u&&0<=r&&(x.length-1-u|0)>=r&&0<=t&&(e.length-1-u|0)>=t){BY(x,r,e,t,u);return}return X2(dz)}function kq(x,r){var e=r.length-1-1|0,t=0;if(e>=0)for(var u=t;;){x(r[1+u]);var i=u+1|0;if(e===u)break;var u=i}}function Id(x,r){var e=r.length-1;if(e===0)return[0];var t=oo(e,x(r[1])),u=e-1|0,i=1;if(u>=1)for(var c=i;;){t[1+c]=x(r[1+c]);var v=c+1|0;if(u===c)break;var c=v}return t}function W6(x){if(!x)return[0];for(var r=0,e=x,t=x[2],u=x[1];e;)var r=r+1|0,e=e[2];for(var i=oo(r,u),c=1,v=t;;){if(!v)return i;var s=v[2];i[1+c]=v[1];var c=c+1|0,v=s}}function mq(x){try{var r=[0,Iv(x)];return r}catch(t){var e=X1(t);if(e[1]===En)return 0;throw z0(e,0)}}var hz=dm,yz=dm,gz=dm,_z=dm;function iN(x){function r(c){return c?c[5]:0}function e(c,v,s,l){var p=r(c),d=r(l),T=d<=p?p+1|0:d+1|0;return[0,c,v,s,l,T]}function t(c,v,s,l){var p=c?c[5]:0,d=l?l[5]:0;if((d+2|0)<p){if(!c)return X2(yz);var T=c[4],b=c[3],C=c[2],N=c[1],I=r(T);if(I<=r(N))return e(N,C,b,e(T,v,s,l));if(!T)return X2(hz);var F=T[3],L=T[2],X=T[1],q=e(T[4],v,s,l);return e(e(N,C,b,X),L,F,q)}if((p+2|0)>=d){var J=d<=p?p+1|0:d+1|0;return[0,c,v,s,l,J]}if(!l)return X2(_z);var e0=l[4],W=l[3],x0=l[2],i0=l[1],f0=r(i0);if(f0<=r(e0))return e(e(c,v,s,i0),x0,W,e0);if(!i0)return X2(gz);var r0=i0[3],v0=i0[2],o0=i0[1],w0=e(i0[4],x0,W,e0);return e(e(c,v,s,o0),v0,r0,w0)}function u(c,v,s){if(!s)return[0,0,c,v,0,1];var l=s[4],p=s[3],d=s[2],T=s[1],b=s[5],C=k(x[1],c,d);if(C===0)return p===v?s:[0,T,c,v,l,b];if(0<=C){var N=u(c,v,l);return l===N?s:t(T,d,p,N)}var I=u(c,v,T);return T===I?s:t(I,d,p,l)}function i(c,v,s){for(var l=v,p=s;;){if(!l)return p;var d=l[4],T=l[3],b=l[2],C=c(b,T,i(c,l[1],p)),l=d,p=C}}return[0,0,u,,,,,,,,,,,,,,,function(c,v){for(var s=v;;){if(!s)throw z0(Na,1);var l=s[4],p=s[3],d=s[1],T=k(x[1],c,s[2]);if(T===0)return p;var b=0<=T?l:d,s=b}},,,,,,,i]}function Q6(x){return[0,0,0]}function Z6(x){x[1]=0,x[2]=0}function Fv(x,r){r[1]=[0,x,r[1]],r[2]=r[2]+1|0}function vl(x){var r=x[1];if(!r)return 0;var e=r[1];return x[1]=r[2],x[2]=x[2]-1|0,[0,e]}function ll(x){var r=x[1];return r?[0,r[1]]:0}function dq(x){return[0,0,0,0]}function fN(x){x[1]=0,x[2]=0,x[3]=0}function cN(x,r){var e=[0,x,0],t=r[3];return t?(r[1]=r[1]+1|0,t[2]=e,r[3]=e,0):(r[1]=1,r[2]=e,r[3]=e,0)}var wz="Buffer.add: cannot grow buffer",bz="Buffer.add_substring/add_subbytes";function Wr(x){var r=1<=x?x:1,e=z6<r?z6:r,t=I1(e);return[0,[0,t,e],0,t]}function K1(x){return ol(x[1][1],0,x[2])}function aN(x,r){for(var e=x[2],t=[0,x[1][2]];!(t[1]>=(e+r|0));)t[1]=2*t[1]|0;z6<t[1]&&((e+r|0)<=z6?t[1]=z6:Sx(wz));var u=I1(t[1]);sq(x[1][1],0,u,0,x[2]),x[1]=[0,u,t[1]]}function ht(x,r){var e=x[2],t=x[1],u=t[1];t[2]<=e?(aN(x,1),ls(x[1][1],x[2],r)):zr(u,e,r),x[2]=e+1|0}function sN(x,r,e,t){var u=e<0?1:0;if(u)var c=u;else var i=t<0?1:0,c=i||((Ux(r)-t|0)<e?1:0);c&&X2(bz);var v=x[2],s=x[1],l=v+t|0,p=s[1];return s[2]<l?(aN(x,t),In(r,e,x[1][1],x[2],t)):Pa(r,e,p,v,t),x[2]=l,0}function oN(x,r,e,t){return sN(x,S2(r),e,t)}function cr(x,r){var e=Ux(r),t=x[2],u=x[1],i=t+e|0,c=u[1];u[2]<i?(aN(x,e),In(r,0,x[1][1],x[2],e)):Pa(r,0,c,t,e),x[2]=i}var vN=[0,0];function hq(x){return x!==vN?1:0}vJ(oo(8,vN));var yq=[0,0],Tz=[0,0],Ez=[0,"domain.ml",E8,13];function Oa(x,r){var e=[0,YY(Tz,1),r];if(x)for(var t=[0,e,x[1]];;){var u=rl(yq);if(!(1-ud(yq,u,[0,t,u])))break}return e}function gq(x){for(;;){var r=OL(0),e=r.length-1;if(x<e)return r;for(var t=e;!(x<t);)var t=2*t|0;var u=oo(t,vN);if(pq(r,0,u,0,e),oJ(r,u))return u}}function xp(x,r){var e=x[1];N1(gq(e),e)[1+e]=r}function Rv(x){var r=x[1],e=x[2],t=N1(gq(r),r)[1+r];if(hq(t))return t;var u=h(e,0),i=OL(0),c=N1(i,r)[1+r]===t?(i[1+r]=u,1):0;if(c)return u;var v=N1(i,r)[1+r];if(hq(v))return v;throw z0([0,Nr,Ez],1)}var lN=Oa(0,function(x){return function(r){return 0}});function _q(x){var r=Rv(lN);return xp(lN,function(e){return x(j),h(r,0)})}nq[1]=function(x){return h(Rv(lN),0)};var Az=OS,Sz="@}",Pz="@?",Iz=`@
`,Cz="@.",Nz="@@",Oz="@%",jz=Bj,Dz="%c",Fz="%s",Rz=pF,Mz=KM,Lz=Ej,qz=bF,Uz="%f",Bz="%B",Xz="%{",Gz="%}",Yz="%(",Jz="%)",zz="%a",Kz="%t",Hz="%?",Vz="%r",$z="%_r",Wz=[0,h2,850,23],Qz=[0,h2,837,26],Zz=[0,h2,847,28],xK=[0,h2,815,21],rK=[0,h2,819,21],eK=[0,h2,823,19],tK=[0,h2,827,22],nK=[0,h2,832,30],uK=[0,h2,851,23],iK=[0,h2,836,26],fK=[0,h2,846,28],cK=[0,h2,814,21],aK=[0,h2,818,21],sK=[0,h2,822,19],oK=[0,h2,826,22],vK=[0,h2,831,30];function pN(x){return x[2]===5?12:-6}function wq(x){return[0,0,I1(x)]}function bq(x,r){var e=Bt(x[2]),t=x[1]+r|0;if(e<t){var u=e*2|0,i=t<=u?u:t,c=I1(i);sq(x[2],0,c,0,e),x[2]=c}}function pl(x,r){bq(x,1),ls(x[2],x[1],r),x[1]=x[1]+1|0}function G2(x,r){var e=Ux(r);bq(x,e),In(r,0,x[2],x[1],e),x[1]=x[1]+e|0}function Tq(x){return ol(x[2],0,x[1])}function Eq(x){if(typeof x=="number")switch(x){case 0:return Az;case 1:return Sz;case 2:return Pz;case 3:return Iz;case 4:return Cz;case 5:return Nz;default:return Oz}switch(x[0]){case 0:return x[1];case 1:return x[1];default:return Jx(jz,Sd(1,x[1]))}}function kN(x,r){for(var e=r;;){if(typeof e=="number")return;switch(e[0]){case 0:var t=e[1];G2(x,Dz);var e=t;break;case 1:var u=e[1];G2(x,Fz);var e=u;break;case 2:var i=e[1];G2(x,Rz);var e=i;break;case 3:var c=e[1];G2(x,Mz);var e=c;break;case 4:var v=e[1];G2(x,Lz);var e=v;break;case 5:var s=e[1];G2(x,qz);var e=s;break;case 6:var l=e[1];G2(x,Uz);var e=l;break;case 7:var p=e[1];G2(x,Bz);var e=p;break;case 8:var d=e[2],T=e[1];G2(x,Xz),kN(x,T),G2(x,Gz);var e=d;break;case 9:var b=e[3],C=e[1];G2(x,Yz),kN(x,C),G2(x,Jz);var e=b;break;case 10:var N=e[1];G2(x,zz);var e=N;break;case 11:var I=e[1];G2(x,Kz);var e=I;break;case 12:var F=e[1];G2(x,Hz);var e=F;break;case 13:var L=e[1];G2(x,Vz);var e=L;break;default:var X=e[1];G2(x,$z);var e=X}}}function a2(x){if(typeof x=="number")return 0;switch(x[0]){case 0:return[0,a2(x[1])];case 1:return[1,a2(x[1])];case 2:return[2,a2(x[1])];case 3:return[3,a2(x[1])];case 4:return[4,a2(x[1])];case 5:return[5,a2(x[1])];case 6:return[6,a2(x[1])];case 7:return[7,a2(x[1])];case 8:var r=x[1];return[8,r,a2(x[2])];case 9:var e=x[2],t=x[1];return[9,e,t,a2(x[3])];case 10:return[10,a2(x[1])];case 11:return[11,a2(x[1])];case 12:return[12,a2(x[1])];case 13:return[13,a2(x[1])];default:return[14,a2(x[1])]}}function Y2(x){if(typeof x=="number")return[0,function(y0){},function(y0){},function(y0){},function(y0){}];switch(x[0]){case 0:var r=Y2(x[1]),e=r[2],t=r[1];return[0,function(y0){t(j)},function(y0){e(j)},r[3],r[4]];case 1:var u=Y2(x[1]),i=u[2],c=u[1];return[0,function(y0){c(j)},function(y0){i(j)},u[3],u[4]];case 2:var v=Y2(x[1]),s=v[2],l=v[1];return[0,function(y0){l(j)},function(y0){s(j)},v[3],v[4]];case 3:var p=Y2(x[1]),d=p[2],T=p[1];return[0,function(y0){T(j)},function(y0){d(j)},p[3],p[4]];case 4:var b=Y2(x[1]),C=b[2],N=b[1];return[0,function(y0){N(j)},function(y0){C(j)},b[3],b[4]];case 5:var I=Y2(x[1]),F=I[2],L=I[1];return[0,function(y0){L(j)},function(y0){F(j)},I[3],I[4]];case 6:var X=Y2(x[1]),q=X[2],J=X[1];return[0,function(y0){J(j)},function(y0){q(j)},X[3],X[4]];case 7:var e0=Y2(x[1]),W=e0[2],x0=e0[1];return[0,function(y0){x0(j)},function(y0){W(j)},e0[3],e0[4]];case 8:var i0=Y2(x[2]),f0=i0[2],r0=i0[1];return[0,function(y0){r0(j)},function(y0){f0(j)},i0[3],i0[4]];case 9:var v0=x[2],o0=x[1],w0=Y2(x[3]),t0=w0[4],s0=w0[3],h0=w0[2],p0=w0[1],C0=Y2(g2(a2(o0),v0)),j0=C0[4],P0=C0[3],M0=C0[2],U0=C0[1];return[0,function(y0){p0(j),U0(j)},function(y0){M0(j),h0(j)},function(y0){s0(j),P0(j)},function(y0){j0(j),t0(j)}];case 10:var T0=Y2(x[1]),G0=T0[2],k0=T0[1];return[0,function(y0){k0(j)},function(y0){G0(j)},T0[3],T0[4]];case 11:var G=Y2(x[1]),S0=G[2],Z0=G[1];return[0,function(y0){Z0(j)},function(y0){S0(j)},G[3],G[4]];case 12:var N0=Y2(x[1]),ux=N0[2],ex=N0[1];return[0,function(y0){ex(j)},function(y0){ux(j)},N0[3],N0[4]];case 13:var nx=Y2(x[1]),px=nx[4],D0=nx[3],dx=nx[2],_x=nx[1];return[0,function(y0){_x(j)},function(y0){dx(j)},function(y0){D0(j)},function(y0){px(j)}];default:var K=Y2(x[1]),_0=K[4],U=K[3],m0=K[2],b0=K[1];return[0,function(y0){b0(j)},function(y0){m0(j)},function(y0){U(j)},function(y0){_0(j)}]}}function g2(x,r){x:{r:{e:{t:{n:{u:{i:{if(typeof x!="number"){switch(x[0]){case 0:var e=x[1];if(typeof r!="number")switch(r[0]){case 0:return[0,g2(e,r[1])];case 8:break u;case 9:break i;case 10:break x;case 11:break r;case 12:break e;case 13:break t;case 14:break n}break;case 1:var t=x[1];if(typeof r!="number")switch(r[0]){case 1:return[1,g2(t,r[1])];case 8:break u;case 9:break i;case 10:break x;case 11:break r;case 12:break e;case 13:break t;case 14:break n}break;case 2:var u=x[1];if(typeof r!="number")switch(r[0]){case 2:return[2,g2(u,r[1])];case 8:break u;case 9:break i;case 10:break x;case 11:break r;case 12:break e;case 13:break t;case 14:break n}break;case 3:var i=x[1];if(typeof r!="number")switch(r[0]){case 3:return[3,g2(i,r[1])];case 8:break u;case 9:break i;case 10:break x;case 11:break r;case 12:break e;case 13:break t;case 14:break n}break;case 4:var c=x[1];if(typeof r!="number")switch(r[0]){case 4:return[4,g2(c,r[1])];case 8:break u;case 9:break i;case 10:break x;case 11:break r;case 12:break e;case 13:break t;case 14:break n}break;case 5:var v=x[1];if(typeof r!="number")switch(r[0]){case 5:return[5,g2(v,r[1])];case 8:break u;case 9:break i;case 10:break x;case 11:break r;case 12:break e;case 13:break t;case 14:break n}break;case 6:var s=x[1];if(typeof r!="number")switch(r[0]){case 6:return[6,g2(s,r[1])];case 8:break u;case 9:break i;case 10:break x;case 11:break r;case 12:break e;case 13:break t;case 14:break n}break;case 7:var l=x[1];if(typeof r!="number")switch(r[0]){case 7:return[7,g2(l,r[1])];case 8:break u;case 9:break i;case 10:break x;case 11:break r;case 12:break e;case 13:break t;case 14:break n}break;case 8:var p=x[2],d=x[1];if(typeof r!="number")switch(r[0]){case 8:var T=r[1],b=g2(p,r[2]);return[8,g2(d,T),b];case 10:break x;case 11:break r;case 12:break e;case 13:break t;case 14:break n}throw z0([0,Nr,iK],1);case 9:var C=x[3],N=x[2],I=x[1];if(typeof r!="number")switch(r[0]){case 8:break u;case 9:var F=r[3],L=r[2],X=r[1],q=Y2(g2(a2(N),X)),J=q[4];return q[2].call(null,j),J(j),[9,I,L,g2(C,F)];case 10:break x;case 11:break r;case 12:break e;case 13:break t;case 14:break n}throw z0([0,Nr,fK],1);case 10:var e0=x[1];if(typeof r!="number"&&r[0]===10)return[10,g2(e0,r[1])];throw z0([0,Nr,cK],1);case 11:var W=x[1];if(typeof r!="number")switch(r[0]){case 10:break x;case 11:return[11,g2(W,r[1])]}throw z0([0,Nr,aK],1);case 12:var x0=x[1];if(typeof r!="number")switch(r[0]){case 10:break x;case 11:break r;case 12:return[12,g2(x0,r[1])]}throw z0([0,Nr,sK],1);case 13:var i0=x[1];if(typeof r!="number")switch(r[0]){case 10:break x;case 11:break r;case 12:break e;case 13:return[13,g2(i0,r[1])]}throw z0([0,Nr,oK],1);default:var f0=x[1];if(typeof r!="number")switch(r[0]){case 10:break x;case 11:break r;case 12:break e;case 13:break t;case 14:return[14,g2(f0,r[1])]}throw z0([0,Nr,vK],1)}throw z0([0,Nr,uK],1)}if(typeof r=="number")return 0;switch(r[0]){case 10:break x;case 11:break r;case 12:break e;case 13:break t;case 14:break n;case 8:break u;case 9:break;default:throw z0([0,Nr,Wz],1)}}throw z0([0,Nr,Zz],1)}throw z0([0,Nr,Qz],1)}throw z0([0,Nr,nK],1)}throw z0([0,Nr,tK],1)}throw z0([0,Nr,eK],1)}throw z0([0,Nr,rK],1)}throw z0([0,Nr,xK],1)}var C2=[f1,"CamlinternalFormat.Type_mismatch",Ca(0)];function lK(x){return x?iz:fz}var pK=rD,kK="\\'",mK="\\b",dK="\\t",hK="\\n",yK="\\r";function gK(x,r){var e=Bt(r);if(e===0)return r;var t=I1(e),u=e-1|0,i=0;if(u>=0)for(var c=i;;){zr(t,c,x(me(r,c)));var v=c+1|0;if(u===c)break;var c=v}return t}var _K=W3,wK="%+d",bK="% d",TK=pF,EK="%+i",AK="% i",SK="%x",PK="%#x",IK="%X",CK="%#X",NK="%o",OK="%#o",jK=tM,DK="%Ld",FK="%+Ld",RK="% Ld",MK=bF,LK="%+Li",qK="% Li",UK="%Lx",BK="%#Lx",XK="%LX",GK="%#LX",YK="%Lo",JK="%#Lo",zK="%Lu",KK="%ld",HK="%+ld",VK="% ld",$K=KM,WK="%+li",QK="% li",ZK="%lx",xH="%#lx",rH="%lX",eH="%#lX",tH="%lo",nH="%#lo",uH="%lu",iH="%nd",fH="%+nd",cH="% nd",aH=Ej,sH="%+ni",oH="% ni",vH="%nx",lH="%#nx",pH="%nX",kH="%#nX",mH="%no",dH="%#no",hH="%nu",yH=[0,wn],gH=_n,_H="neg_infinity",wH=dM,bH=dI,TH=[0,h2,1558,4],EH="Printf: bad conversion %[",AH=[0,h2,1626,39],SH=[0,h2,1649,31],PH=[0,h2,1650,31],IH="Printf: bad conversion %_",CH=EM,NH=CR,OH=EM,jH=CR;function Cd(x,r){if(typeof x=="number")return[0,0,r];if(x[0]===0)return[0,[0,x[1],x[2]],r];if(typeof r!="number"&&r[0]===2)return[0,[1,x[1]],r[1]];throw z0(C2,1)}function rp(x,r,e){var t=Cd(x,e);if(typeof r!="number")return[0,t[1],[0,r[1]],t[2]];if(!r)return[0,t[1],0,t[2]];var u=t[2];if(typeof u!="number"&&u[0]===2)return[0,t[1],1,u[1]];throw z0(C2,1)}function w1(x,r){if(typeof x=="number")return[0,0,r];switch(x[0]){case 0:if(typeof r!="number"&&r[0]===0){var e=w1(x[1],r[1]);return[0,[0,e[1]],e[2]]}break;case 1:if(typeof r!="number"&&r[0]===0){var t=w1(x[1],r[1]);return[0,[1,t[1]],t[2]]}break;case 2:var u=x[2],i=Cd(x[1],r),c=i[2],v=i[1];if(typeof c!="number"&&c[0]===1){var s=w1(u,c[1]);return[0,[2,v,s[1]],s[2]]}throw z0(C2,1);case 3:var l=x[2],p=Cd(x[1],r),d=p[2],T=p[1];if(typeof d!="number"&&d[0]===1){var b=w1(l,d[1]);return[0,[3,T,b[1]],b[2]]}throw z0(C2,1);case 4:var C=x[4],N=x[1],I=rp(x[2],x[3],r),F=I[3],L=I[1];if(typeof F!="number"&&F[0]===2){var X=I[2],q=w1(C,F[1]);return[0,[4,N,L,X,q[1]],q[2]]}throw z0(C2,1);case 5:var J=x[4],e0=x[1],W=rp(x[2],x[3],r),x0=W[3],i0=W[1];if(typeof x0!="number"&&x0[0]===3){var f0=W[2],r0=w1(J,x0[1]);return[0,[5,e0,i0,f0,r0[1]],r0[2]]}throw z0(C2,1);case 6:var v0=x[4],o0=x[1],w0=rp(x[2],x[3],r),t0=w0[3],s0=w0[1];if(typeof t0!="number"&&t0[0]===4){var h0=w0[2],p0=w1(v0,t0[1]);return[0,[6,o0,s0,h0,p0[1]],p0[2]]}throw z0(C2,1);case 7:var C0=x[4],j0=x[1],P0=rp(x[2],x[3],r),M0=P0[3],U0=P0[1];if(typeof M0!="number"&&M0[0]===5){var T0=P0[2],G0=w1(C0,M0[1]);return[0,[7,j0,U0,T0,G0[1]],G0[2]]}throw z0(C2,1);case 8:var k0=x[4],G=x[1],S0=rp(x[2],x[3],r),Z0=S0[3],N0=S0[1];if(typeof Z0!="number"&&Z0[0]===6){var ux=S0[2],ex=w1(k0,Z0[1]);return[0,[8,G,N0,ux,ex[1]],ex[2]]}throw z0(C2,1);case 9:var nx=x[2],px=Cd(x[1],r),D0=px[2],dx=px[1];if(typeof D0!="number"&&D0[0]===7){var _x=w1(nx,D0[1]);return[0,[9,dx,_x[1]],_x[2]]}throw z0(C2,1);case 10:var K=w1(x[1],r);return[0,[10,K[1]],K[2]];case 11:var _0=x[1],U=w1(x[2],r);return[0,[11,_0,U[1]],U[2]];case 12:var m0=x[1],b0=w1(x[2],r);return[0,[12,m0,b0[1]],b0[2]];case 13:if(typeof r!="number"&&r[0]===8){var y0=r[1],E0=r[2],$0=x[3],z=x[1];if(Nv([0,x[2]],[0,y0]))throw z0(C2,1);var Dx=w1($0,E0);return[0,[13,z,y0,Dx[1]],Dx[2]]}break;case 14:if(typeof r!="number"&&r[0]===9){var Xx=r[1],K0=r[3],A=x[3],V=x[2],fx=x[1],wx=[0,G1(Xx)];if(Nv([0,G1(V)],wx))throw z0(C2,1);var Ix=w1(A,G1(K0));return[0,[14,fx,Xx,Ix[1]],Ix[2]]}break;case 15:if(typeof r!="number"&&r[0]===10){var ox=w1(x[1],r[1]);return[0,[15,ox[1]],ox[2]]}break;case 16:if(typeof r!="number"&&r[0]===11){var xr=w1(x[1],r[1]);return[0,[16,xr[1]],xr[2]]}break;case 17:var Fx=x[1],H0=w1(x[2],r);return[0,[17,Fx,H0[1]],H0[2]];case 18:var ur=x[2],X0=x[1];if(X0[0]===0){var or=X0[1],Q0=or[2],yx=w1(or[1],r),ix=yx[1],ax=w1(ur,yx[2]);return[0,[18,[0,[0,ix,Q0]],ax[1]],ax[2]]}var $x=X0[1],fr=$x[2],gr=w1($x[1],r),jr=gr[1],c1=w1(ur,gr[2]);return[0,[18,[1,[0,jr,fr]],c1[1]],c1[2]];case 19:if(typeof r!="number"&&r[0]===13){var Dr=w1(x[1],r[1]);return[0,[19,Dr[1]],Dr[2]]}break;case 20:if(typeof r!="number"&&r[0]===1){var e1=x[2],Ex=x[1],_=w1(x[3],r[1]);return[0,[20,Ex,e1,_[1]],_[2]]}break;case 21:if(typeof r!="number"&&r[0]===2){var $=x[1],vx=w1(x[2],r[1]);return[0,[21,$,vx[1]],vx[2]]}break;case 23:var L0=x[2],lx=x[1];if(typeof lx!="number")switch(lx[0]){case 0:return tt(lx,L0,r);case 1:return tt(lx,L0,r);case 2:return tt(lx,L0,r);case 3:return tt(lx,L0,r);case 4:return tt(lx,L0,r);case 5:return tt(lx,L0,r);case 6:return tt(lx,L0,r);case 7:return tt(lx,L0,r);case 8:return tt([8,lx[1],lx[2]],L0,r);case 9:var Px=lx[1],Ar=De(lx[2],L0,r),Hx=Ar[2];return[0,[23,[9,Px,Ar[1]],Hx[1]],Hx[2]];case 10:return tt(lx,L0,r);default:return tt(lx,L0,r)}switch(lx){case 0:return tt(lx,L0,r);case 1:return tt(lx,L0,r);case 2:if(typeof r!="number"&&r[0]===14){var a1=w1(L0,r[1]);return[0,[23,2,a1[1]],a1[2]]}throw z0(C2,1);default:return tt(lx,L0,r)}}throw z0(C2,1)}function tt(x,r,e){var t=w1(r,e);return[0,[23,x,t[1]],t[2]]}function De(x,r,e){if(typeof x=="number")return[0,0,w1(r,e)];switch(x[0]){case 0:if(typeof e!="number"&&e[0]===0){var t=De(x[1],r,e[1]);return[0,[0,t[1]],t[2]]}break;case 1:if(typeof e!="number"&&e[0]===1){var u=De(x[1],r,e[1]);return[0,[1,u[1]],u[2]]}break;case 2:if(typeof e!="number"&&e[0]===2){var i=De(x[1],r,e[1]);return[0,[2,i[1]],i[2]]}break;case 3:if(typeof e!="number"&&e[0]===3){var c=De(x[1],r,e[1]);return[0,[3,c[1]],c[2]]}break;case 4:if(typeof e!="number"&&e[0]===4){var v=De(x[1],r,e[1]);return[0,[4,v[1]],v[2]]}break;case 5:if(typeof e!="number"&&e[0]===5){var s=De(x[1],r,e[1]);return[0,[5,s[1]],s[2]]}break;case 6:if(typeof e!="number"&&e[0]===6){var l=De(x[1],r,e[1]);return[0,[6,l[1]],l[2]]}break;case 7:if(typeof e!="number"&&e[0]===7){var p=De(x[1],r,e[1]);return[0,[7,p[1]],p[2]]}break;case 8:if(typeof e!="number"&&e[0]===8){var d=e[1],T=e[2],b=x[2];if(Nv([0,x[1]],[0,d]))throw z0(C2,1);var C=De(b,r,T);return[0,[8,d,C[1]],C[2]]}break;case 9:if(typeof e!="number"&&e[0]===9){var N=e[2],I=e[1],F=e[3],L=x[3],X=x[2],q=x[1],J=[0,G1(I)];if(Nv([0,G1(q)],J))throw z0(C2,1);var e0=[0,G1(N)];if(Nv([0,G1(X)],e0))throw z0(C2,1);var W=Y2(g2(a2(I),N)),x0=W[4];W[2].call(null,j),x0(j);var i0=De(G1(L),r,F),f0=i0[2];return[0,[9,I,N,a2(i0[1])],f0]}break;case 10:if(typeof e!="number"&&e[0]===10){var r0=De(x[1],r,e[1]);return[0,[10,r0[1]],r0[2]]}break;case 11:if(typeof e!="number"&&e[0]===11){var v0=De(x[1],r,e[1]);return[0,[11,v0[1]],v0[2]]}break;case 13:if(typeof e!="number"&&e[0]===13){var o0=De(x[1],r,e[1]);return[0,[13,o0[1]],o0[2]]}break;case 14:if(typeof e!="number"&&e[0]===14){var w0=De(x[1],r,e[1]);return[0,[14,w0[1]],w0[2]]}break}throw z0(C2,1)}function nt(x,r,e){var t=Ux(e),u=0<=r?x:0,i=Td(r);if(i<=t)return e;var c=u===2?48:32,v=Dv(i,c);switch(u){case 0:In(e,0,v,0,t);break;case 1:In(e,0,v,i-t|0,t);break;default:x:if(0<t){if(B1(e,0)!==43&&B1(e,0)!==45&&B1(e,0)!==32)break x;ls(v,0,B1(e,0)),In(e,1,v,(i-t|0)+1|0,t-1|0);break}x:if(1<t&&B1(e,0)===48){if(Cf!==B1(e,1)&&B1(e,1)!==88)break x;ls(v,1,B1(e,1)),In(e,2,v,(i-t|0)+2|0,t-2|0);break}In(e,0,v,i-t|0,t)}return S2(v)}function kl(x,r){var e=Td(x),t=Ux(r),u=B1(r,0);x:{r:{if(58>u){if(u!==32){if(43>u)break x;switch(u+Mw|0){case 5:e:if(t<(e+2|0)&&1<t){if(Cf!==B1(r,1)&&B1(r,1)!==88)break e;var i=Dv(e+2|0,48);return ls(i,1,B1(r,1)),In(r,2,i,(e-t|0)+4|0,t-2|0),S2(i)}break r;case 0:case 2:break;case 1:case 3:case 4:break x;default:break r}}if(t>=(e+1|0))break x;var c=Dv(e+1|0,48);return ls(c,0,u),In(r,1,c,(e-t|0)+2|0,t-1|0),S2(c)}if(71<=u){if(5<u+gk>>>0)break x}else if(65>u)break x}if(t<e){var v=Dv(e,48);return In(r,0,v,e-t|0,t),S2(v)}}return r}function DH(x){var r=qt(x),e=[0,0],t=Bt(r)-1|0,u=0;if(t>=0)for(var i=u;;){var c=me(r,i);x:{r:{e:{if(32<=c){var v=c-34|0;if(58<v>>>0){if(93<=v)break e}else if(56<v-1>>>0)break r;var s=1;break x}if(11<=c){if(c===13)break r}else if(8<=c)break r}var s=4;break x}var s=2}e[1]=e[1]+s|0;var l=i+1|0;if(t===i)break;var i=l}if(e[1]===Bt(r))var p=r;else{var d=I1(e[1]);e[1]=0;var T=Bt(r)-1|0,b=0;if(T>=0)for(var C=b;;){var N=me(r,C);x:{r:{e:{if(35<=N){if(N!==92){if(Jr<=N)break e;break r}}else{if(32>N){if(14<=N)break e;switch(N){case 8:zr(d,e[1],92),e[1]++,zr(d,e[1],98);break x;case 9:zr(d,e[1],92),e[1]++,zr(d,e[1],Tv);break x;case 10:zr(d,e[1],92),e[1]++,zr(d,e[1],B2);break x;case 13:zr(d,e[1],92),e[1]++,zr(d,e[1],br);break x;default:break e}}if(34>N)break r}zr(d,e[1],92),e[1]++,zr(d,e[1],N);break x}zr(d,e[1],92),e[1]++,zr(d,e[1],48+(N/E1|0)|0),e[1]++,zr(d,e[1],48+((N/10|0)%10|0)|0),e[1]++,zr(d,e[1],48+(N%10|0)|0);break x}zr(d,e[1],N)}e[1]++;var I=C+1|0;if(T===C)break;var C=I}var p=d}var F=S2(p),L=Ux(F),X=Dv(L+2|0,34);return Pa(F,0,X,1,L),S2(X)}function Aq(x,r){var e=Td(r),t=yH[1];switch(x[2]){case 0:var u=E2;break;case 1:var u=pe;break;case 2:var u=69;break;case 3:var u=wn;break;case 4:var u=71;break;case 5:var u=t;break;case 6:var u=We;break;case 7:var u=72;break;default:var u=70}var i=wq(16);switch(pl(i,37),x[1]){case 0:break;case 1:pl(i,43);break;default:pl(i,32)}return 8<=x[2]&&pl(i,35),pl(i,46),G2(i,tx+e),pl(i,u),Tq(i)}function Nd(x,r){if(13>x)return r;var e=[0,0],t=Ux(r)-1|0,u=0;if(t>=0)for(var i=u;;){9>=J0(r,i)+t2>>>0&&e[1]++;var c=i+1|0;if(t===i)break;var i=c}var v=e[1],s=I1(Ux(r)+((v-1|0)/3|0)|0),l=[0,0];function p(F){ls(s,l[1],F),l[1]++}var d=[0,((v-1|0)%3|0)+1|0],T=Ux(r)-1|0,b=0;if(T>=0)for(var C=b;;){var N=J0(r,C);9<N+t2>>>0||(d[1]===0&&(p(95),d[1]=3),d[1]+=-1),p(N);var I=C+1|0;if(T===C)break;var C=I}return S2(s)}function FH(x,r){switch(x){case 1:var e=wK;break;case 2:var e=bK;break;case 4:var e=EK;break;case 5:var e=AK;break;case 6:var e=SK;break;case 7:var e=PK;break;case 8:var e=IK;break;case 9:var e=CK;break;case 10:var e=NK;break;case 11:var e=OK;break;case 0:case 13:var e=_K;break;case 3:case 14:var e=TK;break;default:var e=jK}return Nd(x,vd(e,r))}function RH(x,r){switch(x){case 1:var e=HK;break;case 2:var e=VK;break;case 4:var e=WK;break;case 5:var e=QK;break;case 6:var e=ZK;break;case 7:var e=xH;break;case 8:var e=rH;break;case 9:var e=eH;break;case 10:var e=tH;break;case 11:var e=nH;break;case 0:case 13:var e=KK;break;case 3:case 14:var e=$K;break;default:var e=uH}return Nd(x,vd(e,r))}function MH(x,r){switch(x){case 1:var e=fH;break;case 2:var e=cH;break;case 4:var e=sH;break;case 5:var e=oH;break;case 6:var e=vH;break;case 7:var e=lH;break;case 8:var e=pH;break;case 9:var e=kH;break;case 10:var e=mH;break;case 11:var e=dH;break;case 0:case 13:var e=iH;break;case 3:case 14:var e=aH;break;default:var e=hH}return Nd(x,vd(e,r))}function LH(x,r){switch(x){case 1:var e=FK;break;case 2:var e=RK;break;case 4:var e=LK;break;case 5:var e=qK;break;case 6:var e=UK;break;case 7:var e=BK;break;case 8:var e=XK;break;case 9:var e=GK;break;case 10:var e=YK;break;case 11:var e=JK;break;case 0:case 13:var e=DK;break;case 3:case 14:var e=MK;break;default:var e=zK}return Nd(x,FL(e,r))}function gs(x,r,e){function t(d){switch(x[1]){case 0:var T=45;break;case 1:var T=43;break;default:var T=32}return kJ(e,r,T)}function u(d){var T=QY(e);return T===3?e<0?_H:wH:4<=T?bH:d}switch(x[2]){case 5:for(var i=YC(Aq(x,r),e),c=0,v=Ux(i);;){if(c===v)var s=0;else{var l=B1(i,c)+fo|0;x:{if(23<l>>>0){if(l===55)break x}else if(21<l-1>>>0)break x;var c=c+1|0;continue}var s=1}var p=s?i:Jx(i,gH);return u(p)}case 6:return t(j);case 7:return S2(gK(iq,qt(t(j))));case 8:return u(t(j));default:return YC(Aq(x,r),e)}}function ep(x,r,e,t){for(var u=r,i=e,c=t;;){if(typeof c=="number")return u(i);switch(c[0]){case 0:var v=c[1];return function(T0){return Gr(u,[5,i,T0],v)};case 1:var s=c[1];return function(T0){x:{r:{if(40<=T0){if(T0===92){var G=pK;break x}if(Jr>T0)break r}else{if(32<=T0){if(39>T0)break r;var G=kK;break x}if(14>T0)switch(T0){case 8:var G=mK;break x;case 9:var G=dK;break x;case 10:var G=hK;break x;case 13:var G=yK;break x}}var G0=I1(4);zr(G0,0,92),zr(G0,1,48+(T0/E1|0)|0),zr(G0,2,48+((T0/10|0)%10|0)|0),zr(G0,3,48+(T0%10|0)|0);var G=S2(G0);break x}var k0=I1(1);zr(k0,0,T0);var G=S2(k0)}var S0=Ux(G),Z0=Dv(S0+2|0,39);return Pa(G,0,Z0,1,S0),Gr(u,[4,i,S2(Z0)],s)};case 2:return dN(u,i,c[2],c[1],function(T0){return T0});case 3:return dN(u,i,c[2],c[1],DH);case 4:return Od(u,i,c[4],c[2],c[3],FH,c[1]);case 5:return Od(u,i,c[4],c[2],c[3],RH,c[1]);case 6:return Od(u,i,c[4],c[2],c[3],MH,c[1]);case 7:return Od(u,i,c[4],c[2],c[3],LH,c[1]);case 8:var l=c[4],p=c[3],d=c[2],T=c[1];if(typeof d=="number"){if(typeof p=="number")return p?function(T0,G0){return Gr(u,[4,i,gs(T,T0,G0)],l)}:function(T0){return Gr(u,[4,i,gs(T,pN(T),T0)],l)};var b=p[1];return function(T0){return Gr(u,[4,i,gs(T,b,T0)],l)}}if(d[0]===0){var C=d[2],N=d[1];if(typeof p=="number")return p?function(T0,G0){return Gr(u,[4,i,nt(N,C,gs(T,T0,G0))],l)}:function(T0){return Gr(u,[4,i,nt(N,C,gs(T,pN(T),T0))],l)};var I=p[1];return function(T0){return Gr(u,[4,i,nt(N,C,gs(T,I,T0))],l)}}var F=d[1];if(typeof p=="number")return p?function(T0,G0,k0){return Gr(u,[4,i,nt(F,T0,gs(T,G0,k0))],l)}:function(T0,G0){return Gr(u,[4,i,nt(F,T0,gs(T,pN(T),G0))],l)};var L=p[1];return function(T0,G0){return Gr(u,[4,i,nt(F,T0,gs(T,L,G0))],l)};case 9:return dN(u,i,c[2],c[1],lK);case 10:var i=[7,i],c=c[1];break;case 11:var i=[2,i,c[1]],c=c[2];break;case 12:var i=[3,i,c[1]],c=c[2];break;case 13:var X=c[3],q=c[2],J=wq(16);kN(J,q);var e0=Tq(J);return function(T0){return Gr(u,[4,i,e0],X)};case 14:var W=c[3],x0=c[2];return function(T0){var G0=T0[1],k0=w1(G0,G1(a2(x0)));if(typeof k0[2]=="number")return Gr(u,i,O1(k0[1],W));throw z0(C2,1)};case 15:var i0=c[1];return function(T0,G0){return Gr(u,[6,i,function(k0){return k(T0,k0,G0)}],i0)};case 16:var f0=c[1];return function(T0){return Gr(u,[6,i,T0],f0)};case 17:var i=[0,i,c[1]],c=c[2];break;case 18:var r0=c[1];if(r0[0]===0){let T0=i,G0=u,k0=c[2];var u=function(N0){return Gr(G0,[1,T0,[0,N0]],k0)},i=0,c=r0[1][1]}else{let T0=i,G0=u,k0=c[2];var u=function(N0){return Gr(G0,[1,T0,[1,N0]],k0)},i=0,c=r0[1][1]}break;case 19:throw z0([0,Nr,TH],1);case 20:var v0=c[3],o0=[8,i,EH];return function(T0){return Gr(u,o0,v0)};case 21:var w0=c[2];return function(T0){return Gr(u,[4,i,vd(tM,T0)],w0)};case 22:var t0=c[1];return function(T0){return Gr(u,[5,i,T0],t0)};case 23:var s0=c[2],h0=c[1];if(typeof h0=="number")switch(h0){case 0:return x<50?k1(x+1|0,u,i,s0):z1(k1,[0,u,i,s0]);case 1:return x<50?k1(x+1|0,u,i,s0):z1(k1,[0,u,i,s0]);case 2:throw z0([0,Nr,AH],1);default:return x<50?k1(x+1|0,u,i,s0):z1(k1,[0,u,i,s0])}switch(h0[0]){case 0:return x<50?k1(x+1|0,u,i,s0):z1(k1,[0,u,i,s0]);case 1:return x<50?k1(x+1|0,u,i,s0):z1(k1,[0,u,i,s0]);case 2:return x<50?k1(x+1|0,u,i,s0):z1(k1,[0,u,i,s0]);case 3:return x<50?k1(x+1|0,u,i,s0):z1(k1,[0,u,i,s0]);case 4:return x<50?k1(x+1|0,u,i,s0):z1(k1,[0,u,i,s0]);case 5:return x<50?k1(x+1|0,u,i,s0):z1(k1,[0,u,i,s0]);case 6:return x<50?k1(x+1|0,u,i,s0):z1(k1,[0,u,i,s0]);case 7:return x<50?k1(x+1|0,u,i,s0):z1(k1,[0,u,i,s0]);case 8:return x<50?k1(x+1|0,u,i,s0):z1(k1,[0,u,i,s0]);case 9:var p0=h0[2];return x<50?mN(x+1|0,u,i,p0,s0):z1(mN,[0,u,i,p0,s0]);case 10:return x<50?k1(x+1|0,u,i,s0):z1(k1,[0,u,i,s0]);default:return x<50?k1(x+1|0,u,i,s0):z1(k1,[0,u,i,s0])}default:var C0=c[3],j0=c[1],P0=h(c[2],0);return x<50?hN(x+1|0,u,i,C0,j0,P0):z1(hN,[0,u,i,C0,j0,P0])}}}function Gr(x,r,e){return wd(ep(0,x,r,e))}function mN(x,r,e,t,u){if(typeof t=="number")return x<50?k1(x+1|0,r,e,u):z1(k1,[0,r,e,u]);switch(t[0]){case 0:var i=t[1];return function(q){return yt(r,e,i,u)};case 1:var c=t[1];return function(q){return yt(r,e,c,u)};case 2:var v=t[1];return function(q){return yt(r,e,v,u)};case 3:var s=t[1];return function(q){return yt(r,e,s,u)};case 4:var l=t[1];return function(q){return yt(r,e,l,u)};case 5:var p=t[1];return function(q){return yt(r,e,p,u)};case 6:var d=t[1];return function(q){return yt(r,e,d,u)};case 7:var T=t[1];return function(q){return yt(r,e,T,u)};case 8:var b=t[2];return function(q){return yt(r,e,b,u)};case 9:var C=t[3],N=t[2],I=g2(a2(t[1]),N);return function(q){return yt(r,e,he(I,C),u)};case 10:var F=t[1];return function(q,J){return yt(r,e,F,u)};case 11:var L=t[1];return function(q){return yt(r,e,L,u)};case 12:var X=t[1];return function(q){return yt(r,e,X,u)};case 13:throw z0([0,Nr,SH],1);default:throw z0([0,Nr,PH],1)}}function yt(x,r,e,t){return wd(mN(0,x,r,e,t))}function k1(x,r,e,t){var u=[8,e,IH];return x<50?ep(x+1|0,r,u,t):z1(ep,[0,r,u,t])}function dN(x,r,e,t,u){if(typeof t=="number")return function(s){return Gr(x,[4,r,u(s)],e)};if(t[0]===0){var i=t[2],c=t[1];return function(s){return Gr(x,[4,r,nt(c,i,u(s))],e)}}var v=t[1];return function(s,l){return Gr(x,[4,r,nt(v,s,u(l))],e)}}function Od(x,r,e,t,u,i,c){if(typeof t=="number"){if(typeof u=="number")return u?function(b,C){return Gr(x,[4,r,kl(b,i(c,C))],e)}:function(b){return Gr(x,[4,r,i(c,b)],e)};var v=u[1];return function(b){return Gr(x,[4,r,kl(v,i(c,b))],e)}}if(t[0]===0){var s=t[2],l=t[1];if(typeof u=="number")return u?function(b,C){return Gr(x,[4,r,nt(l,s,kl(b,i(c,C)))],e)}:function(b){return Gr(x,[4,r,nt(l,s,i(c,b))],e)};var p=u[1];return function(b){return Gr(x,[4,r,nt(l,s,kl(p,i(c,b)))],e)}}var d=t[1];if(typeof u=="number")return u?function(b,C,N){return Gr(x,[4,r,nt(d,b,kl(C,i(c,N)))],e)}:function(b,C){return Gr(x,[4,r,nt(d,b,i(c,C))],e)};var T=u[1];return function(b,C){return Gr(x,[4,r,nt(d,b,kl(T,i(c,C)))],e)}}function hN(x,r,e,t,u,i){if(u){var c=u[1];return function(s){return qH(r,e,t,c,h(i,s))}}var v=[4,e,i];return x<50?ep(x+1|0,r,v,t):z1(ep,[0,r,v,t])}function qH(x,r,e,t,u){return wd(hN(0,x,r,e,t,u))}function _s(x,r){for(var e=r;;){if(typeof e=="number")return;switch(e[0]){case 0:var t=e[1],u=Eq(e[2]);return _s(x,t),J6(x,u);case 1:var i=e[2],c=e[1];if(i[0]===0){var v=i[1];_s(x,c),J6(x,CH);var e=v}else{var s=i[1];_s(x,c),J6(x,NH);var e=s}break;case 6:var l=e[2];return _s(x,e[1]),h(l,x);case 7:_s(x,e[1]),bn(x);return;case 8:var p=e[2];return _s(x,e[1]),X2(p);case 2:case 4:var d=e[2];return _s(x,e[1]),J6(x,d);default:var T=e[2];_s(x,e[1]),KL(x,T);return}}}function ws(x,r){for(var e=r;;){if(typeof e=="number")return;switch(e[0]){case 0:var t=e[1],u=Eq(e[2]);return ws(x,t),cr(x,u);case 1:var i=e[2],c=e[1];if(i[0]===0){var v=i[1];ws(x,c),cr(x,OH);var e=v}else{var s=i[1];ws(x,c),cr(x,jH);var e=s}break;case 6:var l=e[2];return ws(x,e[1]),cr(x,h(l,0));case 7:var e=e[1];break;case 8:var p=e[2];return ws(x,e[1]),X2(p);case 2:case 4:var d=e[2];return ws(x,e[1]),cr(x,d);default:var T=e[2];return ws(x,e[1]),ht(x,T)}}}function Sq(x,r){return Gr(function(e){return _s(x,e),0},0,r[1])}function yN(x){return Sq(Sn,x)}function ar(x){return Gr(function(r){var e=Wr(64);return ws(e,r),K1(e)},0,x[1])}var gN=[0,0],UH=_n,BH=[0,[3,0,0],v6],XH=ev,GH=[0,[4,0,0,0,0],W3],YH=tx,JH=[0,[11,AF,[2,0,[2,0,0]]],", %s%s"],zH=[0,[12,40,[2,0,[2,0,[12,41,0]]]],"(%s%s)"],KH=tx,HH=tx,VH=[0,[12,40,[2,0,[12,41,0]]],"(%s)"],$H="Out of memory",WH="Stack overflow",QH="Pattern matching failed",ZH="Assertion failed",xV="Undefined recursive module",rV="Raised at",eV="Re-raised at",tV="Raised by primitive operation at",nV="Called from",uV=[0,[12,32,[4,0,0,0,0]]," %d"],iV=" (inlined)",fV=[0,[2,0,[12,32,[2,0,[11,' in file "',[2,0,[12,34,[2,0,[11,", line",[2,0,[11,Rj,uz]]]]]]]]]],'%s %s in file "%s"%s, line%s, characters %d-%d'],cV=tx,aV=[0,[11,"s ",[4,0,0,0,[12,45,[4,0,0,0,0]]]],"s %d-%d"],sV=[0,[2,0,[11," unknown location",0]],"%s unknown location"],oV=[0,[2,0,[12,10,0]],`%s
`];function _N(x,r){var e=x[1+r];if(!(1-(typeof e=="number"?1:0)))return h(ar(GH),e);if(Ov(e)===q3)return h(ar(BH),e);if(Ov(e)!==_E)return XH;for(var t=YC("%.12g",e),u=0,i=Ux(t);;){if(i<=u)return Jx(t,UH);var c=B1(t,u);x:{if(48<=c){if(58>c)break x}else if(c===45)break x;return t}var u=u+1|0}}function Pq(x,r){if(x.length-1<=r)return YH;var e=Pq(x,r+1|0),t=_N(x,r);return k(ar(JH),t,e)}function tp(x){x:{r:{for(var r=rl(gN);r;){e:{var e=r[2],t=r[1];try{var u=h(t,x)}catch{break e}if(u)break r}var r=e}var i=0;break x}var i=[0,u[1]]}if(i)return i[1];if(x===xN)return $H;if(x===QL)return WH;if(x[1]===WL){var c=x[2],v=c[3],s=c[2],l=c[1];return hs(ar(rN),l,s,v,v+5|0,QH)}if(x[1]===Nr){var p=x[2],d=p[3],T=p[2],b=p[1];return hs(ar(rN),b,T,d,d+6|0,ZH)}if(x[1]===Y6){var C=x[2],N=C[3],I=C[2],F=C[1];return hs(ar(rN),F,I,N,N+6|0,xV)}if(Ov(x)===0){var L=x.length-1,X=x[1][1];if(2<L>>>0)var q=Pq(x,2),J=_N(x,1),e0=k(ar(zH),J,q);else switch(L){case 0:var e0=KH;break;case 1:var e0=HH;break;default:var W=_N(x,1),e0=h(ar(VH),W)}var x0=[0,X,[0,e0]]}else var x0=[0,x[1],0];var i0=x0[2],f0=x0[1];return i0?Jx(f0,i0[1]):f0}function wN(x,r){var e=sJ(r),t=e.length-1-1|0,u=0;if(t>=0)for(var i=u;;){var c=N1(e,i)[1+i];let e0=i;var v=function(x0){return x0?e0===0?rV:eV:e0===0?tV:nV};if(c[0]===0){if(c[3]===c[6])var s=c[3],d=h(ar(uV),s);else var l=c[6],p=c[3],d=k(ar(aV),p,l);var T=c[7],b=c[4],C=c[8]?iV:cV,N=c[2],I=c[9],F=v(c[1]),X=[0,nz(ar(fV),F,I,N,C,d,b,T)]}else if(c[1])var X=0;else var L=v(0),X=[0,h(ar(sV),L)];if(X){var q=X[1];h(Sq(x,oV),q)}var J=i+1|0;if(t===i)break;var i=J}}function bN(x){for(;;){var r=rl(gN),e=1-ud(gN,r,[0,x,r]);if(!e)return e}}var vV=[0,tx,`(Cannot print locations:
 bytecode executable program file not found)`,`(Cannot print locations:
 bytecode executable program file appears to be corrupt)`,`(Cannot print locations:
 bytecode executable program file has wrong magic number)`,`(Cannot print locations:
 bytecode executable program file cannot be opened;
 -- too many open files. Try running with OCAMLRUNPARAM=b=2)`].slice(),lV=[0,[11,xA,[2,0,[12,10,0]]],ZR],pV=[0],kV="Fatal error: out of memory in uncaught exception handler",mV=[0,[11,xA,[2,0,[12,10,0]]],ZR],dV=[0,[11,"Fatal error in uncaught exception handler: exception ",[2,0,[12,10,0]]],`Fatal error in uncaught exception handler: exception %s
`];ZC(Kj,function(x,r){try{try{var e=r?pV:DL(0);try{tN(j)}catch{}try{var t=tp(x);h(yN(lV),t),wN(Sn,e);var u=RJ(0);if(u<0){var i=Td(u);tq(N1(vV,i)[1+i])}var c=bn(Sn),v=c}catch(b){var s=X1(b),l=tp(x);h(yN(mV),l),wN(Sn,e);var p=tp(s);h(yN(dV),p),wN(Sn,DL(0));var v=bn(Sn)}var d=v}catch(b){var T=X1(b);if(T!==xN)throw z0(T,0);var d=tq(kV)}return d}catch{return 0}});var hV=[f1,"Stdlib.Fun.Finally_raised",Ca(0)],yV="Fun.Finally_raised: ";bN(function(x){return x[1]===hV?[0,Jx(yV,tp(x[2]))]:0});var gV="Digest.BLAKE2: wrong hash size";function TN(x){var r=x[1]<1?1:0,e=r||(64<x[1]?1:0);e&&X2(gV)}TN([0,16]),TN([0,32]),TN([0,64]);function Iq(x){var r=S2(x);return FJ(r,0,Ux(r))}var _V=mt(1,0,0),wV=mt(0,0,0),bV=mt(0,0,0),TV=mt(2,0,0),EV=mt(1,0,0);function Cq(x){return VY(7,0,[0,4])}function Nq(x,r,e,t,u){M6(x,0,qL(r,_V)),M6(x,1,e);var i=Nv(t,wV)?t:EV;M6(x,2,i);var c=Nv(u,bV)?u:TV;M6(x,3,c)}function Oq(x,r,e,t){var u=Cq(j);return Nq(u,x,r,e,t),u}var AV=mt(14371852,15349651,22696),SV=mt(12230193,11438743,35013),PV=mt(1424933,15549263,2083),IV=mt(9492471,4696708,SF);Oa([0,function(x){var r=dd(x),e=dd(x),t=dd(x);return Oq(r,e,t,dd(x))}],function(x){return Oq(IV,PV,SV,AV)});var jd=0,jq=-1,EN=[f1,"Stdlib.Format.String_tag",Ca(0)];function np(x,r){return x[13]=x[13]+r[3]|0,cN(r,x[28])}var Dq=1000000010;function CV(x,r){return x<=r?x:r}var NV=[f1,"Stdlib.Queue.Empty",Ca(0)],OV=[0,tx,0,tx],jV=tx,DV=tx,FV=tx,RV=tx,MV=[0,tx],LV=N_;function AN(x,r){return xx(x[17],r,0,Ux(r))}function Dd(x){return h(x[19],0)}function Fq(x,r,e){x[9]=x[9]-r|0,AN(x,e),x[11]=0}function Fd(x,r){var e=P(r,tx);return e&&Fq(x,Ux(r),r)}function Mv(x,r,e){var t=r[3],u=r[2];return Fd(x,r[1]),Dd(x),x[11]=1,x[10]=CV(x[8],(x[6]-e|0)+u|0),x[9]=x[6]-x[10]|0,h(x[21],x[10]),Fd(x,t)}function Rq(x,r){return Mv(x,OV,r)}function ml(x,r){var e=r[2],t=r[3];return Fd(x,r[1]),x[9]=x[9]-e|0,h(x[20],e),Fd(x,t)}function qV(x,r,e){if(typeof e=="number")switch(e){case 0:var t=ll(x[3]);if(!t)return;var u=t[1][1],i=function(Z0,N0){if(!N0)return[0,Z0,0];var ux=N0[1],ex=N0[2];return EJ(Z0,ux)?[0,Z0,N0]:[0,ux,i(Z0,ex)]};u[1]=i(x[6]-x[9]|0,u[1]);return;case 1:vl(x[2]);return;case 2:vl(x[3]);return;case 3:var c=ll(x[2]);return c?Rq(x,c[1][2]):Dd(x);case 4:var v=x[10]!==(x[6]-x[9]|0)?1:0;if(!v)return v;var s=x[28],l=s[2];if(l){var p=l[1];if(l[2]){var d=l[2];s[1]=s[1]-1|0,s[2]=d;var T=[0,p]}else{fN(s);var T=[0,p]}}else var T=0;if(!T)return;var b=T[1],C=b[1];x[12]=x[12]-b[3]|0,x[9]=x[9]+C|0;return;default:var N=vl(x[5]);return N?AN(x,h(x[25],N[1])):void 0}switch(e[0]){case 0:return Fq(x,r,e[1]);case 1:var I=e[2],F=e[1],L=I[1],X=I[2],q=ll(x[2]);if(!q)return;var J=q[1],e0=J[2];switch(J[1]){case 0:return ml(x,F);case 1:return Mv(x,I,e0);case 2:return Mv(x,I,e0);case 3:return x[9]<(r+Ux(L)|0)?Mv(x,I,e0):ml(x,F);case 4:return x[11]?ml(x,F):x[9]<(r+Ux(L)|0)||((x[6]-e0|0)+X|0)<x[10]?Mv(x,I,e0):ml(x,F);default:return ml(x,F)}case 2:var W=x[6]-x[9]|0,x0=e[2],i0=e[1],f0=ll(x[3]);if(!f0)return;var r0=f0[1][1],v0=r0[1];if(v0)for(var o0=r0[1],w0=v0[1];;){if(o0){var t0=o0[1],s0=o0[2];if(W>t0){var o0=s0;continue}var h0=t0}else var h0=w0;var p0=h0;break}else var p0=W;var C0=p0-W|0;return 0<=C0?ml(x,[0,DV,C0+i0|0,jV]):Mv(x,[0,RV,p0+x0|0,FV],x[6]);case 3:var j0=e[2],P0=e[1];if(x[8]<(x[6]-x[9]|0)){var M0=ll(x[2]);if(M0){var U0=M0[1],T0=U0[2],G0=U0[1];x[9]<T0&&3>=G0-1>>>0&&Rq(x,T0)}else Dd(x)}var k0=x[9]-P0|0,G=j0===1?1:x[9]<r?j0:5;return Fv([0,G,k0],x[2]);case 4:return Fv(e[1],x[3]);default:var S0=e[1];return AN(x,h(x[24],S0)),Fv(S0,x[5])}}function Mq(x){for(;;){var r=x[28][2],e=r?[0,r[1]]:0;if(!e)return;var t=e[1],u=t[1],i=0<=u?1:0,c=t[3],v=t[2],s=x[13]-x[12]|0,l=i||(x[9]<=s?1:0);if(!l)return l;var p=x[28],d=p[2];if(!d)throw z0(NV,1);if(d[2]){var T=d[2];p[1]=p[1]-1|0,p[2]=T}else fN(p);var b=0<=u?u:Dq;qV(x,b,v),x[12]=c+x[12]|0}}function Lq(x,r){return np(x,r),Mq(x)}function qq(x,r,e){return Lq(x,[0,r,[0,e],r])}function SN(x){return Z6(x),Fv([0,-1,[0,jq,MV,0]],x)}function PN(x,r){var e=ll(x[1]);if(e){var t=e[1],u=t[2],i=u[1];if(t[1]<x[12])return SN(x[1]);var c=u[2];if(typeof c!="number")switch(c[0]){case 3:1-r&&(u[1]=x[13]+i|0,vl(x[1]));return;case 1:case 2:r&&(u[1]=x[13]+i|0,vl(x[1]));return}}}function Uq(x,r,e){return np(x,e),r&&PN(x,1),Fv([0,x[13],e],x[1])}function Bq(x,r,e){if(x[14]=x[14]+1|0,x[14]<x[15])return Uq(x,0,[0,-x[13]|0,[3,r,e],0]);var t=x[14]===x[15]?1:0;if(!t)return t;var u=x[16];return qq(x,Ux(u),u)}function Xq(x,r){1<x[14]&&(x[14]<x[15]&&(np(x,[0,jd,1,0]),PN(x,1),PN(x,0)),x[14]=x[14]-1|0)}function Gq(x,r){if(x[23]&&np(x,[0,jd,5,0]),x[22]){var e=vl(x[4]);if(e)return h(x[27],e[1])}}function Yq(x,r){for(P2(function(e){return Gq(x,j)},x[4][1]);!(1>=x[14]);)Xq(x,j);return x[13]=Dq,Mq(x),r&&Dd(x),x[12]=1,x[13]=1,fN(x[28]),SN(x[1]),Z6(x[2]),Z6(x[3]),Z6(x[4]),Z6(x[5]),x[10]=0,x[14]=0,x[9]=x[6],Bq(x,0,3)}function IN(x,r,e){var t=x[14]<x[15]?1:0;return t&&qq(x,r,e)}function Jq(x,r,e){return IN(x,r,e)}function up(x,r){return Jq(x,1,Sd(1,r))}function Lv(x,r){return Yq(x,0),h(x[18],0)}function CN(x,r){return xx(x[17],LV,0,1)}var zq=Sd(80,32),UV=sR,BV=d6,XV=Ey,GV=tx,YV=d6,JV="</",zV=tx,KV=[3,0,3],HV=_n;function dl(x,r){for(var e=r;;){var t=0<e?1:0;if(!t)return t;if(80>=e)return xx(x[17],zq,0,e);xx(x[17],zq,0,80);var e=e-80|0}}function VV(x){return x[1]===EN?Jx(XV,Jx(x[2],BV)):GV}function $V(x){return x[1]===EN?Jx(JV,Jx(x[2],YV)):zV}function WV(x){return 0}function QV(x){return 0}function NN(x,r,e,t,u){var i=dq(j),c=[0,jq,KV,0];cN(c,i);var v=Q6(j);SN(v),Fv([0,1,c],v);var s=78,l=Q6(j),p=Q6(j),d=Q6(j);return[0,v,Q6(j),d,p,l,s,10,68,s,0,1,1,1,1,UV,HV,x,r,e,t,u,0,0,VV,$V,WV,QV,i]}function Kq(x,r){var e=NN(x,r,function(t){return 0},function(t){return 0},function(t){return 0});return e[19]=function(t){return CN(e,j)},e[20]=function(t){return dl(e,t)},e[21]=function(t){return dl(e,t)},e}function Hq(x){return Kq(function(r,e,t){return eq(x,r,e,t)},function(r){return bn(x)})}function ON(x){return Kq(function(r,e,t){return sN(x,r,e,t)},function(r){return 0})}var jN=aI;function Vq(x){return Wr(jN)}var $q=Vq(j),ZV=Hq(rq),x$=Hq(Sn),r$=ON($q),Wq=Oa(0,Vq);xp(Wq,$q),xp(Oa(0,function(x){return ON(Rv(Wq))}),r$);function Qq(x,r,e,t){return sN(Rv(x),r,e,t)}function Zq(x,r,e){var t=Rv(r),u=t[2];return eq(x,K1(t),0,u),bn(x),t[2]=0,0}var xU=Oa(0,function(x){return Wr(jN)}),rU=Oa(0,function(x){return Wr(jN)}),eU=Oa(0,function(x){var r=NN(function(e,t,u){return Qq(xU,e,t,u)},function(e){return Zq(rq,xU,j)},function(e){return 0},function(e){return 0},function(e){return 0});return r[19]=function(e){return CN(r,j)},r[20]=function(e){return dl(r,e)},r[21]=function(e){return dl(r,e)},_q(function(e){return Lv(r,j)}),r});xp(eU,ZV);var tU=Oa(0,function(x){var r=NN(function(e,t,u){return Qq(rU,e,t,u)},function(e){return Zq(Sn,rU,j)},function(e){return 0},function(e){return 0},function(e){return 0});return r[19]=function(e){return CN(r,j)},r[20]=function(e){return dl(r,e)},r[21]=function(e){return dl(r,e)},_q(function(e){return Lv(r,j)}),r});xp(tU,x$);var e$="Buffer.sub",t$=[0,0,4],n$=[0,[11,"invalid box description ",[3,0,0]],"invalid box description %S"],u$=tx,i$=tx,f$=tx,c$=tx;function nU(x,r){var e=Wr(16),t=ON(e);x(t,r),Lv(t,j);var u=e[2];if(2>u)return K1(e);var i=u-2|0,c=1;return 0<=i&&(e[2]-i|0)>=1?ol(e[1][1],c,i):X2(e$)}function gt(x,r){if(typeof r!="number"){x:{r:{e:{switch(r[0]){case 0:var e=r[2];if(gt(x,r[1]),typeof e=="number")switch(e){case 0:return Xq(x,j);case 1:return Gq(x,j);case 2:return Lv(x,j);case 3:var t=x[14]<x[15]?1:0;return t&&Lq(x,[0,jd,3,0]);case 4:return Yq(x,1),h(x[18],0);case 5:return up(x,64);default:return up(x,37)}switch(e[0]){case 0:var u=[0,c$,e[2],f$],i=x[14]<x[15]?1:0,c=[0,i$,e[3],u$],v=u[3],s=u[2],l=u[1];return i&&Uq(x,1,[0,-x[13]|0,[1,u,c],(Ux(l)+s|0)+Ux(v)|0]);case 1:return;default:var p=e[1];return up(x,64),up(x,p)}case 1:var d=r[2],T=r[1];if(d[0]===0){var b=d[1];gt(x,T);var C=[0,EN,nU(gt,b)];x[22]&&(Fv(C,x[4]),h(x[26],C));var N=x[23];return N&&np(x,[0,jd,[5,C],0])}var I=d[1];gt(x,T);var F=nU(gt,I);if(Tr(F,tx))var L=t$;else{var X=Ux(F),q=function(_x){var K=n$[1],_0=Wr(k6);return h(Gr(function(U){return ws(_0,U),Sx(K1(_0))},0,K),F)},J=function(_x){for(var K=_x;;){if(K===X)return K;var _0=B1(F,K);if(_0!==9&&_0!==32)return K;var K=K+1|0}},e0=J(0);t:n:{for(var W=e0;;){if(W===X)break n;if(25<B1(F,W)+gk>>>0)break;var W=W+1|0}break t}var x0=I2(F,e0,W-e0|0),i0=J(W);t:n:{for(var f0=i0;;){if(f0===X)break n;var r0=B1(F,f0);if(48<=r0){if(58<=r0)break}else if(r0!==45)break;var f0=f0+1|0}break t}if(i0===f0)var v0=0;else try{var o0=dt(I2(F,i0,f0-i0|0)),v0=o0}catch(_x){var w0=X1(_x);if(w0[1]!==En)throw z0(w0,0);var v0=q(j)}J(f0)!==X&&q(j);t:{if(P(x0,tx)&&P(x0,mI)){if(!P(x0,"h")){var t0=0;break t}if(!P(x0,"hov")){var t0=3;break t}if(!P(x0,"hv")){var t0=2;break t}if(P(x0,eR)){var t0=q(j);break t}var t0=1;break t}var t0=4}var L=[0,v0,t0]}return Bq(x,L[1],L[2]);case 2:var s0=r[1];if(typeof s0!="number"&&s0[0]===0){var h0=s0[2];if(typeof h0!="number"&&h0[0]===1){var p0=r[2],C0=h0[2],j0=s0[1];break r}}var S0=r[2],Z0=s0;break x;case 3:var P0=r[1];if(typeof P0!="number"&&P0[0]===0){var M0=P0[2];if(typeof M0!="number"&&M0[0]===1){var U0=r[2],T0=M0[2],G0=P0[1];break}}var ex=r[2],nx=P0;break e;case 4:var k0=r[1];if(typeof k0!="number"&&k0[0]===0){var G=k0[2];if(typeof G!="number"&&G[0]===1){var p0=r[2],C0=G[2],j0=k0[1];break r}}var S0=r[2],Z0=k0;break x;case 5:var N0=r[1];if(typeof N0!="number"&&N0[0]===0){var ux=N0[2];if(typeof ux!="number"&&ux[0]===1){var U0=r[2],T0=ux[2],G0=N0[1];break}}var ex=r[2],nx=N0;break e;case 6:var px=r[2];return gt(x,r[1]),h(px,x);case 7:return gt(x,r[1]),Lv(x,j);default:var D0=r[2];return gt(x,r[1]),X2(D0)}return gt(x,G0),IN(x,T0,Sd(1,U0))}return gt(x,nx),up(x,ex)}return gt(x,j0),IN(x,C0,p0)}return gt(x,Z0),Jq(x,Ux(S0),S0)}}function s2(x){return function(r){return Gr(function(e){return gt(x,e),0},0,r[1])}}var a$="Array.sub",s$="first domain already spawned",o$=[0,"camlinternalOO.ml",WF,50],v$=[0,uM,72,5],l$=[0,uM,81,2],p$="/tmp",k$=_n,m$=[0,"src/wtf8.ml",65,9],d$=[0,"src/third-party/sedlex/flow_sedlexing.ml",tA,4],h$="Flow_sedlexing.MalFormed",y$=l6,g$=O3,_$=N3,w$=I6,b$=Pv,T$=[0,[12,40,[18,[1,[0,[11,Ui,0],Ui]],[11,"File_key.LibFile",[17,[0,eo,1,0],0]]]],"(@[<2>File_key.LibFile@ "],E$=[0,[3,0,0],v6],A$=[0,[17,0,[12,41,0]],L4],S$=[0,[12,40,[18,[1,[0,[11,Ui,0],Ui]],[11,"File_key.SourceFile",[17,[0,eo,1,0],0]]]],"(@[<2>File_key.SourceFile@ "],P$=[0,[3,0,0],v6],I$=[0,[17,0,[12,41,0]],L4],C$=[0,[12,40,[18,[1,[0,[11,Ui,0],Ui]],[11,"File_key.JsonFile",[17,[0,eo,1,0],0]]]],"(@[<2>File_key.JsonFile@ "],N$=[0,[3,0,0],v6],O$=[0,[17,0,[12,41,0]],L4],j$=[0,[12,40,[18,[1,[0,[11,Ui,0],Ui]],[11,"File_key.ResourceFile",[17,[0,eo,1,0],0]]]],"(@[<2>File_key.ResourceFile@ "],D$=[0,[3,0,0],v6],F$=[0,[17,0,[12,41,0]],L4],R$=[0,1],M$=[0,0],L$=[0,1],q$=[0,2],U$=[0,2],B$=[0,0],X$=[0,1],G$=[0,1],Y$=[0,1],J$=[0,1],z$=[0,2],K$=[0,1],H$=[0,1],V$=[0,0,0],$$=[0,0,0],W$=[0,$2,vf,Zi,eu,Z2,qf,E7,vc,_c,Ff,Ji,qu,Li,wu,Y7,A7,ri,Bf,Q7,ci,af,H7,f7,Zf,Eu,du,u7,cc,Aa,ac,gf,Af,ba,qc,Nc,nu,xt,o7,Wi,d7,$c,Xi,uc,Zc,et,Yc,Ju,vu,b7,fa,ai,hu,O7,Qe,oi,I7,Of,h7,Pf,P7,ua,He,Lf,Ni,ei,J7,yu,ef,of,M7,Wf,ku,Dc,rf,T7,Hn,jf,Bu,$7,Ei,Vc,Ii,Cu,Ne,xa,c7,rc,Zu,_a,t7,s7,y7,Kf,Sc,n7,a7,Yi,Qf,mf,Gu,sf,$n,Hc,Vu,fi,Di,Wu,xi,vi,v7,Ci,Wc,Gi,da,Gf,Jc,zu,Xu,Si,Pc,Au,r7,ka,hf,ru,Vi,nc,df,Nf,su,bu,j7,va,Su,Ou,Fu,Xc,pu,aa,q7,pi,k2,C7,e7,Lc,li,kf,gu,iu,Tf,zf,pa,wc,Oc,W7,mc,Yu,Jf,k7,X7,uu,Xf,R7,ma,si,qi,Ri,jc,Zn,Hu,ti,cu,lf,le,oc,na,ic,Mu,Df,ca,Qi,Wn,W2,Mi,K7,ia,Ft,ji,Mc,ga,au,p7,ki,Uu,gi,xf,D7,lc,hc,ii,lu,wf,tc,Ta,ju,_f,i7,sa,Oi,yi,$i,ya,bf,g7,N7,Mf,hi,L7,Ic,ra,w7,u2,Vn,Fc,yf,Ea,F7,Qn,zi,Ec,Yf,Uc,Cc,wa,U7,zc,Uf,yc,bc,Pu,Lu,B7,Oe,uf,$u,Kn,kc,Ac,bi,Bc,Ti,pf,Tu,Ki,mu,xc,ta,Ze,Ke,tf,cf,ni,Kc,ea,Z7,_u,$f,Tc,ha,x7,gc,Bi,Hf,fu,ff,xu,dc,Ef,Fi,S7,fc,Vf,z7,ui,Pi,_i,Qu,Rf,Ku,Nu,If,m7,mi,_7,Hi,wi,Sa,rt,ou,Gc,gn,V7,G7,di,oa,l7,Iu,Du,Ai,Rc,nf,sc,ln,Sf],Q$=[0,fa,si,hc,ji,gi,Sa,vi,sf,W7,cu,Gc,Eu,s7,et,_a,Fc,ru,df,Rf,ba,Yi,uu,Ci,oc,R7,bc,U7,vf,Bu,au,Nc,yi,T7,kf,Ac,$f,ga,yc,da,Ft,eu,Mf,Wf,pf,Hi,ii,_7,k7,Ec,Lf,ki,$i,Zc,Hc,K7,Ou,vu,r7,Mu,Pu,li,Gi,Pi,Fi,xa,Sf,yu,Vi,Bi,H7,Gu,ei,pa,Vf,y7,He,a7,xf,c7,Gf,mc,X7,Mc,k2,cf,u7,Cc,j7,Nf,wc,C7,lc,pu,M7,nf,Bc,Qn,Xu,h7,Cu,sc,B7,wi,O7,Z2,Ni,mu,A7,I7,Mi,Au,di,Oi,nu,jc,Qu,Di,uc,Oe,g7,du,$2,e7,qc,Ki,ef,qu,Vc,xc,Xc,Pf,oa,Ji,Ef,Tu,xi,vc,bu,Xf,Du,ac,ti,Pc,zc,Ic,t7,yf,Lc,Ei,hi,Bf,Aa,Hf,qf,gf,ai,ri,Ju,Ff,z7,na,ya,S7,pi,D7,fu,_c,xu,Wu,iu,Tc,l7,Si,If,_i,x7,va,$7,Tf,gc,zu,W2,Qe,Y7,Q7,Yc,jf,xt,Ze,v7,ia,Zu,w7,ma,Ne,ou,Ta,Uf,ic,fc,Vn,lu,qi,Uu,wu,wa,ua,i7,Qi,nc,Uc,F7,ea,fi,hu,$n,Df,Sc,Dc,cc,mf,J7,n7,ci,bi,su,m7,Ti,V7,Zn,Xi,Lu,Af,p7,P7,Wn,Qf,Nu,Vu,Jf,ui,Fu,lf,uf,Zf,ln,E7,$u,mi,f7,Kn,Yu,Ri,ni,gu,rf,Wc,Hu,L7,Su,zf,af,d7,Iu,hf,Z7,Hn,Ai,G7,u2,Ii,ff,ra,ka,ju,_f,b7,Ke,wf,ha,of,zi,kc,dc,Wi,$c,sa,Ea,Li,bf,aa,rc,gn,ca,Ku,Oc,Kf,tc,ta,oi,Yf,rt,Rc,le,q7,tf,Zi,_u,Of,N7,Kc,ku,o7,Jc],Z$=xM,xW=cR,rW=BF,eW=Wj,tW=Ey,nW=sL,uW=d6,iW=vD,fW=rR,cW=JF,aW=Uj,sW=tu,oW=$e,vW=qD,lW=OF,pW=ve,kW=uL,mW=UD,dW=H4,hW=I8,yW=xo,gW=s6,_W=PR,wW=fD,bW=YD,TW=ZD,EW=YF,AW=eD,SW=iD,PW=AM,IW=GD,CW=TR,NW=qF,OW=Rr,jW=TF,DW=aL,FW=yF,RW=i6,MW=V3,LW=ao,qW=[0,[18,[1,[0,[11,Ui,0],Ui]],[11,"{ ",0]],"@[<2>{ "],UW="Loc.line",BW=[0,[18,[1,[0,0,tx]],[2,0,[11,iF,[17,[0,eo,1,0],0]]]],RF],XW=[0,[4,0,0,0,0],W3],GW=[0,[17,0,0],OS],YW=[0,[12,59,[17,[0,eo,1,0],0]],";@ "],JW=T6,zW=[0,[18,[1,[0,0,tx]],[2,0,[11,iF,[17,[0,eo,1,0],0]]]],RF],KW=[0,[4,0,0,0,0],W3],HW=[0,[17,0,0],OS],VW=[0,[17,[0,eo,1,0],[12,to,[17,0,0]]],"@ }@]"],$W=tx,WW="Object literal may not have data and accessor property with the same name",QW="Object literal may not have multiple get/set accessors with the same name",ZW="Unexpected token <. Remember, adjacent JSX elements must be wrapped in an enclosing parent tag",xQ="`let [` is ambiguous in this position because it is either a `let` binding pattern, or a member expression.",rQ="Async functions can only be declared at top level or immediately within another function.",eQ="`await` is an invalid identifier in async functions",tQ="`await` is not allowed in async function parameters.",nQ="Computed properties must have a value.",uQ="Constructor can't be an accessor.",iQ="Constructor can't be an async function.",fQ="Constructor can't be a generator.",cQ="It is sufficient for your declare function to just have a Promise return type.",aQ="async is an implementation detail and isn't necessary for your declare function statement. ",sQ="`declare` modifier can only appear on class fields.",oQ="Unexpected token `=`. Initializers are not allowed in a `declare`.",vQ="Unexpected token `=`. Initializers are not allowed in a `declare opaque type`.",lQ="Classes may only have one constructor",pQ="Rest element must be final element of an array pattern",kQ="Cannot export an enum with `export type`, try `export enum E {}` or `module.exports = E;` instead.",mQ="Enum members are separated with `,`. Replace `;` with `,`.",dQ="`const` enums are not supported. Flow Enums are designed to allow for inlining, however the inlining itself needs to be part of the build system (whatever you use) rather than Flow itself.",hQ="Expected an object pattern, array pattern, or an identifier but found an expression instead",yQ="Missing comma between export specifiers",gQ="Generators can only be declared at top level or immediately within another function.",_Q="Getter should have zero parameters",wQ="A getter cannot have a `this` parameter.",bQ="Illegal continue statement",TQ="Illegal return statement",EQ="Illegal Unicode escape",AQ="Missing comma between import specifiers",SQ="It cannot be used with `import type` or `import typeof` statements",PQ="The `type` and `typeof` keywords on named imports can only be used on regular `import` statements. ",IQ="Explicit inexact syntax cannot appear inside an explicit exact object type",CQ="Explicit inexact syntax can only appear inside an object type",NQ="Component params must be an identifier. If you'd like to destructure, you should use `name as {destructure}`",OQ="A bigint literal must be an integer",jQ="JSX value should be either an expression or a quoted JSX text",DQ="Invalid left-hand side in assignment",FQ="Invalid left-hand side in exponentiation expression",RQ="Invalid left-hand side in for-in",MQ="Invalid left-hand side in for-of",LQ="Invalid optional indexed access. Indexed access uses bracket notation. Use the format `T?.[K]`.",qQ="Invalid regular expression",UQ="A bigint literal cannot use exponential notation",BQ="Tuple spread elements cannot be optional.",XQ="Tuple variance annotations can only be used with labeled tuple elements, e.g. `[+foo: number]`",GQ="`typeof` can only be used to get the type of variables.",YQ="JSX attributes must only be assigned a non-empty expression",JQ="Literals cannot be used as shorthand properties.",zQ="Malformed unicode",KQ="`match` argument must not be empty",HQ="`match` argument cannot contain spread elements",VQ="Object pattern can't contain methods",$Q="Expected at least one type parameter.",WQ="Type parameter declaration needs a default, since a preceding type parameter declaration has a default.",QQ="More than one default clause in switch statement",ZQ="Illegal newline after throw",xZ="Illegal newline before arrow",rZ="Missing catch or finally after try",eZ="Const must be initialized",tZ="Destructuring assignment must be initialized",nZ="An optional chain may not be used in a `new` expression.",uZ="Template literals may not be used in an optional chain.",iZ="Rest parameter must be final parameter of an argument list",fZ="Private fields may not be deleted.",cZ="Private fields can only be referenced from within a class.",aZ="Rest property must be final property of an object pattern",sZ="Setter should have exactly one parameter",oZ="A setter cannot have a `this` parameter.",vZ="Catch variable may not be eval or arguments in strict mode",lZ="Delete of an unqualified identifier in strict mode.",pZ="Duplicate data property in object literal not allowed in strict mode",kZ="Function name may not be eval or arguments in strict mode",mZ="Assignment to eval or arguments is not allowed in strict mode",dZ="Postfix increment/decrement may not have eval or arguments operand in strict mode",hZ="Prefix increment/decrement may not have eval or arguments operand in strict mode",yZ="Strict mode code may not include a with statement",gZ="Number literals with leading zeros are not allowed in strict mode.",_Z="Octal literals are not allowed in strict mode.",wZ="Strict mode function may not have duplicate parameter names",bZ="Parameter name eval or arguments is not allowed in strict mode",TZ='Illegal "use strict" directive in function with non-simple parameter list',EZ="Use of reserved word in strict mode",AZ="Variable name may not be eval or arguments in strict mode",SZ="You may not access a private field through the `super` keyword.",PZ="Flow does not support abstract classes.",IZ="Flow does not support template literal types.",CZ="A type annotation is required for the `this` parameter.",NZ="Arrow functions cannot have a `this` parameter; arrow functions automatically bind `this` when declared.",OZ="Constructors cannot have a `this` parameter; constructors don't bind `this` like other functions.",jZ="The `this` parameter cannot be optional.",DZ="The `this` parameter must be the first function parameter.",FZ="A trailing comma is not permitted after the rest element",RZ="Unexpected end of input",MZ="Explicit inexact syntax must come at the end of an object type",LZ="Opaque type aliases are not allowed in untyped mode",qZ="Unexpected proto modifier",UZ="Unexpected reserved word",BZ="Unexpected reserved type",XZ="Spreading a type is only allowed inside an object type",GZ="Unexpected static modifier",YZ="Unexpected `super` outside of a class method",JZ="`super()` is only valid in a class constructor",zZ="Type aliases are not allowed in untyped mode",KZ="Type annotations are not allowed in untyped mode",HZ="Type declarations are not allowed in untyped mode",VZ="Type exports are not allowed in untyped mode",$Z="Type imports are not allowed in untyped mode",WZ="Interfaces are not allowed in untyped mode",QZ="Unexpected variance sigil",ZZ="Found a decorator in an unsupported position.",x00="Invalid regular expression: missing /",r00="Unexpected whitespace between `#` and identifier",e00="`yield` is an invalid identifier in generators",t00="Yield expression not allowed in formal parameter",n00=[0,[11,"Duplicate export for `",[2,0,[12,96,0]]],"Duplicate export for `%s`"],u00=[0,[11,"Private fields may only be declared once. `#",[2,0,[11,"` is declared more than once.",0]]],"Private fields may only be declared once. `#%s` is declared more than once."],i00=[0,[11,"bigint enum members need to be initialized, e.g. `",[2,0,[11," = 1n,` in enum `",[2,0,[11,us,0]]]]],"bigint enum members need to be initialized, e.g. `%s = 1n,` in enum `%s`."],f00=[0,[11,"Boolean enum members need to be initialized. Use either `",[2,0,[11," = true,` or `",[2,0,[11," = false,` in enum `",[2,0,[11,us,0]]]]]]],"Boolean enum members need to be initialized. Use either `%s = true,` or `%s = false,` in enum `%s`."],c00=[0,[11,"Enum member names need to be unique, but the name `",[2,0,[11,"` has already been used before in enum `",[2,0,[11,us,0]]]]],"Enum member names need to be unique, but the name `%s` has already been used before in enum `%s`."],a00=[0,[11,oF,[2,0,[11,"` has inconsistent member initializers. Either use no initializers, or consistently use literals (either booleans, numbers, or strings) for all member initializers.",0]]],"Enum `%s` has inconsistent member initializers. Either use no initializers, or consistently use literals (either booleans, numbers, or strings) for all member initializers."],s00="The `...` must come at the end of the enum body. Remove the trailing comma.",o00="The `...` must come after all enum members. Move it to the end of the enum body.",v00=[0,[11,"Use one of `boolean`, `number`, `string`, `symbol`, or `bigint` in enum `",[2,0,[11,us,0]]],"Use one of `boolean`, `number`, `string`, `symbol`, or `bigint` in enum `%s`."],l00=[0,[11,"Enum type `",[2,0,[11,"` is not valid. ",[2,0,0]]]],"Enum type `%s` is not valid. %s"],p00=[0,[11,"Supplied enum type is not valid. ",[2,0,0]],"Supplied enum type is not valid. %s"],k00=[0,[11,"Enum member names and initializers are separated with `=`. Replace `",[2,0,[11,":` with `",[2,0,[11," =`.",0]]]]],"Enum member names and initializers are separated with `=`. Replace `%s:` with `%s =`."],m00=[0,[11,oF,[2,0,[11,"` has type `",[2,0,[11,"`, so the initializer of `",[2,0,[11,"` needs to be a ",[2,0,[11," literal.",0]]]]]]]]],"Enum `%s` has type `%s`, so the initializer of `%s` needs to be a %s literal."],d00=[0,[11,"Symbol enum members cannot be initialized. Use `",[2,0,[11,",` in enum `",[2,0,[11,us,0]]]]],"Symbol enum members cannot be initialized. Use `%s,` in enum `%s`."],h00=[0,[11,"The enum member initializer for `",[2,0,[11,"` needs to be a literal (either a boolean, number, or string) in enum `",[2,0,[11,us,0]]]]],"The enum member initializer for `%s` needs to be a literal (either a boolean, number, or string) in enum `%s`."],y00=[0,[11,"Enum member names cannot start with lowercase 'a' through 'z'. Instead of using `",[2,0,[11,"`, consider using `",[2,0,[11,"`, in enum `",[2,0,[11,us,0]]]]]]],"Enum member names cannot start with lowercase 'a' through 'z'. Instead of using `%s`, consider using `%s`, in enum `%s`."],g00=[0,[11,"Number enum members need to be initialized, e.g. `",[2,0,[11," = 1,` in enum `",[2,0,[11,us,0]]]]],"Number enum members need to be initialized, e.g. `%s = 1,` in enum `%s`."],_00=[0,[11,"String enum members need to consistently either all use initializers, or use no initializers, in enum ",[2,0,[12,46,0]]],"String enum members need to consistently either all use initializers, or use no initializers, in enum %s."],w00=[0,[11,"Expected corresponding JSX closing tag for ",[2,0,0]],"Expected corresponding JSX closing tag for %s"],b00="immediately within another function.",T00="In strict mode code, functions can only be declared at top level or ",E00="inside a block, or as the body of an if statement.",A00="In non-strict mode code, functions can only be declared at top level, ",S00=" `break` statements are not required in `match` statements, as unlike `switch` statements, `match` statement cases do not fall-through by default.",P00=tx,I00=[0,[11,"Illegal break statement.",[2,0,0]],"Illegal break statement.%s"],C00="static ",N00=tx,O00="methods",j00="fields",D00=KR,F00=[0,[11,"Classes may not have ",[2,0,[2,0,[11," named `",[2,0,[11,us,0]]]]]],"Classes may not have %s%s named `%s`."],R00="Components use `renders` instead of `:` to annotate the render type of a component.",M00=dR,L00=tx,q00=[0,[11,"String params require local bindings using `as` renaming. You can use `'",[2,0,[11,"' as ",[2,0,[2,0,[11,": <TYPE>` ",0]]]]]],"String params require local bindings using `as` renaming. You can use `'%s' as %s%s: <TYPE>` "],U00="Remove the period.",B00="Indexed access uses bracket notation.",X00=[0,[11,"Invalid indexed access. ",[2,0,[11," Use the format `T[K]`.",0]]],"Invalid indexed access. %s Use the format `T[K]`."],G00=[0,[11,"Invalid flags supplied to RegExp constructor '",[2,0,[12,39,0]]],"Invalid flags supplied to RegExp constructor '%s'"],Y00=ln,J00=a4,z00=[0,[11,"In match ",[2,0,[11," pattern, the rest must be the last element in the pattern",0]]],"In match %s pattern, the rest must be the last element in the pattern"],K00=[0,[11,"JSX element ",[2,0,[11," has no corresponding closing tag.",0]]],"JSX element %s has no corresponding closing tag."],H00=[0,[11,pR,[2,0,[11,"`. Parentheses are required to combine `??` with `&&` or `||` expressions.",0]]],"Unexpected token `%s`. Parentheses are required to combine `??` with `&&` or `||` expressions."],V00=[0,[2,0,[11," '",[2,0,[11,"' has already been declared",0]]]],"%s '%s' has already been declared"],$00=tx,W00=Zl,Q00=" You can try using JavaScript private fields by prepending `#` to the field name.",Z00=j6,xx0=" Fields and methods are public by default. You can simply omit the `public` keyword.",rx0=C6,ex0=[0,[11,"Flow does not support using `",[2,0,[11,"` in classes.",[2,0,0]]]],"Flow does not support using `%s` in classes.%s"],tx0=[0,[11,"Private fields must be declared before they can be referenced. `#",[2,0,[11,"` has not been declared.",0]]],"Private fields must be declared before they can be referenced. `#%s` has not been declared."],nx0=[0,[11,oR,[2,0,0]],"Unexpected %s"],ux0=[0,[11,pR,[2,0,[11,"`. Did you mean `",[2,0,[11,"`?",0]]]]],"Unexpected token `%s`. Did you mean `%s`?"],ix0=[0,[11,oR,[2,0,[11,", expected ",[2,0,0]]]],"Unexpected %s, expected %s"],fx0=[0,[11,"Undefined label '",[2,0,[12,39,0]]],"Undefined label '%s'"],cx0="Parse_error.Error",ax0=[0,[0,36,37],[0,48,58],[0,65,91],[0,95,96],[0,97,pn],[0,U5,yk],[0,rA,Fy],[0,O4,E8],[0,AS,M4],[0,Zo,jg],[0,I5,h4],[0,f1,706],[0,Vj,722],[0,736,741],[0,748,749],[0,750,751],[0,768,885],[0,886,888],[0,890,894],[0,895,896],[0,902,907],[0,908,909],[0,910,930],[0,zR,1014],[0,1015,1154],[0,1155,1160],[0,1162,1328],[0,1329,1367],[0,1369,1370],[0,1376,1417],[0,1425,1470],[0,1471,1472],[0,1473,1475],[0,1476,1478],[0,1479,1480],[0,1488,1515],[0,1519,1523],[0,1552,1563],[0,1568,1642],[0,1646,1748],[0,1749,1757],[0,1759,1769],[0,1770,1789],[0,1791,1792],[0,1808,1867],[0,1869,1970],[0,1984,2038],[0,2042,2043],[0,2045,2046],[0,pw,2094],[0,2112,2140],[0,2144,2155],[0,2208,2229],[0,2230,2238],[0,2259,2274],[0,2275,2404],[0,2406,2416],[0,2417,2436],[0,2437,2445],[0,2447,2449],[0,2451,2473],[0,2474,2481],[0,2482,2483],[0,2486,2490],[0,2492,2501],[0,2503,2505],[0,2507,2511],[0,2519,2520],[0,2524,2526],[0,2527,2532],[0,2534,2546],[0,2556,2557],[0,2558,2559],[0,2561,2564],[0,2565,2571],[0,2575,2577],[0,2579,2601],[0,2602,2609],[0,2610,2612],[0,2613,2615],[0,2616,2618],[0,2620,2621],[0,2622,2627],[0,2631,2633],[0,2635,2638],[0,2641,2642],[0,2649,2653],[0,2654,2655],[0,2662,2678],[0,2689,2692],[0,2693,2702],[0,2703,2706],[0,2707,2729],[0,2730,2737],[0,2738,2740],[0,2741,2746],[0,2748,2758],[0,2759,2762],[0,2763,2766],[0,2768,2769],[0,2784,2788],[0,2790,2800],[0,2809,2816],[0,2817,2820],[0,2821,2829],[0,2831,2833],[0,2835,2857],[0,2858,2865],[0,2866,2868],[0,2869,2874],[0,2876,2885],[0,2887,2889],[0,2891,2894],[0,2902,2904],[0,2908,2910],[0,2911,2916],[0,2918,2928],[0,2929,2930],[0,2946,2948],[0,2949,2955],[0,2958,2961],[0,2962,2966],[0,2969,2971],[0,2972,2973],[0,2974,2976],[0,2979,2981],[0,2984,2987],[0,2990,3002],[0,3006,3011],[0,3014,3017],[0,3018,3022],[0,3024,3025],[0,3031,3032],[0,3046,3056],[0,3072,3085],[0,3086,3089],[0,3090,3113],[0,3114,3130],[0,3133,3141],[0,3142,3145],[0,3146,3150],[0,3157,3159],[0,3160,3163],[0,3168,3172],[0,3174,3184],[0,3200,3204],[0,3205,3213],[0,3214,3217],[0,3218,3241],[0,3242,3252],[0,3253,3258],[0,3260,3269],[0,3270,3273],[0,3274,3278],[0,3285,3287],[0,3294,3295],[0,3296,3300],[0,3302,3312],[0,3313,3315],[0,3328,3332],[0,3333,3341],[0,3342,3345],[0,3346,3397],[0,3398,3401],[0,3402,3407],[0,3412,3416],[0,3423,3428],[0,3430,3440],[0,3450,3456],[0,3458,3460],[0,3461,3479],[0,3482,3506],[0,3507,3516],[0,3517,3518],[0,3520,3527],[0,3530,3531],[0,3535,3541],[0,3542,3543],[0,3544,3552],[0,3558,3568],[0,3570,3572],[0,3585,3643],[0,3648,3663],[0,3664,3674],[0,3713,3715],[0,3716,3717],[0,3718,3723],[0,3724,3748],[0,3749,3750],[0,3751,3774],[0,3776,3781],[0,3782,3783],[0,3784,3790],[0,3792,3802],[0,3804,3808],[0,3840,3841],[0,3864,3866],[0,3872,3882],[0,3893,3894],[0,3895,3896],[0,3897,3898],[0,3902,3912],[0,3913,3949],[0,3953,3973],[0,3974,3992],[0,3993,4029],[0,4038,4039],[0,zF,4170],[0,4176,4254],[0,4256,4294],[0,4295,4296],[0,4301,4302],[0,4304,4347],[0,4348,4681],[0,4682,4686],[0,4688,4695],[0,4696,4697],[0,4698,4702],[0,4704,4745],[0,4746,4750],[0,4752,4785],[0,4786,4790],[0,4792,4799],[0,4800,4801],[0,4802,4806],[0,4808,4823],[0,4824,4881],[0,4882,4886],[0,4888,4955],[0,4957,4960],[0,4969,4978],[0,4992,5008],[0,5024,5110],[0,5112,5118],[0,5121,5741],[0,5743,SI],[0,5761,5787],[0,5792,5867],[0,5870,5881],[0,5888,5901],[0,5902,5909],[0,5920,5941],[0,5952,5972],[0,5984,5997],[0,5998,6001],[0,6002,6004],[0,6016,6100],[0,6103,6104],[0,6108,6110],[0,6112,6122],[0,6155,6158],[0,6160,6170],[0,6176,6265],[0,6272,6315],[0,6320,6390],[0,6400,6431],[0,6432,6444],[0,6448,6460],[0,6470,6510],[0,6512,6517],[0,6528,6572],[0,6576,6602],[0,6608,6619],[0,6656,6684],[0,6688,6751],[0,6752,6781],[0,6783,6794],[0,6800,6810],[0,6823,6824],[0,6832,6846],[0,6912,6988],[0,6992,7002],[0,7019,7028],[0,7040,7156],[0,7168,7224],[0,7232,7242],[0,7245,7294],[0,7296,7305],[0,7312,7355],[0,7357,7360],[0,7376,7379],[0,7380,7419],[0,7424,7674],[0,7675,7958],[0,7960,7966],[0,7968,8006],[0,8008,8014],[0,8016,8024],[0,8025,8026],[0,8027,8028],[0,8029,8030],[0,8031,8062],[0,8064,8117],[0,8118,8125],[0,8126,8127],[0,8130,8133],[0,8134,8141],[0,8144,8148],[0,8150,8156],[0,8160,8173],[0,8178,8181],[0,8182,8189],[0,_D,YR],[0,8255,8257],[0,8276,8277],[0,lm,8306],[0,Mk,8320],[0,8336,8349],[0,8400,8413],[0,8417,8418],[0,8421,8433],[0,Hm,8451],[0,y8,8456],[0,8458,j4],[0,z4,8470],[0,_R,8478],[0,Wk,w8],[0,D8,$4],[0,ek,T8],[0,8490,8506],[0,8508,8512],[0,8517,8522],[0,vk,8527],[0,8544,8585],[0,11264,11311],[0,11312,11359],[0,11360,11493],[0,11499,11508],[0,11520,Bm],[0,Qa,11560],[0,v8,11566],[0,11568,11624],[0,Fk,11632],[0,U4,11671],[0,11680,Gm],[0,11688,Jm],[0,11696,Qp],[0,11704,kk],[0,11712,wm],[0,11720,t4],[0,11728,U8],[0,11736,11743],[0,11744,11776],[0,12293,12296],[0,12321,R8],[0,12337,12342],[0,12344,12349],[0,12353,12439],[0,12441,gm],[0,12449,$8],[0,12540,12544],[0,12549,g8],[0,12593,12687],[0,12704,12731],[0,12784,12800],[0,13312,19894],[0,19968,40944],[0,40960,42125],[0,42192,42238],[0,42240,42509],[0,42512,42540],[0,42560,42608],[0,42612,N4],[0,42623,42738],[0,42775,42784],[0,42786,42889],[0,42891,42944],[0,42946,42951],[0,Yk,43048],[0,43072,43124],[0,43136,43206],[0,43216,43226],[0,43232,43256],[0,Ak,hk],[0,43261,43310],[0,43312,43348],[0,43360,43389],[0,43392,43457],[0,Am,43482],[0,43488,k4],[0,SF,43575],[0,43584,43598],[0,43600,43610],[0,43616,43639],[0,Lm,43715],[0,43739,43742],[0,43744,43760],[0,43762,43767],[0,43777,43783],[0,43785,43791],[0,43793,43799],[0,43808,Wm],[0,43816,Jk],[0,43824,Im],[0,43868,i4],[0,43888,44011],[0,44012,44014],[0,44016,44026],[0,44032,55204],[0,55216,55239],[0,55243,55292],[0,63744,64110],[0,64112,64218],[0,64256,64263],[0,64275,64280],[0,Tk,p4],[0,64298,xm],[0,64312,Vm],[0,Sk,G4],[0,64320,M8],[0,64323,xd],[0,64326,64434],[0,64467,64830],[0,64848,64912],[0,64914,64968],[0,65008,65020],[0,65024,65040],[0,65056,65072],[0,65075,65077],[0,65101,65104],[0,65136,P8],[0,65142,65277],[0,65296,65306],[0,65313,65339],[0,65343,Ym],[0,65345,65371],[0,65382,65471],[0,65474,65480],[0,65482,65488],[0,65490,65496],[0,65498,65501],[0,E6,Y8],[0,65549,Cm],[0,65576,Z4],[0,65596,rk],[0,65599,65614],[0,65616,65630],[0,65664,65787],[0,65856,65909],[0,66045,66046],[0,66176,66205],[0,66208,66257],[0,66272,66273],[0,66304,66336],[0,66349,66379],[0,66384,66427],[0,66432,66462],[0,66464,66500],[0,66504,B8],[0,66513,66518],[0,66560,66718],[0,66720,66730],[0,66736,66772],[0,66776,66812],[0,66816,66856],[0,66864,66916],[0,67072,67383],[0,67392,67414],[0,67424,67432],[0,67584,67590],[0,X4,Ck],[0,67594,X8],[0,67639,67641],[0,Pm,67645],[0,67647,67670],[0,67680,67703],[0,67712,67743],[0,67808,b4],[0,67828,67830],[0,67840,67862],[0,67872,67898],[0,67968,68024],[0,68030,68032],[0,J8,68100],[0,68101,68103],[0,68108,mk],[0,68117,Sm],[0,68121,68150],[0,68152,68155],[0,68159,68160],[0,68192,68221],[0,68224,68253],[0,68288,om],[0,68297,68327],[0,68352,68406],[0,68416,68438],[0,68448,68467],[0,68480,68498],[0,68608,68681],[0,68736,68787],[0,68800,68851],[0,68864,68904],[0,68912,68922],[0,69376,69405],[0,k8,69416],[0,69424,69457],[0,69600,69623],[0,69632,69703],[0,69734,F8],[0,69759,69819],[0,69840,69865],[0,69872,69882],[0,69888,69941],[0,69942,69952],[0,m4,ck],[0,69968,70004],[0,b8,70007],[0,70016,70085],[0,70089,70093],[0,70096,c8],[0,Xk,70109],[0,70144,f8],[0,70163,70200],[0,70206,70207],[0,70272,u8],[0,tm,sk],[0,70282,i8],[0,70287,zm],[0,70303,70313],[0,70320,70379],[0,70384,70394],[0,70400,c4],[0,70405,70413],[0,70415,70417],[0,70419,S8],[0,70442,_m],[0,70450,qm],[0,70453,70458],[0,70459,70469],[0,70471,70473],[0,70475,70478],[0,y4,70481],[0,70487,70488],[0,70493,70500],[0,70502,70509],[0,70512,70517],[0,70656,70731],[0,70736,70746],[0,F4,70752],[0,70784,d8],[0,Rk,70856],[0,70864,70874],[0,71040,71094],[0,71096,71105],[0,71128,71134],[0,71168,71233],[0,Bk,71237],[0,71248,71258],[0,71296,71353],[0,71360,71370],[0,71424,71451],[0,71453,71468],[0,71472,71482],[0,71680,71739],[0,71840,71914],[0,71935,71936],[0,72096,72104],[0,72106,72152],[0,72154,vm],[0,h8,72165],[0,jk,72255],[0,72263,72264],[0,lk,72346],[0,zk,72350],[0,72384,72441],[0,72704,_8],[0,72714,72759],[0,72760,72769],[0,72784,72794],[0,72818,72848],[0,72850,72872],[0,72873,72887],[0,72960,Vk],[0,72968,q8],[0,72971,73015],[0,73018,73019],[0,73020,73022],[0,73023,73032],[0,73040,73050],[0,73056,em],[0,73063,Zm],[0,73066,73103],[0,73104,73106],[0,73107,73113],[0,73120,73130],[0,73440,73463],[0,73728,74650],[0,74752,74863],[0,74880,75076],[0,77824,78895],[0,82944,83527],[0,92160,92729],[0,92736,92767],[0,92768,92778],[0,92880,92910],[0,92912,92917],[0,92928,92983],[0,92992,92996],[0,93008,93018],[0,93027,93048],[0,93053,93072],[0,93760,93824],[0,93952,94027],[0,Qm,94088],[0,94095,94112],[0,94176,Y4],[0,f4,94180],[0,94208,100344],[0,100352,101107],[0,110592,110879],[0,110928,110931],[0,110948,110952],[0,110960,111356],[0,113664,113771],[0,113776,113789],[0,113792,113801],[0,113808,113818],[0,113821,113823],[0,119141,119146],[0,119149,119155],[0,119163,119171],[0,119173,119180],[0,119210,119214],[0,119362,119365],[0,119808,o4],[0,119894,fk],[0,119966,119968],[0,Nk,119971],[0,119973,119975],[0,119977,td],[0,119982,e8],[0,pm,Nm],[0,119997,Lk],[0,120005,Z8],[0,120071,120075],[0,120077,q4],[0,120086,n8],[0,120094,d4],[0,120123,Em],[0,120128,Dk],[0,_k,120135],[0,120138,j8],[0,120146,120486],[0,120488,K4],[0,120514,rm],[0,120540,W8],[0,120572,ak],[0,120598,fm],[0,120630,P4],[0,120656,Qk],[0,120688,g4],[0,120714,u4],[0,120746,uk],[0,120772,120780],[0,120782,120832],[0,121344,121399],[0,121403,121453],[0,121461,121462],[0,121476,121477],[0,121499,121504],[0,121505,121520],[0,122880,122887],[0,122888,122905],[0,122907,122914],[0,122915,122917],[0,122918,122923],[0,123136,123181],[0,123184,123198],[0,123200,123210],[0,r4,123215],[0,123584,123642],[0,124928,125125],[0,125136,125143],[0,125184,125260],[0,125264,125274],[0,126464,I4],[0,126469,Km],[0,126497,V8],[0,n4,126501],[0,l8,bm],[0,126505,x4],[0,126516,cm],[0,z8,Dm],[0,E4,126524],[0,L8,126531],[0,rd,S4],[0,K8,Xm],[0,G8,v4],[0,126541,ok],[0,126545,N8],[0,s8,126549],[0,sm,A4],[0,Pk,$k],[0,Um,O8],[0,B4,_4],[0,mm,xk],[0,126561,T4],[0,C8,126565],[0,126567,s4],[0,126572,D4],[0,126580,nm],[0,126585,Ek],[0,am,m8],[0,126592,w4],[0,126603,126620],[0,126625,im],[0,126629,Kk],[0,126635,126652],[0,131072,173783],[0,173824,177973],[0,177984,178206],[0,178208,183970],[0,183984,191457],[0,194560,195102],[0,917760,918e3]],sx0=[0,1,0],ox0=[0,0,[0,1,0],[0,1,0]],vx0=hM,lx0="end of input",px0=w6,kx0="template literal part",mx0=w6,dx0=$o,hx0=hM,yx0=w6,gx0=O3,_x0=w6,wx0=Pv,bx0=w6,Tx0=N3,Ex0="an",Ax0=Ft,Sx0=Ru,Px0=[0,[11,"token `",[2,0,[12,96,0]]],"token `%s`"],Ix0="{",Cx0=jm,Nx0="{|",Ox0="|}",jx0=XR,Dx0=hR,Fx0="[",Rx0="]",Mx0=rT,Lx0=tL,qx0=_n,Ux0="=>",Bx0="...",Xx0=Bj,Gx0=KR,Yx0=R3,Jx0=Fm,zx0=xo,Kx0=s6,Hx0=Qe,Vx0=et,$x0=gI,Wx0=pv,Qx0=xt,Zx0=Rm,xr0=i6,rr0=Zp,er0=km,tr0=ao,nr0=V3,ur0=wv,ir0=xs,fr0=as,cr0=rt,ar0=C4,sr0=o8,or0=Ke,vr0=sv,lr0=Q4,pr0=ym,kr0=Tm,mr0=r6,dr0=pc,hr0=ze,yr0=nk,gr0=hv,_r0=c6,wr0=fs,br0=rs,Tr0=p6,Er0=H8,Ar0=W2,Sr0=H3,Pr0=Av,Ir0=le,Cr0=tk,Nr0=j6,Or0=Zl,jr0=C6,Dr0=$2,Fr0=Ze,Rr0=F6,Mr0=ec,Lr0=kb,qr0=mA,Ur0=io,Br0=_v,Xr0="%checks",Gr0=GD,Yr0=AM,Jr0=iD,zr0=qF,Kr0=TR,Hr0=Rr,Vr0=eD,$r0=YF,Wr0=YD,Qr0=ZD,Zr0=fD,x10=PR,r10=TF,e10=aL,t10=yF,n10=U9,u10="?.",i10=i_,f10=dR,c10=rv,a10=VF,s10=GR,o10=UD,v10=H4,l10=I8,p10=xM,k10=cR,m10=BF,d10=Wj,h10=sL,y10=vD,g10=Ey,_10=d6,w10=rR,b10=JF,T10=Uj,E10=tu,A10=$e,S10=ve,P10=qD,I10=OF,C10=uL,N10=Jj,O10=cL,j10=aM,D10=DD,F10=tx,R10=R4,M10=V4,L10=Oe,q10=O3,U10=Pv,B10=N3,X10=rs,G10=I6,Y10=J4,J10=W4,z10=bk,K10=p8,H10=mv,V10=Zj,$10=S6,W10=X3,Q10=F3,Z10=HF,x20=wF,r20=x6,e20=x6,t20=jM,n20=x6,u20=x6,i20=jm,f20=jm,c20=jM,a20=ve,s20=ve,o20=l6,v20=Om,l20="T_LCURLY",p20="T_RCURLY",k20="T_LCURLYBAR",m20="T_RCURLYBAR",d20="T_LPAREN",h20="T_RPAREN",y20="T_LBRACKET",g20="T_RBRACKET",_20="T_SEMICOLON",w20="T_COMMA",b20="T_PERIOD",T20="T_ARROW",E20="T_ELLIPSIS",A20="T_AT",S20="T_POUND",P20="T_FUNCTION",I20="T_IF",C20="T_IN",N20="T_INSTANCEOF",O20="T_RETURN",j20="T_SWITCH",D20="T_MATCH",F20="T_THIS",R20="T_THROW",M20="T_TRY",L20="T_VAR",q20="T_WHILE",U20="T_WITH",B20="T_CONST",X20="T_LET",G20="T_NULL",Y20="T_FALSE",J20="T_TRUE",z20="T_BREAK",K20="T_CASE",H20="T_CATCH",V20="T_CONTINUE",$20="T_DEFAULT",W20="T_DO",Q20="T_FINALLY",Z20="T_FOR",xe0="T_CLASS",re0="T_EXTENDS",ee0="T_STATIC",te0="T_ELSE",ne0="T_NEW",ue0="T_DELETE",ie0="T_TYPEOF",fe0="T_VOID",ce0="T_ENUM",ae0="T_EXPORT",se0="T_IMPORT",oe0="T_SUPER",ve0="T_IMPLEMENTS",le0="T_INTERFACE",pe0="T_PACKAGE",ke0="T_PRIVATE",me0="T_PROTECTED",de0="T_PUBLIC",he0="T_YIELD",ye0="T_DEBUGGER",ge0="T_DECLARE",_e0="T_TYPE",we0="T_OPAQUE",be0="T_OF",Te0="T_ASYNC",Ee0="T_AWAIT",Ae0="T_CHECKS",Se0="T_RSHIFT3_ASSIGN",Pe0="T_RSHIFT_ASSIGN",Ie0="T_LSHIFT_ASSIGN",Ce0="T_BIT_XOR_ASSIGN",Ne0="T_BIT_OR_ASSIGN",Oe0="T_BIT_AND_ASSIGN",je0="T_MOD_ASSIGN",De0="T_DIV_ASSIGN",Fe0="T_MULT_ASSIGN",Re0="T_EXP_ASSIGN",Me0="T_MINUS_ASSIGN",Le0="T_PLUS_ASSIGN",qe0="T_NULLISH_ASSIGN",Ue0="T_AND_ASSIGN",Be0="T_OR_ASSIGN",Xe0="T_ASSIGN",Ge0="T_PLING_PERIOD",Ye0="T_PLING_PLING",Je0="T_PLING",ze0="T_COLON",Ke0="T_OR",He0="T_AND",Ve0="T_BIT_OR",$e0="T_BIT_XOR",We0="T_BIT_AND",Qe0="T_EQUAL",Ze0="T_NOT_EQUAL",xt0="T_STRICT_EQUAL",rt0="T_STRICT_NOT_EQUAL",et0="T_LESS_THAN_EQUAL",tt0="T_GREATER_THAN_EQUAL",nt0="T_LESS_THAN",ut0="T_GREATER_THAN",it0="T_LSHIFT",ft0="T_RSHIFT",ct0="T_RSHIFT3",at0="T_PLUS",st0="T_MINUS",ot0="T_DIV",vt0="T_MULT",lt0="T_EXP",pt0="T_MOD",kt0="T_NOT",mt0="T_BIT_NOT",dt0="T_INCR",ht0="T_DECR",yt0="T_EOF",gt0="T_ANY_TYPE",_t0="T_MIXED_TYPE",wt0="T_EMPTY_TYPE",bt0="T_NUMBER_TYPE",Tt0="T_BIGINT_TYPE",Et0="T_STRING_TYPE",At0="T_VOID_TYPE",St0="T_SYMBOL_TYPE",Pt0="T_UNKNOWN_TYPE",It0="T_NEVER_TYPE",Ct0="T_UNDEFINED_TYPE",Nt0="T_KEYOF",Ot0="T_READONLY",jt0="T_INFER",Dt0="T_IS",Ft0="T_ASSERTS",Rt0="T_IMPLIES",Mt0=nL,Lt0=nL,qt0="T_NUMBER",Ut0="T_BIGINT",Bt0="T_STRING",Xt0="T_TEMPLATE_PART",Gt0="T_IDENTIFIER",Yt0="T_REGEXP",Jt0="T_INTERPRETER",zt0="T_ERROR",Kt0="T_JSX_IDENTIFIER",Ht0=xL,Vt0=xL,$t0="T_BOOLEAN_TYPE",Wt0="T_NUMBER_SINGLETON_TYPE",Qt0="T_BIGINT_SINGLETON_TYPE",Zt0=[0,tF,_A,9],xn0=[0,tF,Ik,9],rn0=OM,en0="*/",tn0=OM,nn0="unreachable line_comment",un0="unreachable string_quote",in0="\\",fn0="unreachable template_part",cn0=`\r
`,an0=N_,sn0="unreachable regexp_class",on0=rD,vn0="unreachable regexp_body",ln0=tx,pn0=tx,kn0=tx,mn0=tx,dn0=FD,hn0="{'>'}",yn0=d6,gn0="{'}'}",_n0=jm,wn0=no,bn0=rT,Tn0=I8,En0=FD,An0=no,Sn0=rT,Pn0=I8,In0="unreachable type_token wholenumber",Cn0="unreachable type_token wholebigint",Nn0="unreachable type_token floatbigint",On0="unreachable type_token scinumber",jn0="unreachable type_token scibigint",Dn0="unreachable type_token hexnumber",Fn0="unreachable type_token hexbigint",Rn0="unreachable type_token legacyoctnumber",Mn0="unreachable type_token octnumber",Ln0="unreachable type_token octbigint",qn0="unreachable type_token binnumber",Un0="unreachable type_token bigbigint",Bn0="unreachable type_token",Xn0=CM,Gn0=[11,1],Yn0=[11,0],Jn0="unreachable template_tail",zn0=tx,Kn0=tx,Hn0="unreachable jsx_child",Vn0="unreachable jsx_tag",$n0=[0,MD],Wn0=[0,913],Qn0=[0,Zo],Zn0=[0,qI],x70=[0,AD],r70=[0,_I],e70=[0,8747],t70=[0,iy],n70=[0,916],u70=[0,8225],i70=[0,935],f70=[0,DM],c70=[0,914],a70=[0,wM],s70=[0,GF],o70=[0,AR],v70=[0,915],l70=[0,Yj],p70=[0,919],k70=[0,917],m70=[0,NM],d70=[0,aD],h70=[0,sF],y70=[0,924],g70=[0,923],_70=[0,922],w70=[0,PF],b70=[0,921],T70=[0,lR],E70=[0,Ik],A70=[0,mF],S70=[0,I5],P70=[0,927],I70=[0,937],C70=[0,oD],N70=[0,lF],O70=[0,bD],j70=[0,338],D70=[0,352],F70=[0,929],R70=[0,936],M70=[0,8243],L70=[0,928],q70=[0,934],U70=[0,WM],B70=[0,sD],X70=[0,933],G70=[0,SR],Y70=[0,vM],J70=[0,Sj],z70=[0,920],K70=[0,932],H70=[0,Hy],V70=[0,RD],$70=[0,nR],W70=[0,aF],Q70=[0,918],Z70=[0,376],xu0=[0,uR],ru0=[0,926],eu0=[0,NF],tu0=[0,zR],nu0=[0,925],uu0=[0,39],iu0=[0,8736],fu0=[0,8743],cu0=[0,38],au0=[0,945],su0=[0,8501],ou0=[0,Qo],vu0=[0,8226],lu0=[0,cD],pu0=[0,946],ku0=[0,8222],mu0=[0,Qj],du0=[0,FR],hu0=[0,8776],yu0=[0,PM],gu0=[0,8773],_u0=[0,9827],wu0=[0,Vj],bu0=[0,967],Tu0=[0,WR],Eu0=[0,E8],Au0=[0,Hj],Su0=[0,xR],Pu0=[0,8595],Iu0=[0,8224],Cu0=[0,8659],Nu0=[0,SD],Ou0=[0,8746],ju0=[0,8629],Du0=[0,LD],Fu0=[0,8745],Ru0=[0,8195],Mu0=[0,8709],Lu0=[0,Aj],qu0=[0,IM],Uu0=[0,yM],Bu0=[0,h4],Xu0=[0,9830],Gu0=[0,8707],Yu0=[0,8364],Ju0=[0,qR],zu0=[0,U3],Ku0=[0,951],Hu0=[0,8801],Vu0=[0,949],$u0=[0,8194],Wu0=[0,8805],Qu0=[0,947],Zu0=[0,8260],xi0=[0,tE],ri0=[0,wD],ei0=[0,_A],ti0=[0,8704],ni0=[0,ZF],ui0=[0,MM],ii0=[0,8230],fi0=[0,9829],ci0=[0,8596],ai0=[0,8660],si0=[0,62],oi0=[0,402],vi0=[0,948],li0=[0,_F],pi0=[0,Qy],ki0=[0,8712],mi0=[0,A8],di0=[0,953],hi0=[0,8734],yi0=[0,8465],gi0=[0,BR],_i0=[0,8220],wi0=[0,8968],bi0=[0,8592],Ti0=[0,yk],Ei0=[0,10216],Ai0=[0,955],Si0=[0,8656],Pi0=[0,954],Ii0=[0,60],Ci0=[0,8216],Ni0=[0,8249],Oi0=[0,YR],ji0=[0,9674],Di0=[0,8727],Fi0=[0,8970],Ri0=[0,GP],Mi0=[0,8711],Li0=[0,956],qi0=[0,8722],Ui0=[0,O4],Bi0=[0,rA],Xi0=[0,8212],Gi0=[0,VD],Yi0=[0,8804],Ji0=[0,957],zi0=[0,jF],Ki0=[0,8836],Hi0=[0,8713],Vi0=[0,dw],$i0=[0,8715],Wi0=[0,8800],Qi0=[0,8853],Zi0=[0,959],xf0=[0,969],rf0=[0,8254],ef0=[0,nM],tf0=[0,339],nf0=[0,fv],uf0=[0,QR],if0=[0,Fy],ff0=[0,z3],cf0=[0,8855],af0=[0,iE],sf0=[0,f1],of0=[0,AS],vf0=[0,U5],lf0=[0,x1],pf0=[0,kS],kf0=[0,982],mf0=[0,960],df0=[0,966],hf0=[0,8869],yf0=[0,8240],gf0=[0,8706],_f0=[0,8744],wf0=[0,8211],bf0=[0,10217],Tf0=[0,8730],Ef0=[0,8658],Af0=[0,34],Sf0=[0,968],Pf0=[0,8733],If0=[0,8719],Cf0=[0,961],Nf0=[0,8971],Of0=[0,$M],jf0=[0,8476],Df0=[0,8221],Ff0=[0,8969],Rf0=[0,8594],Mf0=[0,M4],Lf0=[0,MR],qf0=[0,IF],Uf0=[0,8901],Bf0=[0,353],Xf0=[0,8218],Gf0=[0,8217],Yf0=[0,8250],Jf0=[0,8835],zf0=[0,8721],Kf0=[0,8838],Hf0=[0,8834],Vf0=[0,9824],$f0=[0,8764],Wf0=[0,962],Qf0=[0,963],Zf0=[0,8207],xc0=[0,952],rc0=[0,8756],ec0=[0,964],tc0=[0,Ok],nc0=[0,8839],uc0=[0,iL],ic0=[0,PD],fc0=[0,Z3],cc0=[0,8657],ac0=[0,8482],sc0=[0,jg],oc0=[0,732],vc0=[0,M3],lc0=[0,8201],pc0=[0,977],kc0=[0,_R],mc0=[0,q3],dc0=[0,965],hc0=[0,978],yc0=[0,qM],gc0=[0,tA],_c0=[0,fL],wc0=[0,_D],bc0=[0,8205],Tc0=[0,950],Ec0=[0,pk],Ac0=[0,FF],Sc0=[0,_E],Pc0=[0,958],Ic0=[0,8593],Cc0=[0,Nj],Nc0=[0,8242],Oc0=[0,gM],jc0="unreachable regexp",Dc0="unreachable token wholenumber",Fc0="unreachable token wholebigint",Rc0="unreachable token floatbigint",Mc0="unreachable token scinumber",Lc0="unreachable token scibigint",qc0="unreachable token hexnumber",Uc0="unreachable token hexbigint",Bc0="unreachable token legacyoctnumber",Xc0="unreachable token legacynonoctnumber",Gc0="unreachable token octnumber",Yc0="unreachable token octbigint",Jc0="unreachable token bignumber",zc0="unreachable token bigint",Kc0="unreachable token",Hc0=CM,Vc0=[7,"#!"],$c0="expected ?",Wc0="unreachable string_escape",Qc0=Q2,Zc0=o6,xa0=o6,ra0=Q2,ea0=mI,ta0=UF,na0="n",ua0="r",ia0="t",fa0=eR,ca0=o6,aa0=no,sa0=no,oa0="unreachable id_char",va0=no,la0=no,pa0=o6,ka0=lM,ma0=Dj,da0=Kw,ha0=[27,"token ILLEGAL"],ya0=[0,[11,"the identifier `",[2,0,[12,96,0]]],"the identifier `%s`"],ga0=[0,1],_a0=[0,1],wa0=KF,ba0=KF,Ta0=[0,[11,"an identifier. When exporting a ",[2,0,[11," as a named export, you must specify a ",[2,0,[11," name. Did you mean `export default ",[2,0,[11," ...`?",0]]]]]]],"an identifier. When exporting a %s as a named export, you must specify a %s name. Did you mean `export default %s ...`?"],Ea0=ed,Aa0="Peeking current location when not available",Sa0=[0,"src/parser/parser_env.ml",369,9],Pa0="Internal Error: Tried to add_declared_private with outside of class scope.",Ia0="Internal Error: `exit_class` called before a matching `enter_class`",Ca0=tx,Na0=[0,0,0],Oa0=[0,0,0],ja0="Parser_env.Try.Rollback",Da0=tx,Fa0=tx,Ra0=[0,$2,vf,Zi,zD,LR,eu,Z2,qf,E7,vc,_c,Ff,Ji,qu,Li,wu,Y7,A7,ri,Bf,Q7,ci,af,H7,f7,Zf,Eu,du,u7,cc,Aa,ac,gf,Af,ba,qc,Nc,nu,xt,o7,Wi,d7,$c,Xi,uc,Zc,et,Yc,Ju,vu,b7,fa,ai,hu,O7,Qe,oi,I7,Of,h7,Pf,P7,ua,He,Lf,Ni,ei,J7,yu,ef,of,M7,Wf,ku,Dc,rf,T7,Hn,jf,Bu,$7,Ei,Vc,Ii,Cu,Ne,xa,c7,rc,Zu,_a,t7,s7,y7,Kf,Sc,n7,a7,Yi,Qf,mf,Gu,sf,$n,Hc,Vu,fi,Di,Wu,xi,vi,v7,Ci,Wc,Gi,da,Gf,Jc,zu,Xu,Si,Pc,Au,r7,ka,hf,ru,Vi,nc,df,Nf,su,bu,j7,va,Su,Ou,Fu,Xc,pu,aa,q7,pi,k2,C7,e7,Lc,li,kf,gu,iu,Tf,zf,pa,wc,Oc,W7,mc,Yu,Jf,k7,X7,uu,Xf,R7,ma,si,qi,Ri,jc,Zn,Hu,ti,cu,lf,le,oc,na,ic,Mu,Df,ca,Qi,Wn,W2,Mi,K7,ia,Ft,ji,Mc,ga,au,p7,ki,Uu,gi,xf,D7,lc,hc,ii,lu,wf,tc,Ta,ju,_f,i7,sa,Oi,yi,$i,ya,bf,g7,N7,Mf,hi,L7,Ic,ra,w7,u2,Vn,Fc,yf,Ea,F7,Qn,zi,Ec,Yf,Uc,Cc,wa,U7,zc,Uf,yc,bc,Pu,Lu,B7,Oe,uf,$u,Kn,kc,Ac,bi,Bc,Ti,pf,Tu,Ki,mu,xc,ta,Ze,Ke,tf,cf,ni,Kc,ea,Z7,_u,$f,Tc,ha,x7,gc,Bi,Hf,fu,ff,ID,xu,jj,$F,dc,Ef,Fi,S7,fc,Vf,z7,ui,Pi,_i,Qu,Rf,Ku,Nu,If,m7,mi,_7,Hi,wi,Sa,rt,ou,Gc,gn,V7,G7,di,oa,l7,Iu,Du,Ai,Rc,nf,sc,ln,Sf],Ma0=[0,$2,vf,Zi,eu,Z2,qf,E7,vc,_c,Ff,Ji,qu,Li,wu,Y7,A7,ri,Bf,Q7,ci,af,H7,f7,Zf,Eu,du,u7,cc,Aa,ac,gf,Af,ba,qc,Nc,nu,xt,o7,Wi,d7,$c,Xi,uc,Zc,et,Yc,Ju,vu,b7,fa,ai,hu,O7,Qe,oi,I7,Of,h7,Pf,P7,ua,He,Lf,Ni,ei,J7,yu,ef,of,M7,Wf,ku,Dc,rf,T7,Hn,jf,Bu,$7,Ei,Vc,Ii,Cu,Ne,xa,c7,rc,Zu,_a,t7,s7,y7,Kf,Sc,n7,a7,Yi,Qf,mf,Gu,sf,$n,Hc,Vu,fi,Di,Wu,xi,vi,v7,Ci,Wc,Gi,da,Gf,Jc,zu,Xu,Si,Pc,Au,r7,ka,hf,ru,Vi,nc,df,Nf,su,bu,j7,va,Su,Ou,Fu,Xc,pu,aa,q7,pi,k2,C7,e7,Lc,li,kf,gu,iu,Tf,zf,pa,wc,Oc,W7,mc,Yu,Jf,k7,X7,uu,Xf,R7,ma,si,qi,Ri,jc,Zn,Hu,ti,cu,lf,le,oc,na,ic,Mu,Df,ca,Qi,Wn,W2,Mi,K7,ia,Ft,ji,Mc,ga,au,p7,ki,Uu,gi,xf,D7,lc,hc,ii,lu,wf,tc,Ta,ju,_f,i7,sa,Oi,yi,$i,ya,bf,g7,N7,Mf,hi,L7,Ic,ra,w7,u2,Vn,Fc,yf,Ea,F7,Qn,zi,Ec,Yf,Uc,Cc,wa,U7,zc,Uf,yc,bc,Pu,Lu,B7,Oe,uf,$u,Kn,kc,Ac,bi,Bc,Ti,pf,Tu,Ki,mu,xc,ta,Ze,Ke,tf,cf,ni,Kc,ea,Z7,_u,$f,Tc,ha,x7,gc,Bi,Hf,fu,ff,xu,dc,Ef,Fi,S7,fc,Vf,z7,ui,Pi,_i,Qu,Rf,Ku,Nu,If,m7,mi,_7,Hi,wi,Sa,rt,ou,Gc,gn,V7,G7,di,oa,l7,Iu,Du,Ai,Rc,nf,sc,ln,Sf],La0=[0,fa,si,hc,ji,gi,Sa,vi,sf,W7,cu,Gc,Eu,s7,et,_a,Fc,ru,df,Rf,ba,Yi,uu,Ci,oc,R7,bc,U7,vf,Bu,au,Nc,yi,T7,kf,Ac,$f,ga,yc,da,Ft,eu,Mf,Wf,pf,Hi,ii,_7,k7,Ec,Lf,ki,$i,Zc,Hc,K7,Ou,vu,r7,Mu,Pu,li,Gi,Pi,Fi,xa,Sf,yu,Vi,Bi,H7,Gu,ei,pa,Vf,y7,He,a7,xf,c7,Gf,mc,X7,Mc,k2,cf,u7,Cc,j7,Nf,wc,C7,lc,pu,M7,nf,Bc,Qn,Xu,h7,Cu,sc,B7,wi,O7,Z2,Ni,mu,A7,I7,Mi,Au,di,Oi,nu,jc,Qu,Di,uc,Oe,g7,du,$2,e7,qc,Ki,ef,qu,Vc,xc,Xc,Pf,oa,Ji,Ef,Tu,xi,vc,bu,Xf,Du,ac,ti,Pc,zc,Ic,t7,yf,Lc,Ei,hi,Bf,Aa,Hf,qf,gf,ai,ri,Ju,Ff,z7,na,ya,S7,pi,D7,fu,_c,xu,Wu,iu,Tc,l7,Si,If,_i,x7,va,$7,Tf,gc,zu,W2,Qe,Y7,Q7,Yc,jf,xt,Ze,v7,ia,Zu,w7,ma,Ne,ou,Ta,Uf,ic,fc,Vn,lu,qi,Uu,wu,wa,ua,i7,Qi,nc,Uc,F7,ea,fi,hu,$n,Df,Sc,Dc,cc,mf,J7,n7,ci,bi,su,m7,Ti,V7,Zn,Xi,Lu,Af,p7,P7,Wn,Qf,Nu,Vu,Jf,ui,Fu,lf,uf,Zf,ln,E7,$u,mi,f7,Kn,Yu,Ri,ni,gu,rf,Wc,Hu,L7,Su,zf,af,d7,Iu,hf,Z7,Hn,Ai,G7,u2,Ii,ff,ra,ka,ju,_f,b7,Ke,wf,ha,of,zi,kc,dc,Wi,$c,sa,Ea,Li,bf,aa,rc,gn,ca,Ku,Oc,Kf,tc,ta,oi,Yf,rt,Rc,le,q7,tf,Zi,_u,Of,N7,Kc,ku,o7,Jc],qa0=[0,fa,si,hc,ji,gi,Sa,vi,sf,W7,cu,Gc,Eu,s7,et,_a,Fc,ru,df,Rf,ba,Yi,uu,Ci,oc,R7,bc,U7,vf,Bu,au,Nc,yi,T7,kf,Ac,$f,ga,yc,da,Ft,eu,LR,Mf,Wf,pf,Hi,ii,_7,k7,Ec,Lf,ki,$i,Zc,Hc,K7,Ou,vu,r7,Mu,Pu,li,Gi,Pi,Fi,xa,Sf,yu,Vi,Bi,jj,H7,Gu,ei,pa,Vf,y7,He,a7,xf,c7,Gf,mc,X7,Mc,k2,cf,u7,Cc,j7,Nf,wc,C7,lc,pu,M7,nf,Bc,Qn,Xu,h7,Cu,sc,B7,wi,O7,Z2,Ni,mu,A7,I7,Mi,Au,di,Oi,nu,jc,Qu,Di,uc,Oe,g7,du,$2,e7,qc,Ki,ef,qu,Vc,xc,Xc,Pf,oa,Ji,Ef,Tu,xi,vc,bu,Xf,Du,ac,ti,Pc,zc,Ic,t7,yf,Lc,Ei,hi,Bf,Aa,Hf,qf,gf,ai,ri,Ju,Ff,z7,na,ya,S7,pi,D7,fu,_c,xu,Wu,iu,Tc,l7,Si,If,_i,x7,va,$7,Tf,gc,zu,W2,Qe,Y7,Q7,Yc,jf,xt,Ze,v7,ia,Zu,w7,ma,Ne,ou,Ta,Uf,ic,fc,Vn,lu,qi,Uu,wu,wa,ua,i7,Qi,nc,Uc,F7,ea,fi,hu,$n,Df,Sc,Dc,cc,mf,J7,n7,ci,bi,su,m7,Ti,V7,Zn,$F,Xi,Lu,Af,p7,P7,Wn,Qf,Nu,Vu,Jf,ui,Fu,lf,uf,Zf,ID,ln,E7,$u,mi,f7,zD,Kn,Yu,Ri,ni,gu,rf,Wc,Hu,L7,Su,zf,af,d7,Iu,hf,Z7,Hn,Ai,G7,u2,Ii,ff,ra,ka,ju,_f,b7,Ke,wf,ha,of,zi,kc,dc,Wi,$c,sa,Ea,Li,bf,aa,rc,gn,ca,Ku,Oc,Kf,tc,ta,oi,Yf,rt,Rc,le,q7,tf,Zi,_u,Of,N7,Kc,ku,o7,Jc],Ua0=R3,Ba0=Fm,Xa0=xo,Ga0=s6,Ya0=Qe,Ja0=et,za0=gI,Ka0=pv,Ha0=xt,Va0=Rm,$a0=i6,Wa0=Zp,Qa0=km,Za0=ao,xs0=V3,rs0=wv,es0=xs,ts0=as,ns0=rt,us0=C4,is0=o8,fs0=Ke,cs0=sv,as0=Q4,ss0=ym,os0=Tm,vs0=r6,ls0=pc,ps0=ze,ks0=nk,ms0=hv,ds0=c6,hs0=fs,ys0=rs,gs0=p6,_s0=H8,ws0=W2,bs0=H3,Ts0=Av,Es0=le,As0=tk,Ss0=j6,Ps0=Zl,Is0=C6,Cs0=$2,Ns0=Ze,Os0=F6,js0=ec,Ds0=kb,Fs0=mA,Rs0=io,Ms0=_v,Ls0=R4,qs0=V4,Us0=Oe,Bs0=O3,Xs0=Pv,Gs0=N3,Ys0=rs,Js0=I6,zs0=J4,Ks0=W4,Hs0=bk,Vs0=p8,$s0=mv,Ws0=S6,Qs0=X3,Zs0=F3,xo0=l6,ro0=Om,eo0=[0,ed],to0=tx,no0=[19,1],uo0=[19,0],io0=[0,0],fo0=ts,co0=[0,0],ao0=[0,"a type"],so0=[0,0],oo0=[0,"a number literal type"],vo0=[0,0],lo0=S6,po0=X3,ko0=F3,mo0="You should only call render_type after making sure the next token is a renders variant",do0=[0,[0,0,0,0,0]],ho0=[0,0,0,0],yo0=[0,1],go0=[0,K3,1466,6],_o0=[0,K3,1469,6],wo0=[0,K3,1572,8],bo0=[0,1],To0=[0,K3,1589,8],Eo0="Can not have both `static` and `proto`",Ao0=ze,So0=Sg,Po0=[0,0],Io0=[0,"the end of a tuple type (no trailing comma is allowed in inexact tuple type)."],Co0=[0,K3,Qo,15],No0=[0,K3,A8,15],Oo0=$e,jo0=$e,Do0=wk,Fo0=T6,Ro0=[0,[11,"Failure while looking up ",[2,0,[11,". Index: ",[4,0,0,0,[11,". Length: ",[4,0,0,0,[12,46,0]]]]]]],"Failure while looking up %s. Index: %d. Length: %d."],Mo0=[0,0,0,0],Lo0="Offset_utils.Offset_lookup_failed",qo0=A1,Uo0=Fj,Bo0=T6,Xo0=wk,Go0=Xj,Yo0=T6,Jo0=wk,zo0=ND,Ko0=W0,Ho0="normal",Vo0=ec,$o0="jsxTag",Wo0="jsxChild",Qo0="template",Zo0=$o,xv0="context",rv0=ec,ev0=[6,0],tv0=[0,0],nv0=[0,1],uv0=[0,4],iv0=[0,2],fv0=[0,3],cv0=[0,0],av0=$e,sv0=[0,0,0,0,0,0],ov0=[0,0],vv0=[0,JR],lv0=[0,1],pv0=[0,0],kv0=ts,mv0=[0,70],dv0=[0,81],hv0=wR,yv0=ET,gv0="exports",_v0=P6,wv0=[0,tx,tx,0],bv0=[0,zj],Tv0=[0,81],Ev0=[0,"a declaration, statement or export specifiers"],Av0=[0,1],Sv0=[0,r9,1893,21],Pv0=[0,"the keyword `as`"],Iv0=[0,29],Cv0=[0,29],Nv0=[0,0],Ov0=[0,1],jv0=[0,zj],Dv0=[0,"the keyword `from`"],Fv0=[0,tx,tx,0],Rv0="Label",Mv0=[0,JR],Lv0=[0,0,0],qv0=[0,38],Uv0=[0,r9,372,22],Bv0=[0,37],Xv0=[0,r9,391,22],Gv0=[0,0],Yv0="the token `;`",Jv0=[0,0],zv0=[0,0],Kv0=xF,Hv0=[0,ed],Vv0=xF,$v0=[27,Ft],Wv0=ts,Qv0=[0,70],Zv0=[0,tx,0],x30=Mt,r30=[0,tx,0],e30=[0,70],t30=[0,70],n30=R3,u30=[0,tx,0],i30=[0,0,0],f30=[0,0,0],c30=[0,[0,8]],a30=[0,[0,7]],s30=[0,[0,6]],o30=[0,[0,10]],v30=[0,[0,9]],l30=[0,[0,11]],p30=[0,[0,5]],k30=[0,[0,4]],m30=[0,[0,2]],d30=[0,[0,3]],h30=[0,[0,1]],y30=[0,[0,0]],g30=[0,[0,12]],_30=[0,[0,13]],w30=[0,[0,14]],b30=[0,0],T30=[0,1],E30=[0,0],A30=[0,2],S30=[0,3],P30=[0,7],I30=[0,6],C30=[0,4],N30=[0,5],O30=[0,1],j30=[0,0],D30=[0,1],F30=[0,0],R30=H3,M30=[0,"either a call or access of `super`"],L30=H3,q30=W2,U30=u6,B30=u6,X30=[0,2],G30=[0,0],Y30=[0,1],J30=hv,z30=[0,"the identifier `target`"],K30=[0,0],H30=[0,1],V30=[0,0],$30=[0,0],W30=[0,2],Q30=[0,2],Z30=[0,1],xl0=[0,70],rl0=o6,el0=lM,tl0=Kw,nl0=Kw,ul0=Dj,il0=[0,0],fl0=[0,1],cl0=[0,0],al0=ve,sl0=ve,ol0=[0,"a regular expression"],vl0=tx,ll0=tx,pl0=tx,kl0=[0,78],ml0=[0,"src/parser/expression_parser.ml",1546,17],dl0=[0,"a template literal part"],hl0=[0,[0,tx,tx],1],yl0=ev,gl0=[0,6],_l0=[0,[0,17,[0,2]]],wl0=[0,[0,18,[0,3]]],bl0=[0,[0,19,[0,4]]],Tl0=[0,[0,0,[0,5]]],El0=[0,[0,1,[0,5]]],Al0=[0,[0,2,[0,5]]],Sl0=[0,[0,3,[0,5]]],Pl0=[0,[0,5,[0,6]]],Il0=[0,[0,7,[0,6]]],Cl0=[0,[0,4,[0,6]]],Nl0=[0,[0,6,[0,6]]],Ol0=[0,[0,8,[0,7]]],jl0=[0,[0,9,[0,7]]],Dl0=[0,[0,10,[0,7]]],Fl0=[0,[0,11,[0,8]]],Rl0=[0,[0,12,[0,8]]],Ml0=[0,[0,15,[0,9]]],Ll0=[0,[0,13,[0,9]]],ql0=[0,[0,14,[1,10]]],Ul0=[0,[0,16,[0,9]]],Bl0=[0,[0,21,[0,6]]],Xl0=[0,[0,20,[0,6]]],Gl0=[23,i_],Yl0=[13,"JSX fragment"],Jl0=rv,zl0=_n,Kl0=[0,dn],Hl0=[1,dn],Vl0=[0,tx,tx,0],$l0=[0,ed],Wl0=tx,Ql0=[0,"a numeric or string literal"],Zl0=[0,tx,'""',0],x60=[0,0],r60=[0,"a number literal"],e60=[0,[0,0,Q2,0]],t60=[0,81],n60=[21,NR],u60=[21,m6],i60=r6,f60=[0,tx,0],c60="unexpected PrivateName in Property, expected a PrivateField",a60=[0,0,0],s60=uo,o60="Must be one of the above",v60=[0,1],l60=[0,1],p60=[0,1],k60=uo,m60=uo,d60=U9,h60="Internal Error: private name found in object props",y60=[0,0,0,0],g60=[0,EF],_60=[19,[0,0]],w60=[0,EF],b60=N_,T60="Nooo: ",E60=sv,A60="Parser error: No such thing as an expression pattern!",S60=[0,[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]],P60=[0,"src/parser/parser_flow.ml",kS,28],I60=[0,[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]],C60=Fj,N60=W0,O60=kD,j60=gD,D60=gD,F60=kD,R60=ec,M60=Ij,L60=C1,q60=A1,U60="InterpreterDirective",B60="interpreter",X60="Program",G60=a6,Y60="BreakStatement",J60=a6,z60="ContinueStatement",K60="DebuggerStatement",H60=bv,V60="DeclareExportAllDeclaration",$60=bv,W60=q9,Q60=QT,Z60=sv,xp0="DeclareExportDeclaration",rp0=C1,ep0=$r,tp0="DeclareModule",np0=i2,up0="DeclareModuleExports",ip0=C1,fp0=$r,cp0=GM,ap0="DeclareNamespace",sp0=L3,op0=C1,vp0="DoWhileStatement",lp0="EmptyStatement",pp0=NE,kp0=QT,mp0="ExportDefaultDeclaration",dp0=NE,hp0=OA,yp0=bv,gp0="ExportAllDeclaration",_p0=NE,wp0=bv,bp0=q9,Tp0=QT,Ep0="ExportNamedDeclaration",Ap0="directive",Sp0=u2,Pp0="ExpressionStatement",Ip0=C1,Cp0="update",Np0=L3,Op0=la,jp0="ForStatement",Dp0="each",Fp0=C1,Rp0=kn,Mp0=ns,Lp0="ForInStatement",qp0=_v,Up0=C1,Bp0=kn,Xp0=ns,Gp0="ForOfStatement",Yp0=OD,Jp0=TP,zp0=L3,Kp0="IfStatement",Hp0=ec,Vp0=fs,$p0=A1,Wp0=uD,Qp0=bv,Zp0=q9,x40="ImportDeclaration",r40=C1,e40=a6,t40="LabeledStatement",n40=y9,u40=W1,i40="MatchStatement",f40=W1,c40="ReturnStatement",a40=y9,s40="discriminant",o40="SwitchStatement",v40=W1,l40="ThrowStatement",p40="finalizer",k40="handler",m40=gn,d40="TryStatement",h40=C1,y40=L3,g40="WhileStatement",_40=C1,w40=a4,b40="WithStatement",T40=Sy,E40=W1,A40=rS,S40=Sy,P40=W1,I40=rS,C40=mT,N40="ArrayExpression",O40=xe,j40=Q8,D40=u2,F40=He,R40=W5,M40=io,L40=C1,q40=yn,U40=$r,B40="ArrowFunctionExpression",X40=u2,G40="AsConstExpression",Y40=i2,J40=u2,z40="AsExpression",K40=U9,H40=kn,V40=ns,$40=dv,W40="AssignmentExpression",Q40=kn,Z40=ns,xk0=dv,rk0="BinaryExpression",ek0="CallExpression",tk0=OD,nk0=TP,uk0=L3,ik0="ConditionalExpression",fk0=bv,ck0="ImportExpression",ak0=VF,sk0=GR,ok0=i_,vk0=kn,lk0=ns,pk0=dv,kk0="LogicalExpression",mk0=y9,dk0=W1,hk0="MatchExpression",yk0="MemberExpression",gk0=x8,_k0=u6,wk0="MetaProperty",bk0=wb,Tk0=Hk,Ek0=ED,Ak0="NewExpression",Sk0=Uk,Pk0="ObjectExpression",Ik0=pt,Ck0="OptionalCallExpression",Nk0=pt,Ok0="OptionalMemberExpression",jk0=vR,Dk0="SequenceExpression",Fk0="Super",Rk0="ThisExpression",Mk0=i2,Lk0=u2,qk0="TypeCastExpression",Uk0=i2,Bk0=u2,Xk0="SatisfiesExpression",Gk0=Sy,Yk0=W1,Jk0=rS,zk0=$e,Kk0=tu,Hk0=Jj,Vk0=cL,$k0=fs,Wk0=rs,Qk0=c6,Zk0="matched above",xm0=W1,rm0=uF,em0=dv,tm0="UnaryExpression",nm0=W1,um0="AwaitExpression",im0=DD,fm0=aM,cm0=uF,am0=W1,sm0=dv,om0="UpdateExpression",vm0="delegate",lm0=W1,pm0="YieldExpression",km0="MatchExpressionCase",mm0="guard",dm0=C1,hm0=Ne,ym0="MatchStatementCase",gm0=f6,_m0=Ne,wm0=is,bm0=vF,Tm0=f6,Em0=Ne,Am0=is,Sm0=vF,Pm0=x8,Im0="base",Cm0="MatchMemberPattern",Nm0="literal",Om0="MatchLiteralPattern",jm0="MatchWildcardPattern",Dm0=$e,Fm0=tu,Rm0=W1,Mm0=dv,Lm0="MatchUnaryPattern",qm0=Ql,Um0=Uk,Bm0="MatchObjectPattern",Xm0=Ql,Gm0=mT,Ym0="MatchArrayPattern",Jm0="patterns",zm0="MatchOrPattern",Km0=nd,Hm0=Ne,Vm0="MatchAsPattern",$m0=$r,Wm0="MatchIdentifierPattern",Qm0=es,Zm0=$r,x80="MatchBindingPattern",r80=W1,e80="MatchRestPattern",t80="Unexpected FunctionDeclaration with BodyExpression",n80="HookDeclaration",u80=u2,i80=He,f80=W5,c80=io,a80="FunctionDeclaration",s80=xe,o80=Q8,v80=C1,l80=yn,p80=$r,k80="Unexpected FunctionExpression with BodyExpression",m80=xe,d80=Q8,h80=u2,y80=He,g80=W5,_80=io,w80=C1,b80=yn,T80=$r,E80="FunctionExpression",A80=pt,S80=i2,P80=Ve,I80=oA,C80=pt,N80=i2,O80=Ve,j80="PrivateIdentifier",D80=pt,F80=i2,R80=Ve,M80=oA,L80=TP,q80=L3,U80="SwitchCase",B80=C1,X80="param",G80="CatchClause",Y80=C1,J80="BlockStatement",z80=es,K80=$r,H80="DeclareVariable",V80="DeclareHook",$80=He,W80="DeclareFunction",Q80=$r,Z80=ER,xd0=Av,rd0=pc,ed0=C1,td0=xe,nd0=$r,ud0="DeclareClass",id0=xe,fd0=D9,cd0=yn,ad0=Ql,sd0=yn,od0=$r,vd0="DeclareComponent",ld0=xe,pd0=D9,kd0=Ql,md0=yn,dd0="ComponentTypeAnnotation",hd0=pt,yd0=i2,gd0=Ve,_d0="ComponentTypeParameter",wd0=C1,bd0=$r,Td0="DeclareEnum",Ed0=pc,Ad0=C1,Sd0=xe,Pd0=$r,Id0="DeclareInterface",Cd0=A1,Nd0=ec,Od0=OA,jd0="ExportNamespaceSpecifier",Dd0=kn,Fd0=xe,Rd0=$r,Md0="DeclareTypeAlias",Ld0=kn,qd0=xe,Ud0=$r,Bd0="TypeAlias",Xd0="DeclareOpaqueType",Gd0="OpaqueType",Yd0="supertype",Jd0="impltype",zd0=xe,Kd0=$r,Hd0="ClassDeclaration",Vd0="ClassExpression",$d0=Zk,Wd0=Av,Qd0="superTypeParameters",Zd0="superClass",xh0=xe,rh0=C1,eh0=$r,th0=u2,nh0="Decorator",uh0=xe,ih0=$r,fh0="ClassImplements",ch0=C1,ah0="ClassBody",sh0=C1,oh0="StaticBlock",vh0=Wo,lh0=N6,ph0=nv,kh0=G3,mh0=Zk,dh0=j3,hh0=ze,yh0=es,gh0=A1,_h0=is,wh0="MethodDefinition",bh0=F6,Th0=Zk,Eh0=Z2,Ah0=ze,Sh0=j3,Ph0=i2,Ih0=A1,Ch0=is,Nh0=JM,Oh0="Internal Error: Private name found in class prop",jh0=F6,Dh0=Zk,Fh0=Z2,Rh0=ze,Mh0=j3,Lh0=i2,qh0=A1,Uh0=is,Bh0=JM,Xh0=xe,Gh0=D9,Yh0=yn,Jh0=$r,zh0=C1,Kh0="ComponentDeclaration",Hh0=W1,Vh0=eE,$h0=kn,Wh0=ns,Qh0=a8,Zh0=f6,x50=b6,r50=Ve,e50="ComponentParameter",t50=la,n50=$r,u50="EnumBigIntMember",i50=$r,f50=eF,c50=la,a50=$r,s50="EnumStringMember",o50=$r,v50=eF,l50=la,p50=$r,k50="EnumNumberMember",m50=la,d50=$r,h50="EnumBooleanMember",y50=_6,g50=t8,_50=t6,w50="EnumBooleanBody",b50=_6,T50=t8,E50=t6,A50="EnumNumberBody",S50=_6,P50=t8,I50=t6,C50="EnumStringBody",N50=_6,O50=t6,j50="EnumSymbolBody",D50=_6,F50=t8,R50=t6,M50="EnumBigIntBody",L50=C1,q50=$r,U50="EnumDeclaration",B50=pc,X50=C1,G50=xe,Y50=$r,J50="InterfaceDeclaration",z50=xe,K50=$r,H50="InterfaceExtends",V50=i2,$50=Uk,W50="ObjectPattern",Q50=i2,Z50=mT,xy0="ArrayPattern",ry0=kn,ey0=ns,ty0=a8,ny0=i2,uy0=Ve,iy0=oA,fy0=W1,cy0=eE,ay0=W1,sy0=eE,oy0=kn,vy0=ns,ly0=a8,py0=la,ky0=la,my0=nv,dy0=G3,hy0=pD,yy0=j3,gy0=f6,_y0=N6,wy0=es,by0=A1,Ty0=is,Ey0=DF,Ay0=W1,Sy0=TD,Py0=kn,Iy0=ns,Cy0=a8,Ny0=j3,Oy0=f6,jy0=N6,Dy0=es,Fy0=A1,Ry0=is,My0=DF,Ly0=W1,qy0=TD,Uy0=Rt,By0=A1,Xy0=C3,Gy0=tx,Yy0=Rt,Jy0=Pv,zy0=A1,Ky0=C3,Hy0=Rt,Vy0=A1,$y0=C3,Wy0=as,Qy0=xs,Zy0=Rt,x90=A1,r90=C3,e90="flags",t90=Ne,n90="regex",u90=Rt,i90=A1,f90=C3,c90=Rt,a90=A1,s90=C3,o90=vR,v90="quasis",l90="TemplateLiteral",p90="cooked",k90=Rt,m90="tail",d90=A1,h90="TemplateElement",y90="quasi",g90="tag",_90="TaggedTemplateExpression",w90=es,b90="declarations",T90="VariableDeclaration",E90=la,A90=$r,S90="VariableDeclarator",P90="plus",I90=kR,C90=mv,N90=xo,O90=C_,j90="in-out",D90=es,F90="Variance",R90="AnyTypeAnnotation",M90="MixedTypeAnnotation",L90="EmptyTypeAnnotation",q90="VoidTypeAnnotation",U90="NullLiteralTypeAnnotation",B90="SymbolTypeAnnotation",X90="NumberTypeAnnotation",G90="BigIntTypeAnnotation",Y90="StringTypeAnnotation",J90="BooleanTypeAnnotation",z90=i2,K90="NullableTypeAnnotation",H90="UnknownTypeAnnotation",V90="NeverTypeAnnotation",$90="UndefinedTypeAnnotation",W90=es,Q90=i2,Z90="parameterName",xg0="TypePredicate",rg0="HookTypeAnnotation",eg0="FunctionTypeAnnotation",tg0=pv,ng0=xe,ug0=Ql,ig0=Q8,fg0=yn,cg0=pt,ag0=i2,sg0=Ve,og0=yR,vg0=pt,lg0=i2,pg0=Ve,kg0=yR,mg0=[0,0,0,0,0],dg0="internalSlots",hg0="callProperties",yg0="indexers",gg0=Uk,_g0="exact",wg0=oM,bg0="ObjectTypeAnnotation",Tg0=pD,Eg0="There should not be computed object type property keys",Ag0=la,Sg0=nv,Pg0=G3,Ig0=es,Cg0=Z2,Ng0=Sg,Og0=ze,jg0=pt,Dg0=N6,Fg0=A1,Rg0=is,Mg0="ObjectTypeProperty",Lg0=W1,qg0="ObjectTypeSpreadProperty",Ug0=Z2,Bg0=ze,Xg0=A1,Gg0=is,Yg0=$r,Jg0="ObjectTypeIndexer",zg0=ze,Kg0=A1,Hg0="ObjectTypeCallProperty",Vg0=pt,$g0=Z2,Wg0="sourceType",Qg0="propType",Zg0="keyTparam",x_0="ObjectTypeMappedTypeProperty",r_0=A1,e_0=N6,t_0=ze,n_0=pt,u_0=$r,i_0="ObjectTypeInternalSlot",f_0=C1,c_0=pc,a_0="InterfaceTypeAnnotation",s_0=cM,o_0="ArrayTypeAnnotation",v_0="falseType",l_0="trueType",p_0="extendsType",k_0="checkType",m_0="ConditionalTypeAnnotation",d_0="typeParameter",h_0="InferTypeAnnotation",y_0=$r,g_0=QF,__0="QualifiedTypeIdentifier",w_0=xe,b_0=$r,T_0="GenericTypeAnnotation",E_0="indexType",A_0="objectType",S_0="IndexedAccessType",P_0=pt,I_0="OptionalIndexedAccessType",C_0=K9,N_0="UnionTypeAnnotation",O_0=K9,j_0="IntersectionTypeAnnotation",D_0=Hk,F_0=W1,R_0="TypeofTypeAnnotation",M_0=$r,L_0=QF,q_0="QualifiedTypeofIdentifier",U_0=W1,B_0="KeyofTypeAnnotation",X_0=B3,G_0=HF,Y_0=wF,J_0=i2,z_0=dv,K_0="TypeOperator",H_0=mv,V_0=oM,$_0="elementTypes",W_0="TupleTypeAnnotation",Q_0=pt,Z_0=Z2,xw0=cM,rw0=a6,ew0="TupleTypeLabeledElement",tw0=i2,nw0=a6,uw0="TupleTypeSpreadElement",iw0=Rt,fw0=A1,cw0="StringLiteralTypeAnnotation",aw0=Rt,sw0=A1,ow0="NumberLiteralTypeAnnotation",vw0=Rt,lw0=A1,pw0="BigIntLiteralTypeAnnotation",kw0=as,mw0=xs,dw0=Rt,hw0=A1,yw0="BooleanLiteralTypeAnnotation",gw0="ExistsTypeAnnotation",_w0=i2,ww0=CF,bw0=i2,Tw0=CF,Ew0=yn,Aw0="TypeParameterDeclaration",Sw0="usesExtendsBound",Pw0=sv,Iw0=Z2,Cw0=ao,Nw0="bound",Ow0=Ve,jw0="TypeParameter",Dw0=yn,Fw0=IR,Rw0=yn,Mw0=IR,Lw0=ev,qw0=YM,Uw0="closingElement",Bw0="openingElement",Xw0="JSXElement",Gw0="closingFragment",Yw0=YM,Jw0="openingFragment",zw0="JSXFragment",Kw0=Hk,Hw0="selfClosing",Vw0="attributes",$w0=Ve,Ww0="JSXOpeningElement",Qw0="JSXOpeningFragment",Zw0=Ve,xb0="JSXClosingElement",rb0="JSXClosingFragment",eb0=A1,tb0=Ve,nb0="JSXAttribute",ub0=W1,ib0="JSXSpreadAttribute",fb0="JSXEmptyExpression",cb0=u2,ab0="JSXExpressionContainer",sb0=u2,ob0="JSXSpreadChild",vb0=Rt,lb0=A1,pb0="JSXText",kb0=x8,mb0=a4,db0="JSXMemberExpression",hb0=Ve,yb0=ET,gb0="JSXNamespacedName",_b0=Ve,wb0="JSXIdentifier",bb0=OA,Tb0=b6,Eb0="ExportSpecifier",Ab0=b6,Sb0="ImportDefaultSpecifier",Pb0=b6,Ib0="ImportNamespaceSpecifier",Cb0=uD,Nb0=b6,Ob0="imported",jb0="ImportSpecifier",Db0="Line",Fb0="Block",Rb0=A1,Mb0=A1,Lb0="DeclaredPredicate",qb0="InferredPredicate",Ub0=wb,Bb0=Hk,Xb0=ED,Gb0=j3,Yb0=x8,Jb0=a4,zb0="message",Kb0=W0,Hb0=Xj,Vb0=ND,$b0=bv,Wb0=T6,Qb0=wk,Zb0=[0,$2,vf,Zi,eu,Z2,qf,E7,vc,_c,Ff,Ji,qu,Li,wu,Y7,A7,ri,Bf,Q7,ci,af,H7,f7,Zf,Eu,du,u7,cc,Aa,ac,gf,Af,ba,qc,Nc,nu,xt,o7,Wi,d7,$c,Xi,uc,Zc,et,Yc,Ju,vu,b7,fa,ai,hu,O7,Qe,oi,I7,Of,h7,Pf,P7,ua,He,Lf,Ni,ei,J7,yu,ef,of,M7,Wf,ku,Dc,rf,T7,Hn,jf,Bu,$7,Ei,Vc,Ii,Cu,Ne,xa,c7,rc,Zu,_a,t7,s7,y7,Kf,Sc,n7,a7,Yi,Qf,mf,Gu,sf,$n,Hc,Vu,fi,Di,Wu,xi,vi,v7,Ci,Wc,Gi,da,Gf,Jc,zu,Xu,Si,Pc,Au,r7,ka,hf,ru,Vi,nc,df,Nf,su,bu,j7,va,Su,Ou,Fu,Xc,pu,aa,q7,pi,k2,C7,e7,Lc,li,kf,gu,iu,Tf,zf,pa,wc,Oc,W7,mc,Yu,Jf,k7,X7,uu,Xf,R7,ma,si,qi,Ri,jc,Zn,Hu,ti,cu,lf,le,oc,na,ic,Mu,Df,ca,Qi,Wn,W2,Mi,K7,ia,Ft,ji,Mc,ga,au,p7,ki,Uu,gi,xf,D7,lc,hc,ii,lu,wf,tc,Ta,ju,_f,i7,sa,Oi,yi,$i,ya,bf,g7,N7,Mf,hi,L7,Ic,ra,w7,u2,Vn,Fc,yf,Ea,F7,Qn,zi,Ec,Yf,Uc,Cc,wa,U7,zc,Uf,yc,bc,Pu,Lu,B7,Oe,uf,$u,Kn,kc,Ac,bi,Bc,Ti,pf,Tu,Ki,mu,xc,ta,Ze,Ke,tf,cf,ni,Kc,ea,Z7,_u,$f,Tc,ha,x7,gc,Bi,Hf,fu,ff,xu,dc,Ef,Fi,S7,fc,Vf,z7,ui,Pi,_i,Qu,Rf,Ku,Nu,If,m7,mi,_7,Hi,wi,Sa,rt,ou,Gc,gn,V7,G7,di,oa,l7,Iu,Du,Ai,Rc,nf,sc,ln,Sf],xT0=[0,fa,si,hc,ji,gi,Sa,vi,sf,W7,cu,Gc,Eu,s7,et,_a,Fc,ru,df,Rf,ba,Yi,uu,Ci,oc,R7,bc,U7,vf,Bu,au,Nc,yi,T7,kf,Ac,$f,ga,yc,da,Ft,eu,Mf,Wf,pf,Hi,ii,_7,k7,Ec,Lf,ki,$i,Zc,Hc,K7,Ou,vu,r7,Mu,Pu,li,Gi,Pi,Fi,xa,Sf,yu,Vi,Bi,H7,Gu,ei,pa,Vf,y7,He,a7,xf,c7,Gf,mc,X7,Mc,k2,cf,u7,Cc,j7,Nf,wc,C7,lc,pu,M7,nf,Bc,Qn,Xu,h7,Cu,sc,B7,wi,O7,Z2,Ni,mu,A7,I7,Mi,Au,di,Oi,nu,jc,Qu,Di,uc,Oe,g7,du,$2,e7,qc,Ki,ef,qu,Vc,xc,Xc,Pf,oa,Ji,Ef,Tu,xi,vc,bu,Xf,Du,ac,ti,Pc,zc,Ic,t7,yf,Lc,Ei,hi,Bf,Aa,Hf,qf,gf,ai,ri,Ju,Ff,z7,na,ya,S7,pi,D7,fu,_c,xu,Wu,iu,Tc,l7,Si,If,_i,x7,va,$7,Tf,gc,zu,W2,Qe,Y7,Q7,Yc,jf,xt,Ze,v7,ia,Zu,w7,ma,Ne,ou,Ta,Uf,ic,fc,Vn,lu,qi,Uu,wu,wa,ua,i7,Qi,nc,Uc,F7,ea,fi,hu,$n,Df,Sc,Dc,cc,mf,J7,n7,ci,bi,su,m7,Ti,V7,Zn,Xi,Lu,Af,p7,P7,Wn,Qf,Nu,Vu,Jf,ui,Fu,lf,uf,Zf,ln,E7,$u,mi,f7,Kn,Yu,Ri,ni,gu,rf,Wc,Hu,L7,Su,zf,af,d7,Iu,hf,Z7,Hn,Ai,G7,u2,Ii,ff,ra,ka,ju,_f,b7,Ke,wf,ha,of,zi,kc,dc,Wi,$c,sa,Ea,Li,bf,aa,rc,gn,ca,Ku,Oc,Kf,tc,ta,oi,Yf,rt,Rc,le,q7,tf,Zi,_u,Of,N7,Kc,ku,o7,Jc],rT0=[0,Sf,ln,sc,nf,Rc,Ai,Du,Iu,l7,oa,di,G7,V7,gn,Gc,ou,rt,Sa,wi,Hi,_7,mi,m7,If,Nu,Ku,Rf,Qu,_i,Pi,ui,z7,Vf,fc,S7,Fi,Ef,dc,xu,ff,fu,Hf,Bi,gc,x7,ha,Tc,$f,_u,Z7,ea,Kc,ni,cf,tf,Ke,Ze,ta,xc,mu,Ki,Tu,pf,Ti,Bc,bi,Ac,kc,Kn,$u,uf,Oe,B7,Lu,Pu,bc,yc,Uf,zc,U7,wa,Cc,Uc,Yf,Ec,zi,Qn,F7,Ea,yf,Fc,Vn,u2,w7,ra,Ic,L7,hi,Mf,N7,g7,bf,ya,$i,yi,Oi,sa,i7,_f,ju,Ta,tc,wf,lu,ii,hc,lc,D7,xf,gi,Uu,ki,p7,au,ga,Mc,ji,Ft,ia,K7,Mi,W2,Wn,Qi,ca,Df,Mu,ic,na,oc,le,lf,cu,ti,Hu,Zn,jc,Ri,qi,si,ma,R7,Xf,uu,X7,k7,Jf,Yu,mc,W7,Oc,wc,pa,zf,Tf,iu,gu,kf,li,Lc,e7,C7,k2,pi,q7,aa,pu,Xc,Fu,Ou,Su,va,j7,bu,su,Nf,df,nc,Vi,ru,hf,ka,r7,Au,Pc,Si,Xu,zu,Jc,Gf,da,Gi,Wc,Ci,v7,vi,xi,Wu,Di,fi,Vu,Hc,$n,sf,Gu,mf,Qf,Yi,a7,n7,Sc,Kf,y7,s7,t7,_a,Zu,rc,c7,xa,Ne,Cu,Ii,Vc,Ei,$7,Bu,jf,Hn,T7,rf,Dc,ku,Wf,M7,of,ef,yu,J7,ei,Ni,Lf,He,ua,P7,Pf,h7,Of,I7,oi,Qe,O7,hu,ai,fa,b7,vu,Ju,Yc,et,Zc,uc,Xi,$c,d7,Wi,o7,xt,nu,Nc,qc,ba,Af,gf,ac,Aa,cc,u7,du,Eu,Zf,f7,H7,af,ci,Q7,Bf,ri,A7,Y7,wu,Li,qu,Ji,Ff,_c,vc,E7,qf,Z2,eu,Zi,vf,$2],eT0="Jsoo_runtime.Error.Exn",tT0=[0,0],nT0="assert_operator",uT0="use_strict",iT0=K9,fT0="esproposal_decorators",cT0="pattern_matching",aT0="enums",sT0="components",oT0="Internal error: ",vT0=[f1,"CamlinternalLazy.Undefined",Ca(0)];function lT0(x,r){var e=Ux(r)-1|0,t=0;if(e>=0)for(var u=t;;){x(J0(r,u));var i=u+1|0;if(e===u)break;var u=i}}var pT0=sx,kT0=[0,0];function mT0(x){var r=ZJ(0),e=Cq(j),t=r.length-1,u=I1((t*8|0)+1|0),i=t-1|0,c=0;if(i>=0)for(var v=c;;){WY(u,v*8|0,q6(N1(r,v)[1+v]));var s=v+1|0;if(i===v)break;var v=s}ls(u,t*8|0,1);var l=Iq(u);ls(u,t*8|0,2);var p=Iq(u),d=Pd(p,8),T=Pd(p,0),b=Pd(l,8);return Nq(e,Pd(l,0),b,T,d),e}for(;;){var uU=rl(eN);let x=[0,1],r=uU;if(!(1-ud(eN,uU,function(e){return ud(x,1,0)&&(Lv(Rv(eU),j),Lv(Rv(tU),j)),h(r,0)})))break}if(rl(kT0))throw z0([0,bd,s$],1);var bs=iN([0,sx]),qv=iN([0,sx]),lo=iN([0,je]),iU=QC(0,0),dT0=2,hT0=[0,0];function fU(x){return 2<x?fU((x+1|0)/2|0)*2|0:x}function cU(x){hT0[1]++;var r=x.length-1,e=oo((r*2|0)+2|0,iU);N1(e,0)[1]=r;var t=((fU(r)*32|0)/8|0)-1|0;N1(e,1)[2]=t;var u=r-1|0,i=0;if(u>=0)for(var c=i;;){var v=(c*2|0)+3|0,s=N1(x,c)[1+c];N1(e,v)[1+v]=s;var l=c+1|0;if(u===c)break;var c=l}return[0,dT0,e,qv[1],lo[1],0,0,bs[1],0]}function DN(x,r){var e=x[2].length-1;if(e<r){var t=oo(r,iU);pq(x[2],0,t,0,e),x[2]=t}}function yT0(x){var r=[0,0],e=Ux(x)-1|0,t=0;if(e>=0)for(var u=t;;){var i=B1(x,u);r[1]=(Ok*r[1]|0)+i|0;var c=u+1|0;if(e===u)break;var u=c}r[1]=r[1]&sR;var v=1073741823<r[1]?r[1]+2147483648|0:r[1];return v}var gT0=[0,0];function FN(x){var r=x[2].length-1;return DN(x,r+1|0),r}function ip(x,r){try{var e=qv[17].call(null,r,x[3]);return e}catch(i){var t=X1(i);if(t!==Na)throw z0(t,0);var u=FN(x);return x[3]=qv[2].call(null,r,u,x[3]),x[4]=lo[2].call(null,u,1,x[4]),u}}function RN(x,r){return Id(function(e){return ip(x,e)},r)}function aU(x,r,e){if(gT0[1]++,lo[17].call(null,r,x[4])){DN(x,r+1|0),N1(x[2],r)[1+r]=e;return}x[6]=[0,[0,r,e],x[6]]}function MN(x){if(x===0)return 0;for(var r=x.length-1-1|0,e=0;;){if(0>r)return e;var t=[0,x[1+r],e],r=r-1|0,e=t}}function LN(x,r){try{var e=bs[17].call(null,r,x[7]);return e}catch(i){var t=X1(i);if(t!==Na)throw z0(t,0);var u=x[1];return x[1]=u+1|0,P(r,tx)&&(x[7]=bs[2].call(null,r,u,x[7])),u}}function qN(x){return il(x,0)?[0]:x}function UN(x,r,e,t,u,i){var c=u[2],v=u[4],s=MN(r),l=MN(e),p=MN(t),d=Pn(function(x0){return ip(x,x0)},l),T=Pn(function(x0){return ip(x,x0)},p);x[5]=[0,[0,x[3],x[4],x[6],x[7],d,s],x[5]],x[7]=bs[24].call(null,function(x0,i0,f0){return uN(x0,s)?bs[2].call(null,x0,i0,f0):f0},x[7],bs[1]);var b=[0,qv[1]],C=[0,lo[1]];cq(function(x0,i0){b[1]=qv[2].call(null,x0,i0,b[1]);var f0=C[1];try{var r0=lo[17].call(null,i0,x[4]),v0=r0}catch(w0){var o0=X1(w0);if(o0!==Na)throw z0(o0,0);var v0=1}C[1]=lo[2].call(null,i0,v0,f0)},p,T),cq(function(x0,i0){b[1]=qv[2].call(null,x0,i0,b[1]),C[1]=lo[2].call(null,i0,0,C[1])},l,d),x[3]=b[1],x[4]=C[1],x[6]=nN(function(x0,i0){return uN(x0[1],d)?i0:[0,x0,i0]},x[6],0);var N=i?h(c(x),v):c(x),I=H6(x[5]),F=I[6],L=I[5],X=I[4],q=I[3],J=I[2],e0=I[1];x[5]=fq(x[5]),x[7]=y2(function(x0,i0){var f0=bs[17].call(null,i0,x[7]);return bs[2].call(null,i0,f0,x0)},X,F),x[3]=e0,x[4]=J,x[6]=nN(function(x0,i0){return uN(x0[1],L)?i0:[0,x0,i0]},x[6],q);var W=[0,Id(function(x0){var i0=ip(x,x0);try{for(var f0=x[6];;){if(!f0)throw z0(Na,1);var r0=f0[1],v0=f0[2],o0=r0[2];if(NL(r0[1],i0)===0)return o0;var f0=v0}}catch(t0){var w0=X1(t0);if(w0===Na)return N1(x[2],i0)[1+i0];throw z0(w0,0)}},qN(t)),0];return XY([0,[0,N],[0,Id(function(x0){try{var i0=bs[17].call(null,x0,x[7]);return i0}catch(r0){var f0=X1(r0);throw f0===Na?z0([0,Nr,o$],1):z0(f0,0)}},qN(r)),W]])}function Rd(x,r){if(x===0)var e=cU([0]);else{var t=cU(Id(yT0,x)),u=x.length-1-1|0,i=0;if(u>=0)for(var c=i;;){var v=(c*2|0)+2|0;t[3]=qv[2].call(null,x[1+c],v,t[3]),t[4]=lo[2].call(null,v,1,t[4]);var s=c+1|0;if(u===c)break;var c=s}var e=t}var l=r(e);return e[8]=cx(e[8]),DN(e,3+((N1(e[2],1)[2]*16|0)/32|0)|0),[0,h(l,0),r,,0]}function Md(x,r){if(x)return x;var e=QC(f1,r[1]);return e[1]=r[2],KJ(e)}function BN(x,r,e){if(x)return r;var t=e[8];if(t!==0)for(var u=t;u;){var i=u[2];h(u[1],r);var u=i}return r}function Ld(x){var r=FN(x);x:{if((r%2|0)!==0&&(2+((N1(x[2],1)[2]*16|0)/32|0)|0)>=r){var e=FN(x);break x}var e=r}return N1(x[2],e)[1+e]=0,e}function XN(x,r){for(var e=[0,0],t=r.length-1;;){if(e[1]>=t)return;var u=e[1],i=function(Z0){e[1]++;var N0=e[1];return N1(r,N0)[1+N0]},c=N1(r,u)[1+u],v=i(j);if(typeof v=="number")switch(v){case 0:let Z0=i(j);var S0=function(hx){return Z0};break;case 1:let N0=i(j);var S0=function(hx){return hx[1+N0]};break;case 2:var s=i(j);let ux=s,ex=i(j);var S0=function(hx){return hx[1+ux][1+ex]};break;case 3:let nx=i(j);var S0=function(hx){return h(hx[1][1+nx],hx)};break;case 4:let px=i(j);var S0=function(hx,Nx){return hx[1+px]=Nx,0};break;case 5:var l=i(j);let D0=l,dx=i(j);var S0=function(hx){return h(D0,dx)};break;case 6:var p=i(j);let _x=p,K=i(j);var S0=function(hx){return h(_x,hx[1+K])};break;case 7:var d=i(j),T=i(j);let _0=d,U=T,m0=i(j);var S0=function(hx){return h(_0,hx[1+U][1+m0])};break;case 8:var b=i(j);let b0=b,y0=i(j);var S0=function(hx){return h(b0,h(hx[1][1+y0],hx))};break;case 9:var C=i(j),N=i(j);let E0=C,$0=N,z=i(j);var S0=function(hx){return k(E0,$0,z)};break;case 10:var I=i(j),F=i(j);let Dx=I,Xx=F,K0=i(j);var S0=function(hx){return k(Dx,Xx,hx[1+K0])};break;case 11:var L=i(j),X=i(j),q=i(j);let A=L,V=X,fx=q,wx=i(j);var S0=function(hx){return k(A,V,hx[1+fx][1+wx])};break;case 12:var J=i(j),e0=i(j);let Ix=J,ox=e0,xr=i(j);var S0=function(hx){return k(Ix,ox,h(hx[1][1+xr],hx))};break;case 13:var W=i(j),x0=i(j);let Fx=W,H0=x0,ur=i(j);var S0=function(hx){return k(Fx,hx[1+H0],ur)};break;case 14:var i0=i(j),f0=i(j),r0=i(j);let X0=i0,or=f0,Q0=r0,yx=i(j);var S0=function(hx){return k(X0,hx[1+or][1+Q0],yx)};break;case 15:var v0=i(j),o0=i(j);let ix=v0,ax=o0,$x=i(j);var S0=function(hx){return k(ix,h(hx[1][1+ax],hx),$x)};break;case 16:var w0=i(j);let fr=w0,gr=i(j);var S0=function(hx){return k(hx[1][1+fr],hx,gr)};break;case 17:var t0=i(j);let jr=t0,c1=i(j);var S0=function(hx){return k(hx[1][1+jr],hx,hx[1+c1])};break;case 18:var s0=i(j),h0=i(j);let Dr=s0,e1=h0,Ex=i(j);var S0=function(hx){return k(hx[1][1+Dr],hx,hx[1+e1][1+Ex])};break;case 19:var p0=i(j);let _=p0,$=i(j);var S0=function(hx){var Nx=h(hx[1][1+$],hx);return k(hx[1][1+_],hx,Nx)};break;case 20:var C0=i(j),j0=i(j);Ld(x);let vx=C0,L0=j0;var S0=function(hx){return h(Kx(L0,vx,0),L0)};break;case 21:var P0=i(j),M0=i(j);Ld(x);let lx=P0,Px=M0;var S0=function(hx){var Nx=hx[1+Px];return h(Kx(Nx,lx,0),Nx)};break;case 22:var U0=i(j),T0=i(j),G0=i(j);Ld(x);let Ar=U0,Hx=T0,a1=G0;var S0=function(hx){var Nx=hx[1+Hx][1+a1];return h(Kx(Nx,Ar,0),Nx)};break;default:var k0=i(j),G=i(j);Ld(x);let v1=k0,Sr=G;var S0=function(hx){var Nx=h(hx[1][1+Sr],hx);return h(Kx(Nx,v1,0),Nx)}}else var S0=v;aU(x,c,S0),e[1]++}}function sU(x,r){var e=r.length-1,t=QC(0,e),u=e-1|0,i=0;if(u>=0)for(var c=i;;){var v=N1(r,c)[1+c];if(typeof v=="number")switch(v){case 0:let C=c;var s=function(X){var q=t[1+C];if(N===q)throw z0([0,Y6,x],1);return h(q,X)};let N=s;var d=s;break;case 1:var l=[];let I=l,F=c;qr(l,[z3,function(X){var q=t[1+F];if(I===q)throw z0([0,Y6,x],1);var J=Ov(q);if(Z3===J)return q[1];if(z3!==J&&fv!==J)return q;if(bJ(q)!==0)throw z0(vT0,1);var e0=q[1];q[1]=0;try{var W=h(e0,0);return q[1]=W,TJ(q),W}catch(i0){var x0=X1(i0);throw q[1]=function(f0){throw z0(x0,0)},wJ(q),z0(x0,0)}}]);var d=l;break;default:var p=function(X){throw z0([0,Y6,x],1)},d=[0,p,p,p,0]}else var d=v[0]===0?sU(x,v[1]):v[1];t[1+c]=d;var T=c+1|0;if(u===c)break;var c=T}return t}function oU(x,r,e){if(Ov(e)===0&&x.length-1<=e.length-1){var t=x.length-1-1|0,u=0;if(t>=0)for(var i=u;;){var c=e[1+i],v=N1(x,i)[1+i];x:if(typeof v=="number"){if(v===2){if(Ov(c)===0&&c.length-1===4){for(var s=0,l=r[1+i];;){l[1+s]=c[1+s];var p=s+1|0;if(s===3)break;var s=p}break x}throw z0([0,Nr,v$],1)}r[1+i]=c}else v[0]===0&&oU(v[1],r[1+i],c);var d=i+1|0;if(t===i)break;var i=d}return}throw z0([0,Nr,l$],1)}try{var _T0=HL("TMPDIR"),GN=_T0}catch(x){var vU=X1(x);if(vU!==Na)throw z0(vU,0);var GN=p$}var wT0=[0,,,,,,,,,,GN];try{var bT0=HL("TEMP"),lU=bT0}catch(x){var pU=X1(x);if(pU!==Na)throw z0(pU,0);var lU=k$}var TT0=[0,,,,,,,,,,lU],ET0=[0,,,,,,,,,,GN],AT0=P(uq,cF)?P(uq,"Win32")?wT0:TT0:ET0,ST0=AT0[10];Oa(0,mT0),Oa([0,function(x){return x}],function(x){return ST0});function ja(x,r){function e(t){return ht(x,t)}return E6<=r?(e(U3|r>>>18|0),e(U1|(r>>>12|0)&63),e(U1|(r>>>6|0)&63),e(U1|r&63)):pw<=r?(e(Qo|r>>>12|0),e(U1|(r>>>6|0)&63),e(U1|r&63)):U1<=r?(e(Zo|r>>>6|0),e(U1|r&63)):e(r)}var po=[f1,h$,Ca(0)],kU=0,mU=0,dU=0,hU=0,yU=0,gU=0,_U=0,wU=0,bU=0,TU=0;function y(x){if(x[3]===x[2])return-1;var r=x[1][1+x[3]];return x[3]=x[3]+1|0,r===10&&(x[5]!==0&&(x[5]=x[5]+1|0),x[4]=x[3]),r}function H(x,r){x[9]=x[3],x[10]=x[4],x[11]=x[5],x[12]=r}function mr(x){return x[6]=x[3],x[7]=x[4],x[8]=x[5],H(x,-1)}function w(x){return x[3]=x[9],x[4]=x[10],x[5]=x[11],x[12]}function hl(x){x[3]=x[6],x[4]=x[7],x[5]=x[8]}function YN(x,r){x[6]=r}function qd(x){return x[3]-x[6]|0}function m1(x){var r=x[3]-x[6]|0,e=x[6],t=x[1];return 0<=e&&0<=r&&(t.length-1-r|0)>=e?GY(t,e,r):X2(a$)}function EU(x){var r=x[6];return N1(x[1],r)[1+r]}function fp(x,r,e,t){for(var u=[0,r],i=[0,e],c=[0,0];;){if(0>=i[1])return c[1];var v=x[1+u[1]];if(0>v)throw z0(po,1);if(Jr<v)if(FM<v)if($m<v){if(dk<v)throw z0(po,1);zr(t,c[1],U3|v>>>18|0),zr(t,c[1]+1|0,U1|(v>>>12|0)&63),zr(t,c[1]+2|0,U1|(v>>>6|0)&63),zr(t,c[1]+3|0,U1|v&63),c[1]=c[1]+4|0}else zr(t,c[1],Qo|v>>>12|0),zr(t,c[1]+1|0,U1|(v>>>6|0)&63),zr(t,c[1]+2|0,U1|v&63),c[1]=c[1]+3|0;else zr(t,c[1],Zo|v>>>6|0),zr(t,c[1]+1|0,U1|v&63),c[1]=c[1]+2|0;else zr(t,c[1],v),c[1]++;u[1]++,i[1]+=-1}}function AU(x){for(var r=Ux(x),e=oo(r,0),t=[0,0],u=[0,0];;){if(t[1]>=r)return[0,e,u[1],TU,bU,wU,_U,gU,yU,hU,dU,mU,kU];var i=J0(x,t[1]);x:{if(Zo<=i){if(U3>i){if(Qo>i){var c=J0(x,t[1]+1|0);if((c>>>6|0)!==2)throw z0(po,1);e[1+u[1]]=(i&31)<<6|c&63,t[1]=t[1]+2|0;break x}var v=J0(x,t[1]+1|0),s=J0(x,t[1]+2|0),l=(i&15)<<12|(v&63)<<6|s&63,p=(v>>>6|0)!==2?1:0,d=p||((s>>>6|0)!==2?1:0);if(d)var b=d;else var T=55296<=l?1:0,b=T&&(l<=57343?1:0);if(b)throw z0(po,1);e[1+u[1]]=l,t[1]=t[1]+3|0;break x}if(f1>i){var C=J0(x,t[1]+1|0),N=J0(x,t[1]+2|0),I=J0(x,t[1]+3|0),F=(C>>>6|0)!==2?1:0;if(F)var X=F;else var L=(N>>>6|0)!==2?1:0,X=L||((I>>>6|0)!==2?1:0);if(X)throw z0(po,1);var q=(i&7)<<18|(C&63)<<12|(N&63)<<6|I&63;if(dk<q)throw z0(po,1);e[1+u[1]]=q,t[1]=t[1]+4|0;break x}}else if(U1>i){e[1+u[1]]=i,t[1]++;break x}throw z0(po,1)}u[1]++}}function cp(x,r,e){var t=x[6]+r|0,u=I1(e*4|0),i=x[1];if((t+e|0)<=i.length-1)return ol(u,0,fp(i,t,e,u));throw z0([0,Nr,d$],1)}function Bx(x){var r=x[6],e=x[3]-r|0,t=I1(e*4|0);return ol(t,0,fp(x[1],r,e,t))}function Ud(x,r){var e=x[6],t=x[3]-e|0,u=I1(t*4|0);return oN(r,u,0,fp(x[1],e,t,u))}function ap(x){var r=x.length-1,e=I1(r*4|0);return ol(e,0,fp(x,0,r,e))}function SU(x,r){x[3]=x[3]-r|0}function Da(x){return typeof x=="number"?0:x[0]===0?1:x[1]}function Uv(x,r,e,t){var u=Da(x),i=Da(t),c=i<=u?u+1|0:i+1|0;return c===1?[0,r,e]:[1,c,r,e,x,t]}function Bd(x,r,e,t){var u=Da(x),i=Da(t),c=i<=u?u+1|0:i+1|0;return[1,c,r,e,x,t]}function PU(x,r,e,t){var u=Da(x),i=Da(t);if((i+2|0)<u){var c=x[5],v=x[4],s=x[3],l=x[2],p=Da(c);if(p<=Da(v))return Bd(v,l,s,Uv(c,r,e,t));var d=c[4],T=c[3],b=c[2],C=Uv(c[5],r,e,t);return Bd(Uv(v,l,s,d),b,T,C)}if((u+2|0)>=i)return Uv(x,r,e,t);var N=t[5],I=t[4],F=t[3],L=t[2],X=Da(I);if(X<=Da(N))return Bd(Uv(x,r,e,I),L,F,N);var q=I[4],J=I[3],e0=I[2],W=Uv(I[5],L,F,N);return Bd(Uv(x,r,e,q),e0,J,W)}function ko(x){return typeof x=="number"?0:x[0]===0?1:x[1]}function Ts(x,r,e){x:{r:{if(typeof x=="number"){if(typeof e=="number")return[0,r];if(e[0]===1)break r}else{if(x[0]!==0){var t=x[1];if(typeof e!="number"&&e[0]===1){var u=e[1],i=u<=t?t+1|0:u+1|0;return[1,i,r,x,e]}var c=t;break x}if(typeof e!="number"&&e[0]===1)break r}return[1,2,r,x,e]}var c=e[1]}return[1,c+1|0,r,x,e]}function Xd(x,r,e){var t=ko(x),u=ko(e),i=u<=t?t+1|0:u+1|0;return[1,i,r,x,e]}function IU(x,r,e){var t=ko(x),u=ko(e);if((u+2|0)<t){var i=x[4],c=x[3],v=x[2],s=ko(i);if(s<=ko(c))return Xd(c,v,Ts(i,r,e));var l=i[3],p=i[2],d=Ts(i[4],r,e);return Xd(Ts(c,v,l),p,d)}if((t+2|0)>=u)return Ts(x,r,e);var T=e[4],b=e[3],C=e[2],N=ko(b);if(N<=ko(T))return Xd(Ts(x,r,b),C,T);var I=b[3],F=b[2],L=Ts(b[4],C,T);return Xd(Ts(x,r,I),F,L)}var JN=0;function CU(x){function r(e,t){if(typeof t=="number")return[0,e];if(t[0]===0){var u=t[1],i=k(x[1],e,u);return i===0?t:0<=i?Ts(t,e,JN):Ts([0,e],u,JN)}var c=t[4],v=t[3],s=t[2],l=k(x[1],e,s);if(l===0)return t;if(0<=l){var p=r(e,c);return c===p?t:IU(v,s,p)}var d=r(e,v);return v===d?t:IU(d,s,c)}return[0,JN,,function(e,t){for(var u=t;;){if(typeof u=="number")return 0;if(u[0]===0)return k(x[1],e,u[1])===0?1:0;var i=u[4],c=u[3],v=k(x[1],e,u[2]),s=v===0?1:0;if(s)return s;var l=0<=v?i:c,u=l}},r]}function NU(x){switch(x[0]){case 0:return 1;case 1:return 2;case 2:return 2;default:return 3}}function Rx(x,r){if(!r)return r;var e=r[1],t=h(x,e);return e===t?r:[0,t]}function I0(x,r,e,t,u){var i=k(x,r,e);return e===i?t:u(i)}function A0(x,r,e,t){var u=h(x,r);return r===u?e:t(u)}function Q1(x,r){var e=r[1];return I0(x,e,r[2],r,function(t){return[0,e,t]})}function sp(x,r){return Rx(function(e){var t=e[1];return I0(x,t,e[2],e,function(u){return[0,t,u]})},r)}function vr(x,r){var e=y2(function(u,i){var c=u[2],v=u[1],s=h(x,i),l=c||(s!==i?1:0);return[0,[0,s,v],l]},$$,r),t=e[1];return e[2]?cx(t):r}var zN=Rd(Q$,function(x){var r=RN(x,W$),e=r[1],t=r[2],u=r[3],i=r[4],c=r[5],v=r[6],s=r[7],l=r[8],p=r[9],d=r[10],T=r[11],b=r[12],C=r[13],N=r[14],I=r[15],F=r[16],L=r[17],X=r[18],q=r[19],J=r[20],e0=r[21],W=r[22],x0=r[23],i0=r[24],f0=r[25],r0=r[26],v0=r[27],o0=r[28],w0=r[29],t0=r[30],s0=r[31],h0=r[32],p0=r[33],C0=r[34],j0=r[35],P0=r[36],M0=r[37],U0=r[38],T0=r[39],G0=r[40],k0=r[41],G=r[42],S0=r[43],Z0=r[44],N0=r[45],ux=r[46],ex=r[47],nx=r[48],px=r[49],D0=r[50],dx=r[51],_x=r[52],K=r[53],_0=r[54],U=r[55],m0=r[56],b0=r[57],y0=r[58],E0=r[60],$0=r[61],z=r[62],Dx=r[63],Xx=r[64],K0=r[65],A=r[66],V=r[67],fx=r[68],wx=r[69],Ix=r[70],ox=r[71],xr=r[72],Fx=r[73],H0=r[74],ur=r[75],X0=r[76],or=r[77],Q0=r[78],yx=r[79],ix=r[80],ax=r[81],$x=r[82],fr=r[83],gr=r[84],jr=r[85],c1=r[86],Dr=r[87],e1=r[88],Ex=r[89],_=r[90],$=r[91],vx=r[92],L0=r[93],lx=r[94],Px=r[95],Ar=r[96],Hx=r[97],a1=r[98],v1=r[99],Sr=r[E1],lr=r[pe],hx=r[E2],Nx=r[wn],rr=r[We],Vr=r[vn],J1=r[kt],ie=r[q1],J2=r[Sv],ft=r[lt],bt=r[B2],js=r[U2],Ds=r[ss],R2=r[ke],w2=r[br],S1=r[gv],Dn=r[Tv],Fn=r[Q3],Ba=r[g6],fe=r[D3],ct=r[Cf],u3=r[O6],Tt=r[o1],_r=r[pn],Fs=r[J3],Xa=r[to],Zt=r[Gk],i3=r[Jr],f3=r[U1],Rn=r[uv],Rs=r[D6],Ms=r[h6],Co=r[hm],Ls=r[qk],qs=r[DR],No=r[gR],Mn=r[XD],ge=r[nD],b2=r[eL],Oo=r[iR],Et=r[VM],Us=r[ME],c3=r[vL],Bs=r[rM],jo=r[144],Fl=r[145],a3=r[146],Do=r[147],Xs=r[148],Fo=r[149],Lp=r[150],qp=r[151],Rl=r[152],At=r[153],Up=r[HD],Gs=r[155],c5=r[156],s3=r[157],Ml=r[158],Ll=r[159],Ro=r[GP],Bp=r[gM],Xp=r[WR],a5=r[x1],s5=r[SD],o5=r[FF],Gp=r[cD],Yp=r[IF],Jp=r[qM],ql=r[LD],B=r[U5],S=r[yk],D=r[dw],c0=r[MR],d0=r[$M],O0=r[VD],rx=r[xR],kx=r[kS],Ox=r[PD],Lx=r[iL],ir=r[RD],Qx=r[rA],er=r[Fy],pr=r[O4],tr=r[E8],wr=r[Nj],Fr=r[AS],qx=r[M4],Ur=r[wD],Pr=r[_A],t1=r[tE],h1=r[A8],y1=r[Zo],Lr=r[AD],g1=r[qI],n1=r[GF],Ir=r[wM],Cr=r[MD],l1=r[_I],Br=r[DM],u1=r[NM],$1=r[mF],Or=r[aD],Xr=r[Yj],p1=r[lR],s1=r[AR],M1=r[Ik],l2=r[PF],T2=r[iy],_e=r[sF],z2=r[oD],i1=r[bD],ce=r[lF],L1=r[sD],St=r[WM],M2=r[jg],Pt=r[I5],xn=r[SR],at=r[Sj],st=r[vM],It=r[NF],Ct=r[uR],Mx=r[Hy],r2=r[Ok],we=r[Qo],Ue=r[aF],be=r[nR],ot=r[FR],vt=r[Qj],p2=r[PM],ae=r[_F],se=r[Hj],rn=r[Aj],Be=r[yM],Nt=r[IM],L2=r[qR],en=r[BR],K2=r[MM],Xe=r[ZF],tn=r[Qy],Ga=r[U3],Ln=r[jF],Ya=r[nM],Ge=r[QR],Ja=r[fv],Ot=r[iE],nn=r[z3],un=r[h4],qn=r[f1],Ys=r[tA],Ul=r[Z3],za=r[fL],Js=r[q3],o3=r[_E],Mo=r[M3],zs=r[pk],v3=r[k6],Ks=r[257],Bl=r[258],Xl=r[259],l3=r[260],Lo=r[nF],p3=r[BM],Gl=r[263],Hs=r[264],qo=r[265],Uo=r[266],k3=r[267],jt=r[268],Yl=r[VR],Vs=r[270],$s=r[271],m3=r[272],d3=r[273],Bo=r[oL],Jl=r[dD],fn=r[276],Xo=r[277],Ws=r[hF],zl=r[WF],Kl=r[qj],Hl=r[bM],Go=r[mD],h3=r[Oj],Un=r[284],Yo=r[BD],Jo=r[286],zo=r[287],Bn=r[288],Ko=r[289],Dt=r[WD],Ka=r[eM],Ye=r[292],y3=r[293],g3=r[294],Ho=r[yD],Vo=r[296],_3=r[CD],Vl=r[298],Xn=r[299],v5=r[300],Gn=r[KD],Yn=r[302],w3=r[303],l5=r[Mj],Jn=r[305],b3=r[SM],zp=r[kM],p5=r[308],k5=r[309],m5=r[310],d5=r[mM],T3=r[312],Kp=r[RM],Hp=r[RR];return XN(x,[0,r[59],function(n,a){var f=a[2],o=f[4],m=f[3],g=f[1],E=f[2],O=a[1],R=k(n[1][1+P0],n,g),u0=k(n[1][1+G],n,m),l0=vr(h(n[1][1+fn],n),o);return g===R&&m===u0&&o===l0?a:[0,O,[0,R,E,u0,l0]]},D0,function(n,a){var f=a[2],o=a[1];switch(f[0]){case 0:var m=f[1];return I0(h(n[1][1+Gn],n),o,m,a,function(mx){return[0,o,[0,mx]]});case 1:var g=f[1];return I0(h(n[1][1+Vl],n),o,g,a,function(mx){return[0,o,[1,mx]]});case 2:var E=f[1];return I0(h(n[1][1+Ko],n),o,E,a,function(mx){return[0,o,[2,mx]]});case 3:var O=f[1];return I0(h(n[1][1+Bo],n),o,O,a,function(mx){return[0,o,[3,mx]]});case 4:var R=f[1];return I0(h(n[1][1+Xl],n),o,R,a,function(mx){return[0,o,[4,mx]]});case 5:var u0=f[1];return I0(h(n[1][1+Bl],n),o,u0,a,function(mx){return[0,o,[5,mx]]});case 6:var l0=f[1];return I0(h(n[1][1+Ks],n),o,l0,a,function(mx){return[0,o,[6,mx]]});case 7:var F0=f[1];return I0(h(n[1][1+v3],n),o,F0,a,function(mx){return[0,o,[7,mx]]});case 8:var V0=f[1];return I0(h(n[1][1+zs],n),o,V0,a,function(mx){return[0,o,[8,mx]]});case 9:var Cx=f[1];return I0(h(n[1][1+Mo],n),o,Cx,a,function(mx){return[0,o,[9,mx]]});case 10:var jx=f[1];return I0(h(n[1][1+Js],n),o,jx,a,function(mx){return[0,o,[10,mx]]});case 11:var kr=f[1];return I0(h(n[1][1+za],n),o,kr,a,function(mx){return[0,o,[11,mx]]});case 12:var Qr=f[1];return I0(h(n[1][1+Ul],n),o,Qr,a,function(mx){return[0,o,[12,mx]]});case 13:var Zr=f[1];return I0(h(n[1][1+Ys],n),o,Zr,a,function(mx){return[0,o,[13,mx]]});case 14:var Wx=f[1];return I0(h(n[1][1+qn],n),o,Wx,a,function(mx){return[0,o,[14,mx]]});case 15:var P1=f[1];return I0(h(n[1][1+un],n),o,P1,a,function(mx){return[0,o,[15,mx]]});case 16:var e2=f[1];return I0(h(n[1][1+e1],n),o,e2,a,function(mx){return[0,o,[16,mx]]});case 17:var q2=f[1];return I0(h(n[1][1+nn],n),o,q2,a,function(mx){return[0,o,[17,mx]]});case 18:var Te=f[1];return I0(h(n[1][1+Ja],n),o,Te,a,function(mx){return[0,o,[18,mx]]});case 19:var Ee=f[1];return I0(h(n[1][1+Ge],n),o,Ee,a,function(mx){return[0,o,[19,mx]]});case 20:var Je=f[1];return I0(h(n[1][1+K2],n),o,Je,a,function(mx){return[0,o,[20,mx]]});case 21:var H2=f[1];return I0(h(n[1][1+vt],n),o,H2,a,function(mx){return[0,o,[21,mx]]});case 22:var Ae=f[1];return I0(h(n[1][1+be],n),o,Ae,a,function(mx){return[0,o,[22,mx]]});case 23:var Se=f[1];return I0(h(n[1][1+It],n),o,Se,a,function(mx){return[0,o,[23,mx]]});case 24:var cn=f[1];return I0(h(n[1][1+z2],n),o,cn,a,function(mx){return[0,o,[24,mx]]});case 25:var oe=f[1];return I0(h(n[1][1+xn],n),o,oe,a,function(mx){return[0,o,[25,mx]]});case 26:var an=f[1];return I0(h(n[1][1+ce],n),o,an,a,function(mx){return[0,o,[26,mx]]});case 27:var sn=f[1];return I0(h(n[1][1+s1],n),o,sn,a,function(mx){return[0,o,[27,mx]]});case 28:var Ha=f[1];return I0(h(n[1][1+tr],n),o,Ha,a,function(mx){return[0,o,[28,mx]]});case 29:var Va=f[1];return I0(h(n[1][1+er],n),o,Va,a,function(mx){return[0,o,[29,mx]]});case 30:var $a=f[1];return I0(h(n[1][1+c0],n),o,$a,a,function(mx){return[0,o,[30,mx]]});case 31:var zn=f[1];return I0(h(n[1][1+c3],n),o,zn,a,function(mx){return[0,o,[31,mx]]});case 32:var E3=f[1];return I0(h(n[1][1+_r],n),o,E3,a,function(mx){return[0,o,[32,mx]]});case 33:var Wa=f[1];return I0(h(n[1][1+_0],n),o,Wa,a,function(mx){return[0,o,[33,mx]]});case 34:var A3=f[1];return I0(h(n[1][1+N0],n),o,A3,a,function(mx){return[0,o,[34,mx]]});case 35:var S3=f[1];return I0(h(n[1][1+M0],n),o,S3,a,function(mx){return[0,o,[35,mx]]});case 36:var P3=f[1];return I0(h(n[1][1+C0],n),o,P3,a,function(mx){return[0,o,[36,mx]]});case 37:var Ax=f[1];return I0(h(n[1][1+v0],n),o,Ax,a,function(mx){return[0,o,[37,mx]]});case 38:var Vp=f[1];return I0(h(n[1][1+e1],n),o,Vp,a,function(mx){return[0,o,[38,mx]]});case 39:var gx=f[1];return I0(h(n[1][1+l],n),o,gx,a,function(mx){return[0,o,[39,mx]]});case 40:var oj=f[1];return I0(h(n[1][1+u],n),o,oj,a,function(mx){return[0,o,[40,mx]]});default:var vj=f[1];return I0(h(n[1][1+t],n),o,vj,a,function(mx){return[0,o,[41,mx]]})}},fn,function(n,a){return a},G,function(n){var a=h(n[1][1+S0],n);return function(f){return Rx(a,f)}},S0,function(n,a){var f=a[2],o=a[1],m=a[3],g=vr(h(n[1][1+fn],n),o),E=vr(h(n[1][1+fn],n),f);return o===g&&f===E?a:[0,g,E,m]},Mx,function(n,a){var f=a[2],o=a[1];switch(f[0]){case 0:var m=f[1];return I0(h(n[1][1+Kp],n),o,m,a,function(gx){return[0,o,[0,gx]]});case 1:var g=f[1];return I0(h(n[1][1+m5],n),o,g,a,function(gx){return[0,o,[1,gx]]});case 2:var E=f[1];return I0(h(n[1][1+k5],n),o,E,a,function(gx){return[0,o,[2,gx]]});case 3:var O=f[1];return I0(h(n[1][1+p5],n),o,O,a,function(gx){return[0,o,[3,gx]]});case 4:var R=f[1];return I0(h(n[1][1+zp],n),o,R,a,function(gx){return[0,o,[4,gx]]});case 5:var u0=f[1];return I0(h(n[1][1+l5],n),o,u0,a,function(gx){return[0,o,[5,gx]]});case 6:var l0=f[1];return I0(h(n[1][1+_3],n),o,l0,a,function(gx){return[0,o,[6,gx]]});case 7:var F0=f[1];return I0(h(n[1][1+Jo],n),o,F0,a,function(gx){return[0,o,[7,gx]]});case 8:var V0=f[1];return I0(h(n[1][1+Lo],n),o,V0,a,function(gx){return[0,o,[8,gx]]});case 9:var Cx=f[1];return I0(h(n[1][1+p1],n),o,Cx,a,function(gx){return[0,o,[9,gx]]});case 10:var jx=f[1];return A0(h(n[1][1+qx],n),jx,a,function(gx){return[0,o,[10,gx]]});case 11:var kr=f[1];return A0(k(n[1][1+pr],n,o),kr,a,function(gx){return[0,o,[11,gx]]});case 12:var Qr=f[1];return I0(h(n[1][1+Ro],n),o,Qr,a,function(gx){return[0,o,[12,gx]]});case 13:var Zr=f[1];return I0(h(n[1][1+Up],n),o,Zr,a,function(gx){return[0,o,[13,gx]]});case 14:var Wx=f[1];return I0(h(n[1][1+ex],n),o,Wx,a,function(gx){return[0,o,[14,gx]]});case 15:var P1=f[1];return I0(h(n[1][1+Xn],n),o,P1,a,function(gx){return[0,o,[15,gx]]});case 16:var e2=f[1];return I0(h(n[1][1+bt],n),o,e2,a,function(gx){return[0,o,[16,gx]]});case 17:var q2=f[1];return I0(h(n[1][1+J2],n),o,q2,a,function(gx){return[0,o,[17,gx]]});case 18:var Te=f[1];return I0(h(n[1][1+Jn],n),o,Te,a,function(gx){return[0,o,[18,gx]]});case 19:var Ee=f[1];return I0(h(n[1][1+b0],n),o,Ee,a,function(gx){return[0,o,[19,gx]]});case 20:var Je=f[1];return I0(h(n[1][1+Ds],n),o,Je,a,function(gx){return[0,o,[20,gx]]});case 21:var H2=f[1];return I0(h(n[1][1+Us],n),o,H2,a,function(gx){return[0,o,[21,gx]]});case 22:var Ae=f[1];return I0(h(n[1][1+qs],n),o,Ae,a,function(gx){return[0,o,[22,gx]]});case 23:var Se=f[1];return I0(h(n[1][1+fe],n),o,Se,a,function(gx){return[0,o,[23,gx]]});case 24:var cn=f[1];return I0(h(n[1][1+R2],n),o,cn,a,function(gx){return[0,o,[24,gx]]});case 25:var oe=f[1];return I0(h(n[1][1+js],n),o,oe,a,function(gx){return[0,o,[25,gx]]});case 26:var an=f[1];return I0(h(n[1][1+ie],n),o,an,a,function(gx){return[0,o,[26,gx]]});case 27:var sn=f[1];return A0(k(n[1][1+Dr],n,o),sn,a,function(gx){return[0,o,[27,gx]]});case 28:var Ha=f[1];return I0(h(n[1][1+jr],n),o,Ha,a,function(gx){return[0,o,[28,gx]]});case 29:var Va=f[1];return I0(h(n[1][1+K],n),o,Va,a,function(gx){return[0,o,[29,gx]]});case 30:var $a=f[1];return I0(h(n[1][1+ux],n),o,$a,a,function(gx){return[0,o,[30,gx]]});case 31:var zn=f[1];return I0(h(n[1][1+k0],n),o,zn,a,function(gx){return[0,o,[31,gx]]});case 32:var E3=f[1];return I0(h(n[1][1+G0],n),o,E3,a,function(gx){return[0,o,[32,gx]]});case 33:var Wa=f[1];return I0(h(n[1][1+U0],n),o,Wa,a,function(gx){return[0,o,[33,gx]]});case 34:var A3=f[1];return I0(h(n[1][1+x0],n),o,A3,a,function(gx){return[0,o,[34,gx]]});case 35:var S3=f[1];return I0(h(n[1][1+p0],n),o,S3,a,function(gx){return[0,o,[35,gx]]});case 36:var P3=f[1];return I0(h(n[1][1+T],n),o,P3,a,function(gx){return[0,o,[36,gx]]});case 37:var Ax=f[1];return I0(h(n[1][1+p],n),o,Ax,a,function(gx){return[0,o,[37,gx]]});default:var Vp=f[1];return I0(h(n[1][1+e],n),o,Vp,a,function(gx){return[0,o,[38,gx]]})}},Kp,function(n,a,f){var o=f[2],m=f[1],g=vr(h(n[1][1+T3],n),m),E=k(n[1][1+G],n,o);return m===g&&o===E?f:[0,g,E]},T3,function(n,a){switch(a[0]){case 0:var f=a[1];return A0(h(n[1][1+Mx],n),f,a,function(m){return[0,m]});case 1:var o=a[1];return A0(h(n[1][1+_x],n),o,a,function(m){return[1,m]});default:return a}},m5,function(n,a,f){return xx(n[1][1+T2],n,a,f)},k5,function(n,a,f){var o=f[2],m=f[1],g=k(n[1][1+Mx],n,m),E=k(n[1][1+G],n,o);return g===m&&E===o?f:[0,g,E]},p5,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+Mx],n,g),O=k(n[1][1+r0],n,m),R=k(n[1][1+G],n,o);return E===g&&O===m&&R===o?f:[0,E,O,R]},zp,function(n,a,f){var o=f[4],m=f[3],g=f[2],E=k(n[1][1+b3],n,g),O=k(n[1][1+Mx],n,m),R=k(n[1][1+G],n,o);return g===E&&m===O&&o===R?f:[0,f[1],E,O,R]},l5,function(n,a,f){var o=f[4],m=f[3],g=f[2],E=k(n[1][1+Mx],n,g),O=k(n[1][1+Mx],n,m),R=k(n[1][1+G],n,o);return g===E&&m===O&&o===R?f:[0,f[1],E,O,R]},Gn,function(n,a,f){var o=f[2],m=f[1],g=k(n[1][1+nx],n,m),E=k(n[1][1+G],n,o);return m===g&&o===E?f:[0,g,E]},Vl,function(n,a,f){var o=f[2],m=f[1],g=Rx(h(n[1][1+Bs],n),m),E=k(n[1][1+G],n,o);return m===g&&o===E?f:[0,g,E]},_3,function(n,a,f){var o=f[4],m=f[3],g=f[2],E=f[1],O=k(n[1][1+Mx],n,E),R=Rx(h(n[1][1+Ho],n),g),u0=k(n[1][1+Hp],n,m),l0=k(n[1][1+G],n,o);return E===O&&g===R&&m===u0&&o===l0?f:[0,O,R,u0,l0]},Hp,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=vr(h(n[1][1+Ct],n),m),O=k(n[1][1+G],n,o);return m===E&&o===O?a:[0,g,[0,E,O]]},Dr,function(n,a,f){var o=f[1],m=xx(n[1][1+_3],n,a,o);return o===m?f:[0,m,f[2],f[3]]},Ho,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=vr(h(n[1][1+Vo],n),m),O=k(n[1][1+G],n,o);return m===E&&o===O?a:[0,g,[0,E,O]]},Vo,function(n,a){if(a[0]===0){var f=a[1],o=k(n[1][1+o0],n,f);return o===f?a:[0,o]}var m=a[1],g=m[2][1],E=m[1],O=k(n[1][1+G],n,g);return g===O?a:[1,[0,E,[0,O]]]},g3,function(n,a){return Q1(h(n[1][1+Gn],n),a)},y3,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=Rx(h(n[1][1+Ye],n),g),O=k(n[1][1+g3],n,m),R=k(n[1][1+G],n,o);return g===E&&m===O&&o===R?f:[0,E,O,R]},Ko,function(n,a,f){return xx(n[1][1+Ka],n,a,f)},Jo,function(n,a,f){return xx(n[1][1+Ka],n,a,f)},Ka,function(n,a,f){var o=f[7],m=f[6],g=f[5],E=f[4],O=f[3],R=f[2],u0=f[1],l0=Rx(h(n[1][1+Un],n),u0),F0=Rx(k(n[1][1+L],n,0),O),V0=k(n[1][1+Dt],n,R),Cx=h(n[1][1+Yo],n),jx=Rx(function(Wx){return Q1(Cx,Wx)},E),kr=Rx(h(n[1][1+h3],n),g),Qr=vr(h(n[1][1+Bn],n),m),Zr=k(n[1][1+G],n,o);return u0===l0&&R===V0&&E===jx&&g===kr&&m===Qr&&o===Zr&&O===F0?f:[0,l0,V0,F0,jx,kr,Qr,Zr]},Yo,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+Mx],n,g),O=Rx(h(n[1][1+i0],n),m),R=k(n[1][1+G],n,o);return g===E&&m===O&&o===R?f:[0,E,O,R]},Un,function(n,a){return xx(n[1][1+X0],n,R$,a)},Dt,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=vr(h(n[1][1+zo],n),m),O=k(n[1][1+G],n,o);return m===E&&o===O?a:[0,g,[0,E,O]]},Bn,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+Mx],n,m),O=k(n[1][1+G],n,o);return m===E&&o===O?a:[0,g,[0,E,O]]},zo,function(n,a){switch(a[0]){case 0:var f=a[1],o=f[1],m=f[2];return I0(h(n[1][1+Hl],n),o,m,a,function(jx){return[0,[0,o,jx]]});case 1:var g=a[1],E=g[1],O=g[2];return I0(h(n[1][1+zl],n),E,O,a,function(jx){return[1,[0,E,jx]]});case 2:var R=a[1],u0=R[1],l0=R[2];return I0(h(n[1][1+Kl],n),u0,l0,a,function(jx){return[2,[0,u0,jx]]});default:var F0=a[1],V0=F0[1],Cx=F0[2];return I0(h(n[1][1+Xo],n),V0,Cx,a,function(jx){return[3,[0,V0,jx]]})}},h3,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=vr(h(n[1][1+Go],n),m),O=k(n[1][1+G],n,o);return m===E&&o===O?a:[0,g,[0,E,O]]},Go,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+q],n,m),O=Rx(h(n[1][1+i0],n),o);return m===E&&o===O?a:[0,g,[0,E,O]]},Hl,function(n,a,f){var o=f[6],m=f[5],g=f[3],E=f[2],O=k(n[1][1+Nx],n,E),R=Q1(h(n[1][1+Xr],n),g),u0=vr(h(n[1][1+Bn],n),m),l0=k(n[1][1+G],n,o);return E===O&&g===R&&m===u0&&o===l0?f:[0,f[1],O,R,f[4],u0,l0]},zl,function(n,a,f){var o=f[7],m=f[6],g=f[5],E=f[3],O=f[2],R=f[1],u0=k(n[1][1+Nx],n,R),l0=k(n[1][1+Ws],n,O),F0=k(n[1][1+f0],n,E),V0=k(n[1][1+i],n,g),Cx=vr(h(n[1][1+Bn],n),m),jx=k(n[1][1+G],n,o);return R===u0&&O===l0&&F0===E&&V0===g&&Cx===m&&jx===o?f:[0,u0,l0,F0,f[4],V0,Cx,jx]},Ws,function(n,a){if(typeof a=="number")return a;var f=a[1],o=k(n[1][1+Mx],n,f);return f===o?a:[0,o]},Kl,function(n,a,f){var o=f[7],m=f[6],g=f[5],E=f[3],O=f[2],R=f[1],u0=k(n[1][1+E0],n,R),l0=k(n[1][1+Ws],n,O),F0=k(n[1][1+f0],n,E),V0=k(n[1][1+i],n,g),Cx=vr(h(n[1][1+Bn],n),m),jx=k(n[1][1+G],n,o);return R===u0&&O===l0&&F0===E&&V0===g&&Cx===m&&jx===o?f:[0,u0,l0,F0,f[4],V0,Cx,jx]},Xo,function(n,a,f){var o=f[2],m=f[1],g=k(n[1][1+nx],n,m),E=k(n[1][1+G],n,o);return m===g&&o===E?f:[0,g,E]},Ot,function(n,a){return Rx(h(n[1][1+Mx],n),a)},Bo,function(n,a,f){var o=f[6],m=f[5],g=f[4],E=f[3],O=f[2],R=f[1],u0=f[7],l0=k(n[1][1+d3],n,R),F0=Rx(k(n[1][1+L],n,8),O),V0=k(n[1][1+Yl],n,E),Cx=k(n[1][1+Jl],n,m),jx=k(n[1][1+jt],n,g),kr=k(n[1][1+G],n,o);return R===l0&&O===F0&&E===V0&&m===Cx&&g===jx&&o===kr?f:[0,l0,F0,V0,jx,Cx,kr,u0]},d3,function(n,a){return xx(n[1][1+X0],n,M$,a)},Yl,function(n,a){var f=a[2],o=f[3],m=f[2],g=f[1],E=a[1],O=vr(h(n[1][1+m3],n),g),R=Rx(h(n[1][1+k3],n),m),u0=k(n[1][1+G],n,o);return g===O&&m===R&&o===u0?a:[0,E,[0,O,R,u0]]},m3,function(n,a){var f=a[2],o=f[3],m=f[2],g=f[1],E=f[4],O=a[1],R=k(n[1][1+$s],n,g),u0=k(n[1][1+Vs],n,m),l0=k(n[1][1+Ot],n,o);return g===R&&m===u0&&o===l0?a:[0,O,[0,R,u0,l0,E]]},$s,function(n,a){if(a[0]===0){var f=a[1];return A0(h(n[1][1+qx],n),f,a,function(E){return[0,E]})}var o=a[1],m=o[1],g=o[2];return I0(h(n[1][1+ex],n),m,g,a,function(E){return[1,[0,m,E]]})},Vs,function(n,a){return xx(n[1][1+w3],n,L$,a)},k3,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+Vs],n,m),O=k(n[1][1+G],n,o);return m===E&&o===O?a:[0,g,[0,E,O]]},Jl,function(n,a){var f=a[1],o=a[2];return I0(h(n[1][1+Gn],n),f,o,a,function(m){return[0,f,m]})},Lo,function(n,a,f){var o=f[4],m=f[3],g=f[2],E=f[1],O=k(n[1][1+$0],n,E),R=k(n[1][1+Mx],n,g),u0=k(n[1][1+Mx],n,m),l0=k(n[1][1+G],n,o);return E===O&&g===R&&m===u0&&o===l0?f:[0,O,R,u0,l0]},Xl,function(n,a,f){var o=f[2],m=f[1],g=Rx(h(n[1][1+Bs],n),m),E=k(n[1][1+G],n,o);return m===g&&o===E?f:[0,g,E]},Bl,function(n,a,f){var o=f[1],m=k(n[1][1+G],n,o);return o===m?f:[0,m]},Ks,function(n,a,f){var o=f[7],m=f[6],g=f[5],E=f[4],O=f[3],R=f[2],u0=f[1],l0=k(n[1][1+Un],n,u0),F0=Rx(k(n[1][1+L],n,3),R),V0=Q1(h(n[1][1+vx],n),O),Cx=h(n[1][1+Ur],n),jx=Rx(function(P1){return Q1(Cx,P1)},E),kr=h(n[1][1+Ur],n),Qr=vr(function(P1){return Q1(kr,P1)},g),Zr=Rx(h(n[1][1+h3],n),m),Wx=k(n[1][1+G],n,o);return l0===u0&&F0===R&&V0===O&&jx===E&&Qr===g&&Zr===m&&Wx===o?f:[0,l0,F0,V0,jx,Qr,Zr,Wx]},v3,function(n,a,f){var o=f[5],m=f[4],g=f[3],E=f[2],O=f[1],R=k(n[1][1+d3],n,O),u0=Rx(k(n[1][1+L],n,4),E),l0=k(n[1][1+Hs],n,g),F0=k(n[1][1+jt],n,m),V0=k(n[1][1+G],n,o);return O===R&&E===u0&&g===l0&&m===F0&&o===V0?f:[0,R,u0,l0,F0,V0]},Uo,function(n,a,f){var o=f[4],m=f[3],g=f[2],E=f[1],O=Rx(k(n[1][1+L],n,9),E),R=k(n[1][1+Hs],n,g),u0=k(n[1][1+jt],n,m),l0=k(n[1][1+G],n,o);return E===O&&g===R&&m===u0&&o===l0?f:[0,O,R,u0,l0]},Hs,function(n,a){var f=a[2],o=f[3],m=f[2],g=f[1],E=a[1],O=vr(h(n[1][1+qo],n),g),R=Rx(h(n[1][1+Gl],n),m),u0=k(n[1][1+G],n,o);return g===O&&m===R&&o===u0?a:[0,E,[0,O,R,u0]]},qo,function(n,a){var f=a[2],o=f[2],m=f[1],g=f[3],E=a[1],O=k(n[1][1+$s],n,m),R=k(n[1][1+r0],n,o);return m===O&&o===R?a:[0,E,[0,O,R,g]]},Gl,function(n,a){var f=a[2],o=f[4],m=f[2],g=f[1],E=f[3],O=a[1],R=Rx(h(n[1][1+qx],n),g),u0=k(n[1][1+o0],n,m),l0=k(n[1][1+G],n,o);return g===R&&m===u0&&o===l0?a:[0,O,[0,R,u0,E,l0]]},zs,function(n,a,f){return xx(n[1][1+K2],n,a,f)},Mo,function(n,a,f){var o=f[5],m=f[4],g=f[3],E=f[2],O=f[1],R=sp(h(n[1][1+r2],n),m),u0=Rx(h(n[1][1+we],n),g),l0=Rx(h(n[1][1+o3],n),E),F0=k(n[1][1+G],n,o);return m===R&&g===u0&&E===l0&&o===F0?f:[0,O,l0,u0,R,F0]},o3,function(n,a){switch(a[0]){case 0:var f=a[1],o=f[2],m=f[1],g=xx(n[1][1+nn],n,m,o);return g===o?a:[0,[0,m,g]];case 1:var E=a[1],O=E[2],R=E[1],u0=xx(n[1][1+Js],n,R,O);return u0===O?a:[1,[0,R,u0]];case 2:var l0=a[1],F0=l0[2],V0=l0[1],Cx=xx(n[1][1+Ks],n,V0,F0);return Cx===F0?a:[2,[0,V0,Cx]];case 3:var jx=a[1],kr=jx[2],Qr=jx[1],Zr=xx(n[1][1+v3],n,Qr,kr);return Zr===kr?a:[3,[0,Qr,Zr]];case 4:var Wx=a[1],P1=k(n[1][1+o0],n,Wx);return P1===Wx?a:[4,P1];case 5:var e2=a[1],q2=e2[2],Te=e2[1],Ee=xx(n[1][1+v0],n,Te,q2);return Ee===q2?a:[5,[0,Te,Ee]];case 6:var Je=a[1],H2=Je[2],Ae=Je[1],Se=xx(n[1][1+e1],n,Ae,H2);return Se===H2?a:[6,[0,Ae,Se]];case 7:var cn=a[1],oe=cn[2],an=cn[1],sn=xx(n[1][1+d0],n,an,oe);return sn===oe?a:[7,[0,an,sn]];default:var Ha=a[1],Va=Ha[2],$a=Ha[1],zn=xx(n[1][1+K2],n,$a,Va);return zn===Va?a:[8,[0,$a,zn]]}},Js,function(n,a,f){var o=f[4],m=f[3],g=f[2],E=f[1],O=k(n[1][1+Or],n,E),R=k(n[1][1+r0],n,g),u0=Rx(h(n[1][1+z],n),m),l0=k(n[1][1+G],n,o);return O===E&&R===g&&u0===m&&l0===o?f:[0,O,R,u0,l0]},za,function(n,a,f){return xx(n[1][1+d0],n,a,f)},Ul,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=Q1(h(n[1][1+Gn],n),m),O=k(n[1][1+G],n,o);return E===m&&o===O?f:[0,g,E,O]},Ys,function(n,a,f){var o=f[2],m=f[1],g=k(n[1][1+r0],n,m),E=k(n[1][1+G],n,o);return m===g&&o===E?f:[0,g,E]},qn,function(n,a,f){var o=f[3],m=f[2],g=f[1];if(g[0]===0)var E=g[1],O=k(n[1][1+qx],n,E),R=E===O?g:[0,O],V0=R;else var u0=g[1],l0=xx(n[1][1+X0],n,q$,u0),F0=u0===l0?g:[1,l0],V0=F0;var Cx=Q1(h(n[1][1+Gn],n),m),jx=k(n[1][1+G],n,o);return V0===g&&Cx===m&&o===jx?f:[0,V0,Cx,jx]},un,function(n,a,f){return xx(n[1][1+v0],n,a,f)},nn,function(n,a,f){var o=f[4],m=f[3],g=f[2],E=f[1],O=xx(n[1][1+X0],n,[0,m],E),R=k(n[1][1+r0],n,g),u0=k(n[1][1+G],n,o);return O===E&&R===g&&u0===o?f:[0,O,R,m,u0]},Ja,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+D0],n,g),O=k(n[1][1+$0],n,m),R=k(n[1][1+G],n,o);return g===E&&m===O&&o===R?f:[0,E,O,R]},Ge,function(n,a,f){var o=f[1],m=k(n[1][1+G],n,o);return o===m?f:[0,m]},K2,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=xx(n[1][1+X0],n,U$,g),O=k(n[1][1+Ga],n,m),R=k(n[1][1+G],n,o);return g===E&&m===O&&o===R?f:[0,E,O,R]},Ga,function(n,a){var f=a[2],o=a[1];switch(f[0]){case 0:var m=f[1];return A0(h(n[1][1+tn],n),m,a,function(u0){return[0,o,[0,u0]]});case 1:var g=f[1];return A0(h(n[1][1+Nt],n),g,a,function(u0){return[0,o,[1,u0]]});case 2:var E=f[1];return A0(h(n[1][1+rn],n),E,a,function(u0){return[0,o,[2,u0]]});case 3:var O=f[1];return A0(h(n[1][1+ae],n),O,a,function(u0){return[0,o,[3,u0]]});default:var R=f[1];return A0(h(n[1][1+Ya],n),R,a,function(u0){return[0,o,[4,u0]]})}},tn,function(n,a){var f=a[4],o=a[1],m=vr(h(n[1][1+Xe],n),o),g=k(n[1][1+G],n,f);return o===m&&f===g?a:[0,m,a[2],a[3],g]},Nt,function(n,a){var f=a[4],o=a[1],m=vr(h(n[1][1+Be],n),o),g=k(n[1][1+G],n,f);return o===m&&f===g?a:[0,m,a[2],a[3],g]},rn,function(n,a){var f=a[4],o=a[1];if(o[0]===0)var m=o[1],g=h(n[1][1+en],n),R=A0(function(l0){return vr(g,l0)},m,o,function(l0){return[0,l0]});else var E=o[1],O=h(n[1][1+se],n),R=A0(function(l0){return vr(O,l0)},E,o,function(l0){return[1,l0]});var u0=k(n[1][1+G],n,f);return o===R&&f===u0?a:[0,R,a[2],a[3],u0]},ae,function(n,a){var f=a[3],o=a[1],m=vr(h(n[1][1+en],n),o),g=k(n[1][1+G],n,f);return o===m&&f===g?a:[0,m,a[2],g]},Ya,function(n,a){var f=a[4],o=a[1],m=vr(h(n[1][1+Ln],n),o),g=k(n[1][1+G],n,f);return o===m&&f===g?a:[0,m,a[2],a[3],g]},en,function(n,a){var f=a[2][1],o=a[1],m=k(n[1][1+L2],n,f);return f===m?a:[0,o,[0,m]]},Xe,function(n,a){var f=a[2],o=f[1],m=f[2],g=a[1],E=k(n[1][1+L2],n,o);return o===E?a:[0,g,[0,E,m]]},Be,function(n,a){var f=a[2],o=f[1],m=f[2],g=a[1],E=k(n[1][1+L2],n,o);return o===E?a:[0,g,[0,E,m]]},se,function(n,a){var f=a[2],o=f[1],m=f[2],g=a[1],E=k(n[1][1+L2],n,o);return o===E?a:[0,g,[0,E,m]]},Ln,function(n,a){var f=a[2],o=f[1],m=f[2],g=a[1],E=k(n[1][1+L2],n,o);return o===E?a:[0,g,[0,E,m]]},L2,function(n,a){return k(n[1][1+qx],n,a)},vt,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+ot],n,m),O=k(n[1][1+G],n,o);return E===m&&O===o?f:[0,g,E,O]},ot,function(n,a){if(a[0]===0){var f=a[1];return A0(h(n[1][1+D0],n),f,a,function(m){return[0,m]})}var o=a[1];return A0(h(n[1][1+Mx],n),o,a,function(m){return[1,m]})},be,function(n,a,f){var o=f[5],m=f[3],g=f[2],E=f[1],O=f[4],R=sp(h(n[1][1+r2],n),m),u0=Rx(h(n[1][1+we],n),g),l0=Rx(h(n[1][1+D0],n),E),F0=k(n[1][1+G],n,o);return m===R&&g===u0&&E===l0&&o===F0?f:[0,l0,u0,R,O,F0]},Ue,function(n,a){var f=a[2],o=f[2],m=f[1],g=f[4],E=f[3],O=a[1],R=k(n[1][1+qx],n,m),u0=Rx(h(n[1][1+qx],n),o);return m===R&&o===u0?a:[0,O,[0,R,u0,E,g]]},p2,function(n,a){var f=a[2],o=a[1],m=Rx(h(n[1][1+qx],n),f);return f===m?a:[0,o,m]},we,function(n,a){if(a[0]===0){var f=a[1],o=vr(h(n[1][1+Ue],n),f);return f===o?a:[0,o]}var m=a[1],g=k(n[1][1+p2],n,m);return m===g?a:[1,g]},r2,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+G],n,o);return o===E?f:[0,g,m,E]},It,function(n,a,f){var o=f[3],m=f[1],g=f[2],E=k(n[1][1+Mx],n,m),O=k(n[1][1+G],n,o);return m===E&&o===O?f:[0,E,g,O]},Ct,function(n,a){if(a[0]===0){var f=a[1];return A0(h(n[1][1+Mx],n),f,a,function(m){return[0,m]})}var o=a[1];return A0(h(n[1][1+_x],n),o,a,function(m){return[1,m]})},xn,function(n,a,f){var o=f[5],m=f[3],g=f[2],E=f[1],O=f[4],R=k(n[1][1+Pt],n,E),u0=k(n[1][1+Mx],n,g),l0=k(n[1][1+D0],n,m),F0=k(n[1][1+G],n,o);return E===R&&g===u0&&m===l0&&o===F0?f:[0,R,u0,l0,O,F0]},Pt,function(n,a){if(a[0]===0){var f=a[1];return A0(h(n[1][1+at],n),f,a,function(m){return[0,m]})}var o=a[1];return A0(h(n[1][1+st],n),o,a,function(m){return[1,m]})},at,function(n,a){var f=a[1],o=a[2];return I0(h(n[1][1+l],n),f,o,a,function(m){return[0,f,m]})},ce,function(n,a,f){var o=f[5],m=f[3],g=f[2],E=f[1],O=f[4],R=k(n[1][1+i1],n,E),u0=k(n[1][1+Mx],n,g),l0=k(n[1][1+D0],n,m),F0=k(n[1][1+G],n,o);return E===R&&g===u0&&m===l0&&o===F0?f:[0,R,u0,l0,O,F0]},i1,function(n,a){if(a[0]===0){var f=a[1];return A0(h(n[1][1+L1],n),f,a,function(m){return[0,m]})}var o=a[1];return A0(h(n[1][1+St],n),o,a,function(m){return[1,m]})},L1,function(n,a){var f=a[1],o=a[2];return I0(h(n[1][1+l],n),f,o,a,function(m){return[0,f,m]})},z2,function(n,a,f){var o=f[5],m=f[4],g=f[3],E=f[2],O=f[1],R=Rx(h(n[1][1+_e],n),O),u0=Rx(h(n[1][1+$0],n),E),l0=Rx(h(n[1][1+Mx],n),g),F0=k(n[1][1+D0],n,m),V0=k(n[1][1+G],n,o);return O===R&&E===u0&&g===l0&&m===F0&&o===V0?f:[0,R,u0,l0,F0,V0]},_e,function(n,a){if(a[0]===0){var f=a[1];return A0(h(n[1][1+M2],n),f,a,function(m){return[0,m]})}var o=a[1];return A0(h(n[1][1+Mx],n),o,a,function(m){return[1,m]})},M2,function(n,a){var f=a[1],o=a[2];return I0(h(n[1][1+l],n),f,o,a,function(m){return[0,f,m]})},Br,function(n,a){var f=a[2],o=f[2],m=f[1],g=f[3],E=a[1],O=k(n[1][1+o0],n,o),R=Rx(h(n[1][1+qx],n),m);return O===o&&R===m?a:[0,E,[0,R,O,g]]},Ir,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+Br],n,m),O=k(n[1][1+G],n,o);return E===m&&O===o?a:[0,g,[0,E,O]]},Lr,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+r0],n,m),O=k(n[1][1+G],n,o);return E===m&&O===o?a:[0,g,[0,E,O]]},h1,function(n,a){if(a[0]===0){var f=a[1];return A0(h(n[1][1+o0],n),f,a,function(m){return[0,m]})}var o=a[1];return A0(h(n[1][1+W],n),o,a,function(m){return[1,m]})},y1,function(n,a,f){var o=f[4],m=f[3],g=f[2],E=g[2],O=E[4],R=E[3],u0=E[2],l0=E[1],F0=f[1],V0=f[5],Cx=g[1],jx=Rx(k(n[1][1+L],n,10),F0),kr=Rx(h(n[1][1+Lr],n),l0),Qr=vr(h(n[1][1+Br],n),u0),Zr=Rx(h(n[1][1+Ir],n),R),Wx=k(n[1][1+h1],n,m),P1=k(n[1][1+G],n,o),e2=k(n[1][1+G],n,O);return Qr===u0&&Zr===R&&Wx===m&&jx===F0&&P1===o&&e2===O&&kr===l0?f:[0,jx,[0,Cx,[0,kr,Qr,Zr,e2]],Wx,P1,V0]},Bs,function(n,a){return k(n[1][1+qx],n,a)},lx,function(n,a){switch(a[0]){case 0:var f=a[1];return A0(h(n[1][1+o0],n),f,a,function(g){return[0,g]});case 1:var o=a[1];return A0(h(n[1][1+_],n),o,a,function(g){return[1,g]});default:var m=a[1];return A0(h(n[1][1+Ex],n),m,a,function(g){return[2,g]})}},_,function(n,a){var f=a[1],o=a[2];return I0(h(n[1][1+y1],n),f,o,a,function(m){return[0,f,m]})},Ex,function(n,a){var f=a[1],o=a[2];return I0(h(n[1][1+y1],n),f,o,a,function(m){return[0,f,m]})},Px,function(n,a){var f=a[2],o=f[8],m=f[7],g=f[2],E=f[1],O=f[6],R=f[5],u0=f[4],l0=f[3],F0=a[1],V0=k(n[1][1+Nx],n,E),Cx=k(n[1][1+lx],n,g),jx=k(n[1][1+i],n,m),kr=k(n[1][1+G],n,o);return V0===E&&Cx===g&&jx===m&&kr===o?a:[0,F0,[0,V0,Cx,l0,u0,R,O,jx,kr]]},L0,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+o0],n,m),O=k(n[1][1+G],n,o);return E===m&&o===O?a:[0,g,[0,E,O]]},Vr,function(n,a){var f=a[2],o=f[6],m=f[5],g=f[3],E=f[2],O=f[4],R=f[1],u0=a[1],l0=k(n[1][1+o0],n,E),F0=k(n[1][1+o0],n,g),V0=k(n[1][1+i],n,m),Cx=k(n[1][1+G],n,o);return l0===E&&F0===g&&V0===m&&Cx===o?a:[0,u0,[0,R,l0,F0,O,V0,Cx]]},rr,function(n,a){var f=a[2],o=f[6],m=f[2],g=f[1],E=f[5],O=f[4],R=f[3],u0=a[1],l0=k(n[1][1+qx],n,g),F0=k(n[1][1+o0],n,m),V0=k(n[1][1+G],n,o);return g===l0&&m===F0&&o===V0?a:[0,u0,[0,l0,F0,R,O,E,V0]]},J1,function(n,a){var f=a[2],o=f[3],m=f[1],g=m[2],E=m[1],O=f[2],R=a[1],u0=xx(n[1][1+y1],n,E,g),l0=k(n[1][1+G],n,o);return g===u0&&o===l0?a:[0,R,[0,[0,E,u0],O,l0]]},Hx,function(n,a){var f=a[2],o=f[6],m=f[4],g=f[3],E=f[2],O=f[1],R=f[5],u0=a[1],l0=xx(n[1][1+X],n,12,O),F0=k(n[1][1+o0],n,E),V0=k(n[1][1+o0],n,g),Cx=k(n[1][1+i],n,m),jx=k(n[1][1+G],n,o);return l0===O&&F0===E&&V0===g&&Cx===m&&jx===o?a:[0,u0,[0,l0,F0,V0,Cx,R,jx]]},vx,function(n,a,f){var o=f[4],m=f[3],g=f[2],E=f[1],O=vr(h(n[1][1+$],n),m),R=k(n[1][1+G],n,o);return O===m&&o===R?f:[0,E,g,O,R]},$,function(n,a){switch(a[0]){case 0:var f=a[1];return A0(h(n[1][1+Px],n),f,a,function(R){return[0,R]});case 1:var o=a[1];return A0(h(n[1][1+L0],n),o,a,function(R){return[1,R]});case 2:var m=a[1];return A0(h(n[1][1+Vr],n),m,a,function(R){return[2,R]});case 3:var g=a[1];return A0(h(n[1][1+J1],n),g,a,function(R){return[3,R]});case 4:var E=a[1];return A0(h(n[1][1+rr],n),E,a,function(R){return[4,R]});default:var O=a[1];return A0(h(n[1][1+Hx],n),O,a,function(R){return[5,R]})}},D,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=h(n[1][1+Ur],n),O=vr(function(l0){return Q1(E,l0)},m),R=Q1(h(n[1][1+vx],n),g),u0=k(n[1][1+G],n,o);return O===m&&R===g&&o===u0?f:[0,R,O,u0]},t1,function(n,a){if(a[0]===0){var f=a[1];return A0(h(n[1][1+q],n),f,a,function(m){return[0,m]})}var o=a[1];return A0(h(n[1][1+Pr],n),o,a,function(m){return[1,m]})},Pr,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+t1],n,m),O=k(n[1][1+w2],n,o);return E===m&&O===o?a:[0,g,[0,E,O]]},w2,function(n,a){return k(n[1][1+qx],n,a)},c,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+G],n,o);return o===E?a:[0,g,[0,m,E]]},i,function(n,a){return Rx(h(n[1][1+c],n),a)},j0,function(n,a){var f=a[2],o=a[1],m=k(n[1][1+G],n,f);return f===m?a:[0,o,m]},i0,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=vr(h(n[1][1+o0],n),m),O=k(n[1][1+G],n,o);return m===E&&o===O?a:[0,g,[0,E,O]]},L,function(n,a,f){var o=f[2],m=o[2],g=o[1],E=f[1],O=vr(k(n[1][1+X],n,a),g),R=k(n[1][1+G],n,m);return O===g&&R===m?f:[0,E,[0,O,R]]},X,function(n,a,f){var o=f[2],m=o[6],g=o[5],E=o[4],O=o[2],R=o[1],u0=o[3],l0=f[1],F0=k(n[1][1+f0],n,O),V0=k(n[1][1+i],n,E),Cx=Rx(h(n[1][1+o0],n),g),jx=Rx(h(n[1][1+j0],n),m),kr=k(n[1][1+Yn],n,R);return kr===R&&F0===O&&V0===E&&Cx===g&&jx===m?f:[0,l0,[0,kr,F0,u0,V0,Cx,jx]]},Ur,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+t1],n,g),O=Rx(h(n[1][1+i0],n),m),R=k(n[1][1+G],n,o);return E===g&&O===m&&R===o?f:[0,E,O,R]},rx,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+o0],n,g),O=k(n[1][1+o0],n,m),R=k(n[1][1+G],n,o);return E===g&&O===m&&R===o?f:[0,E,O,R]},c1,function(n,a,f){var o=f[1],m=f[2],g=xx(n[1][1+rx],n,a,o);return g===o?f:[0,g,m]},ex,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+G],n,o);return o===E?f:[0,g,m,E]},J2,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+G],n,o);return o===E?f:[0,g,m,E]},Jn,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+G],n,o);return o===E?f:[0,g,m,E]},Xn,function(n,a,f){var o=f[2],m=f[1],g=k(n[1][1+G],n,o);return o===g?f:[0,m,g]},bt,function(n,a,f){return k(n[1][1+G],n,f)},b0,function(n,a,f){var o=f[4],m=f[3],g=f[2],E=f[1],O=k(n[1][1+G],n,o);return o===O?f:[0,E,g,m,O]},Ds,function(n,a,f){var o=f[6],m=f[5],g=f[4],E=f[3],O=f[2],R=f[1];return o===k(n[1][1+G],n,o)?f:[0,R,O,E,g,m,o]},ft,function(n,a){var f=a[2],o=a[1],m=k(n[1][1+o0],n,o),g=k(n[1][1+G],n,f);return o===m&&f===g?a:[0,m,g]},l3,function(n,a){var f=a[5],o=a[4],m=a[3],g=a[2],E=a[1],O=k(n[1][1+o0],n,E),R=k(n[1][1+o0],n,g),u0=k(n[1][1+o0],n,m),l0=k(n[1][1+o0],n,o),F0=k(n[1][1+G],n,f);return E===O&&g===R&&m===u0&&o===l0&&f===F0?a:[0,O,R,u0,l0,F0]},O0,function(n,a){var f=a[2],o=a[1],m=xx(n[1][1+X],n,11,o),g=k(n[1][1+G],n,f);return o===m&&f===g?a:[0,m,g]},b,function(n,a){var f=a[3],o=a[2],m=a[1],g=k(n[1][1+F],n,m),E=Rx(h(n[1][1+i0],n),o),O=k(n[1][1+G],n,f);return m===g&&il(o,E)&&f===O?a:[0,g,E,O]},F,function(n,a){if(a[0]===0){var f=a[1];return A0(h(n[1][1+I],n),f,a,function(m){return[0,m]})}var o=a[1];return A0(h(n[1][1+C],n),o,a,function(m){return[1,m]})},I,function(n,a){return k(n[1][1+qx],n,a)},N,function(n,a){return k(n[1][1+qx],n,a)},C,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+F],n,m),O=k(n[1][1+N],n,o);return E===m&&O===o?a:[0,g,[0,E,O]]},jo,function(n,a){var f=a[2],o=a[1],m=k(n[1][1+o0],n,o),g=k(n[1][1+G],n,f);return o===m&&f===g?a:[0,m,g]},U,function(n,a){var f=a[3],o=a[2],m=a[4],g=a[1],E=k(n[1][1+o0],n,o),O=k(n[1][1+G],n,f);return o===E&&f===O?a:[0,g,E,O,m]},y0,function(n,a){var f=a[2],o=a[1],m=k(n[1][1+o0],n,o),g=k(n[1][1+G],n,f);return o===m&&f===g?a:[0,m,g]},w0,function(n,a){var f=a[3],o=a[1],m=a[2],g=vr(h(n[1][1+h0],n),o),E=k(n[1][1+G],n,f);return o===g&&f===E?a:[0,g,m,E]},h0,function(n,a){var f=a[2],o=a[1];switch(f[0]){case 0:var m=f[1];return A0(h(n[1][1+o0],n),m,a,function(O){return[0,o,[0,O]]});case 1:var g=f[1];return A0(h(n[1][1+s0],n),g,a,function(O){return[0,o,[1,O]]});default:var E=f[1];return A0(h(n[1][1+t0],n),E,a,function(O){return[0,o,[2,O]]})}},s0,function(n,a){var f=a[3],o=a[2],m=a[4],g=a[1],E=k(n[1][1+o0],n,o),O=k(n[1][1+i],n,f);return E===o&&O===f?a:[0,g,E,O,m]},t0,function(n,a){var f=a[2],o=a[1],m=k(n[1][1+o0],n,f);return m===f?a:[0,o,m]},d5,function(n,a){var f=a[2],o=a[1],m=k(n[1][1+o0],n,o),g=k(n[1][1+G],n,f);return o===m&&f===g?a:[0,m,g]},d,function(n,a,f){var o=f[2],m=f[1],g=m[3],E=m[2],O=m[1],R=k(n[1][1+o0],n,O),u0=k(n[1][1+o0],n,E),l0=vr(h(n[1][1+o0],n),g),F0=k(n[1][1+G],n,o);return R===O&&u0===E&&l0===g&&F0===o?f:[0,[0,R,u0,l0],F0]},S,function(n,a,f){var o=f[2],m=f[1],g=m[3],E=m[2],O=m[1],R=k(n[1][1+o0],n,O),u0=k(n[1][1+o0],n,E),l0=vr(h(n[1][1+o0],n),g),F0=k(n[1][1+G],n,o);return R===O&&u0===E&&l0===g&&F0===o?f:[0,[0,R,u0,l0],F0]},o0,function(n,a){var f=a[2],o=a[1];switch(f[0]){case 0:var m=f[1];return A0(h(n[1][1+G],n),m,a,function(Ax){return[0,o,[0,Ax]]});case 1:var g=f[1];return A0(h(n[1][1+G],n),g,a,function(Ax){return[0,o,[1,Ax]]});case 2:var E=f[1];return A0(h(n[1][1+G],n),E,a,function(Ax){return[0,o,[2,Ax]]});case 3:var O=f[1];return A0(h(n[1][1+G],n),O,a,function(Ax){return[0,o,[3,Ax]]});case 4:var R=f[1];return A0(h(n[1][1+G],n),R,a,function(Ax){return[0,o,[4,Ax]]});case 5:var u0=f[1];return A0(h(n[1][1+G],n),u0,a,function(Ax){return[0,o,[5,Ax]]});case 6:var l0=f[1];return A0(h(n[1][1+G],n),l0,a,function(Ax){return[0,o,[6,Ax]]});case 7:var F0=f[1];return A0(h(n[1][1+G],n),F0,a,function(Ax){return[0,o,[7,Ax]]});case 8:var V0=f[1],Cx=f[2];return A0(h(n[1][1+G],n),Cx,a,function(Ax){return[0,o,[8,V0,Ax]]});case 9:var jx=f[1];return A0(h(n[1][1+G],n),jx,a,function(Ax){return[0,o,[9,Ax]]});case 10:var kr=f[1];return A0(h(n[1][1+G],n),kr,a,function(Ax){return[0,o,[10,Ax]]});case 11:var Qr=f[1];return A0(h(n[1][1+ft],n),Qr,a,function(Ax){return[0,o,[11,Ax]]});case 12:var Zr=f[1];return I0(h(n[1][1+y1],n),o,Zr,a,function(Ax){return[0,o,[12,Ax]]});case 13:var Wx=f[1];return I0(h(n[1][1+Uo],n),o,Wx,a,function(Ax){return[0,o,[13,Ax]]});case 14:var P1=f[1];return I0(h(n[1][1+vx],n),o,P1,a,function(Ax){return[0,o,[14,Ax]]});case 15:var e2=f[1];return I0(h(n[1][1+D],n),o,e2,a,function(Ax){return[0,o,[15,Ax]]});case 16:var q2=f[1];return A0(h(n[1][1+d5],n),q2,a,function(Ax){return[0,o,[16,Ax]]});case 17:var Te=f[1];return A0(h(n[1][1+l3],n),Te,a,function(Ax){return[0,o,[17,Ax]]});case 18:var Ee=f[1];return A0(h(n[1][1+O0],n),Ee,a,function(Ax){return[0,o,[18,Ax]]});case 19:var Je=f[1];return I0(h(n[1][1+Ur],n),o,Je,a,function(Ax){return[0,o,[19,Ax]]});case 20:var H2=f[1];return I0(h(n[1][1+rx],n),o,H2,a,function(Ax){return[0,o,[20,Ax]]});case 21:var Ae=f[1];return I0(h(n[1][1+c1],n),o,Ae,a,function(Ax){return[0,o,[21,Ax]]});case 22:var Se=f[1];return I0(h(n[1][1+d],n),o,Se,a,function(Ax){return[0,o,[22,Ax]]});case 23:var cn=f[1];return I0(h(n[1][1+S],n),o,cn,a,function(Ax){return[0,o,[23,Ax]]});case 24:var oe=f[1];return A0(h(n[1][1+b],n),oe,a,function(Ax){return[0,o,[24,Ax]]});case 25:var an=f[1];return A0(h(n[1][1+jo],n),an,a,function(Ax){return[0,o,[25,Ax]]});case 26:var sn=f[1];return A0(h(n[1][1+U],n),sn,a,function(Ax){return[0,o,[26,Ax]]});case 27:var Ha=f[1];return A0(h(n[1][1+y0],n),Ha,a,function(Ax){return[0,o,[27,Ax]]});case 28:var Va=f[1];return A0(h(n[1][1+w0],n),Va,a,function(Ax){return[0,o,[28,Ax]]});case 29:var $a=f[1];return I0(h(n[1][1+ex],n),o,$a,a,function(Ax){return[0,o,[29,Ax]]});case 30:var zn=f[1];return I0(h(n[1][1+J2],n),o,zn,a,function(Ax){return[0,o,[30,Ax]]});case 31:var E3=f[1];return I0(h(n[1][1+Jn],n),o,E3,a,function(Ax){return[0,o,[31,Ax]]});case 32:var Wa=f[1];return I0(h(n[1][1+Xn],n),o,Wa,a,function(Ax){return[0,o,[32,Ax]]});case 33:var A3=f[1];return A0(h(n[1][1+G],n),A3,a,function(Ax){return[0,o,[33,Ax]]});case 34:var S3=f[1];return A0(h(n[1][1+G],n),S3,a,function(Ax){return[0,o,[34,Ax]]});default:var P3=f[1];return A0(h(n[1][1+G],n),P3,a,function(Ax){return[0,o,[35,Ax]]})}},r0,function(n,a){var f=a[1],o=a[2];return A0(h(n[1][1+o0],n),o,a,function(m){return[0,f,m]})},f0,function(n,a){if(a[0]===0)return a;var f=a[1];return A0(h(n[1][1+r0],n),f,a,function(o){return[1,o]})},jt,function(n,a){if(a[0]===0)return a;var f=a[2],o=a[1],m=k(n[1][1+U],n,f);return m===f?a:[1,o,m]},s1,function(n,a,f){return xx(n[1][1+T2],n,a,f)},p1,function(n,a,f){return xx(n[1][1+Xr],n,a,f)},Xr,function(n,a,f){return xx(n[1][1+T2],n,a,f)},T2,function(n,a,f){var o=f[10],m=f[9],g=f[8],E=f[7],O=f[3],R=f[2],u0=f[1],l0=f[11],F0=f[6],V0=f[5],Cx=f[4],jx=Rx(h(n[1][1+Or],n),u0),kr=Rx(k(n[1][1+L],n,1),m),Qr=k(n[1][1+l1],n,R),Zr=k(n[1][1+n1],n,g),Wx=k(n[1][1+M1],n,O),P1=Rx(h(n[1][1+z],n),E),e2=k(n[1][1+G],n,o);return u0===jx&&R===Qr&&O===Wx&&E===P1&&g===Zr&&m===kr&&o===e2?f:[0,jx,Qr,Wx,Cx,V0,F0,P1,Zr,kr,e2,l0]},l1,function(n,a){var f=a[2],o=f[4],m=f[3],g=f[2],E=f[1],O=a[1],R=vr(h(n[1][1+$1],n),g),u0=Rx(h(n[1][1+Cr],n),m),l0=Rx(h(n[1][1+g1],n),E),F0=k(n[1][1+G],n,o);return g===R&&m===u0&&o===F0&&E===l0?a:[0,O,[0,l0,R,u0,F0]]},g1,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+r0],n,m),O=k(n[1][1+G],n,o);return E===m&&O===o?a:[0,g,[0,E,O]]},$1,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+u1],n,m),O=k(n[1][1+Ot],n,o);return m===E&&o===O?a:[0,g,[0,E,O]]},n1,function(n,a){switch(a[0]){case 0:return a;case 1:var f=a[1];return A0(h(n[1][1+r0],n),f,a,function(m){return[1,m]});default:var o=a[1];return A0(h(n[1][1+e0],n),o,a,function(m){return[2,m]})}},M1,function(n,a){if(a[0]===0){var f=a[1];return A0(h(n[1][1+l2],n),f,a,function(m){return[0,m]})}var o=a[1];return A0(h(n[1][1+v5],n),o,a,function(m){return[1,m]})},l2,function(n,a){var f=a[1],o=a[2];return I0(h(n[1][1+Gn],n),f,o,a,function(m){return[0,f,m]})},v5,function(n,a){return k(n[1][1+Mx],n,a)},Or,function(n,a){return xx(n[1][1+X0],n,B$,a)},qx,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+G],n,o);return o===E?a:[0,g,[0,m,E]]},J,function(n,a){return k(n[1][1+qx],n,a)},q,function(n,a){return k(n[1][1+J],n,a)},Yn,function(n,a){return k(n[1][1+J],n,a)},d0,function(n,a,f){var o=f[5],m=f[4],g=f[3],E=f[2],O=f[1],R=k(n[1][1+Yn],n,O),u0=Rx(k(n[1][1+L],n,6),E),l0=h(n[1][1+Ur],n),F0=vr(function(jx){return Q1(l0,jx)},g),V0=Q1(h(n[1][1+vx],n),m),Cx=k(n[1][1+G],n,o);return R===O&&u0===E&&F0===g&&V0===m&&Cx===o?f:[0,R,u0,F0,V0,Cx]},c0,function(n,a,f){return xx(n[1][1+d0],n,a,f)},E0,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+G],n,o);return o===E?a:[0,g,[0,m,E]]},p3,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+Mx],n,m),O=k(n[1][1+G],n,o);return m===E&&o===O?a:[0,g,[0,E,O]]},pr,function(n,a,f){var o=f[2],m=f[1],g=k(n[1][1+Mx],n,m),E=k(n[1][1+G],n,o);return m===g&&o===E?f:[0,g,E]},wr,function(n,a,f){return k(n[1][1+D0],n,f)},Fr,function(n,a,f){var o=f[2],m=f[1],g=k(n[1][1+D0],n,m),E=k(n[1][1+G],n,o);return m===g&&o===E?f:[0,g,E]},tr,function(n,a,f){var o=f[4],m=f[3],g=f[2],E=f[1],O=k(n[1][1+$0],n,E),R=xx(n[1][1+wr],n,m!==0?1:0,g),u0=h(n[1][1+Fr],n),l0=Rx(function(V0){return Q1(u0,V0)},m),F0=k(n[1][1+G],n,o);return E===O&&g===R&&m===l0&&o===F0?f:[0,O,R,l0,F0]},er,function(n,a,f){var o=f[5],m=f[4],g=f[3],E=f[2],O=f[1],R=Q1(h(n[1][1+Ox],n),E),u0=Rx(k(n[1][1+kx],n,O),m),l0=Rx(function(V0){var Cx=V0[1],jx=V0[2],kr=xx(n[1][1+Qx],n,O,Cx);return kr===Cx?V0:[0,kr,jx]},g),F0=k(n[1][1+G],n,o);return E===R&&m===u0&&g===l0&&o===F0?f:[0,O,R,l0,u0,F0]},Ox,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+G],n,o);return o===E?f:[0,g,m,E]},kx,function(n,a,f){if(f[0]===0){var o=f[1],m=vr(k(n[1][1+ir],n,a),o);return o===m?f:[0,m]}var g=f[1],E=g[1],O=g[2];return I0(k(n[1][1+Lx],n,a),E,O,f,function(R){return[1,[0,E,R]]})},m0,function(n,a){return k(n[1][1+qx],n,a)},ir,function(n,a,f){var o=f[3],m=f[2],g=f[1];x:{r:{var E=f[4];if(a){e:{if(g)switch(g[1]){case 0:break r;case 1:break e}if(2<=a){var O=0,R=0;break x}}var O=1,R=0;break x}}var O=1,R=1}var u0=m?k(n[1][1+m0],n,o):R?k(n[1][1+Yn],n,o):xx(n[1][1+X0],n,X$,o);if(m)var l0=m[1],F0=O?h(n[1][1+Yn],n):k(n[1][1+X0],n,G$),V0=A0(F0,l0,m,function(Cx){return[0,Cx]});else var V0=0;return m===V0&&o===u0?f:[0,g,V0,u0,E]},Qx,function(n,a,f){var o=2<=a?k(n[1][1+X0],n,Y$):h(n[1][1+Yn],n);return h(o,f)},Lx,function(n,a,f,o){var m=2<=a?k(n[1][1+X0],n,J$):h(n[1][1+Yn],n);return h(m,o)},Ro,function(n,a,f){var o=f[4],m=f[3],g=f[2],E=f[1],O=k(n[1][1+Do],n,E),R=Rx(h(n[1][1+Bp],n),g),u0=k(n[1][1+Xp],n,m),l0=k(n[1][1+G],n,o);return E===O&&g===R&&m===u0&&o===l0?f:[0,O,R,u0,l0]},Up,function(n,a,f){var o=f[4],m=f[3],g=k(n[1][1+Xp],n,m),E=k(n[1][1+G],n,o);return m===g&&o===E?f:[0,f[1],f[2],g,E]},Do,function(n,a){var f=a[2],o=f[4],m=f[2],g=f[1],E=f[3],O=a[1],R=k(n[1][1+Ll],n,g),u0=Rx(h(n[1][1+Ho],n),m),l0=vr(h(n[1][1+Xs],n),o);return g===R&&m===u0&&o===l0?a:[0,O,[0,R,u0,E,l0]]},Bp,function(n,a){var f=a[2][1],o=a[1],m=k(n[1][1+Ll],n,f);return f===m?a:[0,o,[0,m]]},Xs,function(n,a){if(a[0]===0){var f=a[1];return A0(h(n[1][1+B],n),f,a,function(E){return[0,E]})}var o=a[1],m=o[1],g=o[2];return I0(h(n[1][1+a3],n),m,g,a,function(E){return[1,[0,m,E]]})},a3,function(n,a,f){var o=f[2],m=f[1],g=k(n[1][1+Mx],n,m),E=k(n[1][1+G],n,o);return m===g&&o===E?f:[0,g,E]},B,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+ql],n,m),O=Rx(h(n[1][1+Gp],n),o);return m===E&&o===O?a:[0,g,[0,E,O]]},ql,function(n,a){if(a[0]===0){var f=a[1];return A0(h(n[1][1+Jp],n),f,a,function(m){return[0,m]})}var o=a[1];return A0(h(n[1][1+Yp],n),o,a,function(m){return[1,m]})},Jp,function(n,a){return k(n[1][1+At],n,a)},Yp,function(n,a){return k(n[1][1+Fo],n,a)},Gp,function(n,a){if(a[0]===0){var f=a[1],o=f[1],m=f[2];return I0(h(n[1][1+s5],n),o,m,a,function(R){return[0,[0,o,R]]})}var g=a[1],E=g[1],O=g[2];return I0(h(n[1][1+o5],n),E,O,a,function(R){return[1,[0,E,R]]})},o5,function(n,a,f){return xx(n[1][1+Gs],n,a,f)},s5,function(n,a,f){return xx(n[1][1+ex],n,a,f)},Xp,function(n,a){var f=a[2],o=a[1],m=vr(h(n[1][1+a5],n),f);return f===m?a:[0,o,m]},a5,function(n,a){var f=a[2],o=a[1];switch(f[0]){case 0:var m=f[1];return I0(h(n[1][1+Ro],n),o,m,a,function(R){return[0,o,[0,R]]});case 1:var g=f[1];return I0(h(n[1][1+Up],n),o,g,a,function(R){return[0,o,[1,R]]});case 2:var E=f[1];return I0(h(n[1][1+Gs],n),o,E,a,function(R){return[0,o,[2,R]]});case 3:var O=f[1];return A0(h(n[1][1+Fl],n),O,a,function(R){return[0,o,[3,R]]});default:return a}},Gs,function(n,a,f){var o=f[2],m=f[1],g=k(n[1][1+G],n,o);if(!m)return o===g?f:[0,0,g];var E=m[1],O=k(n[1][1+Mx],n,E);return E===O&&o===g?f:[0,[0,O],g]},Fl,function(n,a){var f=a[2],o=a[1],m=k(n[1][1+Mx],n,o),g=k(n[1][1+G],n,f);return o===m&&f===g?a:[0,m,g]},Ll,function(n,a){switch(a[0]){case 0:var f=a[1];return A0(h(n[1][1+Ml],n),f,a,function(g){return[0,g]});case 1:var o=a[1];return A0(h(n[1][1+c5],n),o,a,function(g){return[1,g]});default:var m=a[1];return A0(h(n[1][1+s3],n),m,a,function(g){return[2,g]})}},Ml,function(n,a){return k(n[1][1+At],n,a)},c5,function(n,a){return k(n[1][1+Fo],n,a)},s3,function(n,a){return k(n[1][1+Rl],n,a)},Fo,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+At],n,m),O=k(n[1][1+At],n,o);return m===E&&o===O?a:[0,g,[0,E,O]]},Rl,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+Lp],n,m),O=k(n[1][1+At],n,o);return m===E&&o===O?a:[0,g,[0,E,O]]},Lp,function(n,a){if(a[0]===0){var f=a[1];return A0(h(n[1][1+qp],n),f,a,function(m){return[0,m]})}var o=a[1];return A0(h(n[1][1+Rl],n),o,a,function(m){return[1,m]})},qp,function(n,a){return k(n[1][1+Ml],n,a)},At,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+G],n,o);return o===E?a:[0,g,[0,m,E]]},c3,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+Bs],n,g),O=k(n[1][1+D0],n,m),R=k(n[1][1+G],n,o);return g===E&&m===O&&o===R?f:[0,E,O,R]},Us,function(n,a,f){var o=f[4],m=f[3],g=f[2],E=k(n[1][1+Mx],n,g),O=k(n[1][1+Mx],n,m),R=k(n[1][1+G],n,o);return g===E&&m===O&&o===R?f:[0,f[1],E,O,R]},Et,function(n,a,f,o){var m=o[4],g=o[2],E=o[1],O=o[3],R=k(n[1][1+Mx],n,E),u0=vr(k(n[1][1+No],n,f),g),l0=k(n[1][1+G],n,m);return E===R&&g===u0&&m===l0?o:[0,R,u0,O,l0]},No,function(n,a,f){var o=f[2],m=o[4],g=o[3],E=o[2],O=o[1],R=o[5],u0=f[1],l0=k(n[1][1+Zt],n,O),F0=h(a,E),V0=Rx(h(n[1][1+Mx],n),g),Cx=k(n[1][1+G],n,m);return O===l0&&E===F0&&g===V0&&m===Cx?f:[0,u0,[0,l0,F0,V0,Cx,R]]},qs,function(n,a,f){var o=h(n[1][1+Mx],n);return G6(n[1][1+Et],n,a,o,f)},_r,function(n,a,f){var o=h(n[1][1+D0],n);return G6(n[1][1+Et],n,a,o,f)},Zt,function(n,a){var f=a[2],o=a[1];switch(f[0]){case 0:var m=f[1];return A0(h(n[1][1+ct],n),m,a,function(Wx){return[0,o,[0,Wx]]});case 1:var g=f[1];return I0(h(n[1][1+J2],n),o,g,a,function(Wx){return[0,o,[1,Wx]]});case 2:var E=f[1];return I0(h(n[1][1+Jn],n),o,E,a,function(Wx){return[0,o,[2,Wx]]});case 3:var O=f[1];return I0(h(n[1][1+ex],n),o,O,a,function(Wx){return[0,o,[3,Wx]]});case 4:var R=f[1];return I0(h(n[1][1+Xn],n),o,R,a,function(Wx){return[0,o,[4,Wx]]});case 5:var u0=f[1];return A0(h(n[1][1+G],n),u0,a,function(Wx){return[0,o,[5,Wx]]});case 6:var l0=f[1];return A0(h(n[1][1+Tt],n),l0,a,function(Wx){return[0,o,[6,Wx]]});case 7:var F0=f[1];return I0(h(n[1][1+Mn],n),o,F0,a,function(Wx){return[0,o,[7,Wx]]});case 8:var V0=f[1];return A0(h(n[1][1+qx],n),V0,a,function(Wx){return[0,o,[8,Wx]]});case 9:var Cx=f[1];return A0(h(n[1][1+Ls],n),Cx,a,function(Wx){return[0,o,[9,Wx]]});case 10:var jx=f[1];return A0(h(n[1][1+Rs],n),jx,a,function(Wx){return[0,o,[10,Wx]]});case 11:var kr=f[1];return A0(h(n[1][1+Oo],n),kr,a,function(Wx){return[0,o,[11,Wx]]});case 12:var Qr=f[1];return A0(h(n[1][1+i3],n),Qr,a,function(Wx){return[0,o,[12,Wx]]});default:var Zr=f[1];return A0(h(n[1][1+b2],n),Zr,a,function(Wx){return[0,o,[13,Wx]]})}},Tt,function(n,a){var f=a[3],o=a[2],m=o[1],g=a[1],E=o[2],O=I0(h(n[1][1+u3],n),m,E,o,function(u0){return[0,m,u0]}),R=k(n[1][1+G],n,f);return o===O&&f===R?a:[0,g,O,R]},u3,function(n,a,f){if(f[0]===0){var o=f[1];return I0(h(n[1][1+J2],n),a,o,f,function(g){return[0,g]})}var m=f[1];return I0(h(n[1][1+Jn],n),a,m,f,function(g){return[1,g]})},Ls,function(n,a){var f=a[2],o=f[3],m=f[2],g=f[1],E=a[1],O=k(n[1][1+Co],n,g),R=k(n[1][1+Ms],n,m),u0=k(n[1][1+G],n,o);return g===O&&m===R&&o===u0?a:[0,E,[0,O,R,u0]]},Co,function(n,a){if(a[0]===0){var f=a[1];return A0(h(n[1][1+qx],n),f,a,function(m){return[0,m]})}var o=a[1];return A0(h(n[1][1+Ls],n),o,a,function(m){return[1,m]})},Ms,function(n,a){switch(a[0]){case 0:var f=a[1],o=f[1],m=f[2];return I0(h(n[1][1+ex],n),o,m,a,function(V0){return[0,[0,o,V0]]});case 1:var g=a[1],E=g[1],O=g[2];return I0(h(n[1][1+J2],n),E,O,a,function(V0){return[1,[0,E,V0]]});case 2:var R=a[1],u0=R[1],l0=R[2];return I0(h(n[1][1+Jn],n),u0,l0,a,function(V0){return[2,[0,u0,V0]]});default:var F0=a[1];return A0(h(n[1][1+qx],n),F0,a,function(V0){return[3,V0]})}},Mn,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=xx(n[1][1+X0],n,[0,g],m),O=k(n[1][1+G],n,o);return m===E&&o===O?f:[0,g,E,O]},Rs,function(n,a){var f=a[3],o=a[2],m=a[1],g=vr(h(n[1][1+Rn],n),m),E=sp(h(n[1][1+Fs],n),o),O=k(n[1][1+G],n,f);return m===g&&o===E&&f===O?a:[0,g,E,O]},Rn,function(n,a){var f=a[2],o=a[1];if(f[0]!==0){var m=f[1],g=k(n[1][1+qx],n,m);return m===g?a:[0,o,[1,g]]}var E=f[1],O=E[4],R=E[2],u0=E[1],l0=E[3],F0=k(n[1][1+f3],n,u0),V0=k(n[1][1+Zt],n,R),Cx=k(n[1][1+G],n,O);return u0===F0&&R===V0&&O===Cx?a:[0,o,[0,[0,F0,V0,l0,Cx]]]},f3,function(n,a){switch(a[0]){case 0:var f=a[1],o=f[1],m=f[2];return I0(h(n[1][1+ex],n),o,m,a,function(V0){return[0,[0,o,V0]]});case 1:var g=a[1],E=g[1],O=g[2];return I0(h(n[1][1+J2],n),E,O,a,function(V0){return[1,[0,E,V0]]});case 2:var R=a[1],u0=R[1],l0=R[2];return I0(h(n[1][1+Jn],n),u0,l0,a,function(V0){return[2,[0,u0,V0]]});default:var F0=a[1];return A0(h(n[1][1+qx],n),F0,a,function(V0){return[3,V0]})}},Oo,function(n,a){var f=a[3],o=a[2],m=a[1],g=vr(h(n[1][1+Xa],n),m),E=sp(h(n[1][1+Fs],n),o),O=k(n[1][1+G],n,f);return m===g&&o===E&&f===O?a:[0,g,E,O]},Xa,function(n,a){var f=a[2],o=a[1],m=k(n[1][1+Zt],n,f);return f===m?a:[0,o,m]},Fs,function(n,a,f){var o=f[2],m=f[1],g=sp(h(n[1][1+Mn],n),m),E=k(n[1][1+G],n,o);return m===g&&o===E?f:[0,g,E]},i3,function(n,a){var f=a[2],o=a[1],m=vr(h(n[1][1+Zt],n),o),g=k(n[1][1+G],n,f);return o===m&&f===g?a:[0,m,g]},b2,function(n,a){var f=a[3],o=a[2],m=a[1],g=k(n[1][1+Zt],n,m),E=k(n[1][1+ge],n,o),O=k(n[1][1+G],n,f);return m===g&&o===E&&f===O?a:[0,g,E,O]},ge,function(n,a){if(a[0]===0){var f=a[1];return A0(k(n[1][1+X0],n,z$),f,a,function(g){return[0,g]})}var o=a[1],m=a[2];return I0(h(n[1][1+Mn],n),o,m,a,function(g){return[1,o,g]})},ct,function(n,a){var f=a[1],o=a[2],m=k(n[1][1+G],n,f);return f===m?a:[0,m,o]},fe,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+Mx],n,g),O=k(n[1][1+Fn],n,m),R=k(n[1][1+G],n,o);return g===E&&m===O&&o===R?f:[0,E,O,R]},jr,function(n,a,f){var o=f[1],m=xx(n[1][1+fe],n,a,o);return o===m?f:[0,m,f[2],f[3]]},Fn,function(n,a){switch(a[0]){case 0:var f=a[1];return A0(h(n[1][1+S1],n),f,a,function(g){return[0,g]});case 1:var o=a[1];return A0(h(n[1][1+Ba],n),o,a,function(g){return[1,g]});default:var m=a[1];return A0(h(n[1][1+Dn],n),m,a,function(g){return[2,g]})}},S1,function(n,a){return k(n[1][1+qx],n,a)},Ba,function(n,a){return k(n[1][1+E0],n,a)},Dn,function(n,a){return k(n[1][1+Mx],n,a)},R2,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+qx],n,g),O=k(n[1][1+qx],n,m),R=k(n[1][1+G],n,o);return g===E&&m===O&&o===R?f:[0,E,O,R]},js,function(n,a,f){var o=f[4],m=f[3],g=f[2],E=f[1],O=k(n[1][1+Mx],n,E),R=Rx(h(n[1][1+Ho],n),g),u0=Rx(h(n[1][1+Hp],n),m),l0=k(n[1][1+G],n,o);return E===O&&g===R&&m===u0&&o===l0?f:[0,O,R,u0,l0]},ie,function(n,a,f){var o=f[2],m=f[1],g=vr(function(O){if(O[0]===0){var R=O[1],u0=k(n[1][1+Ar],n,R);return R===u0?O:[0,u0]}var l0=O[1],F0=k(n[1][1+dx],n,l0);return l0===F0?O:[1,F0]},m),E=k(n[1][1+G],n,o);return m===g&&o===E?f:[0,g,E]},Ar,function(n,a){var f=a[2],o=a[1];switch(f[0]){case 0:var m=f[3],g=f[2],E=f[1],O=k(n[1][1+Nx],n,E),R=k(n[1][1+Mx],n,g);x:if(m){if(O[0]===3){var u0=R[2];if(u0[0]===10){var F0=Tr(O[1][2][1],u0[1][2][1]);break x}}var l0=E===O?1:0,F0=l0&&(g===R?1:0)}else var F0=m;return E===O&&g===R&&m===F0?a:[0,o,[0,O,R,F0]];case 1:var V0=f[2],Cx=f[1],jx=k(n[1][1+Nx],n,Cx),kr=Q1(h(n[1][1+Xr],n),V0);return Cx===jx&&V0===kr?a:[0,o,[1,jx,kr]];case 2:var Qr=f[3],Zr=f[2],Wx=f[1],P1=k(n[1][1+Nx],n,Wx),e2=Q1(h(n[1][1+Xr],n),Zr),q2=k(n[1][1+G],n,Qr);return Wx===P1&&Zr===e2&&Qr===q2?a:[0,o,[2,P1,e2,q2]];default:var Te=f[3],Ee=f[2],Je=f[1],H2=k(n[1][1+Nx],n,Je),Ae=Q1(h(n[1][1+Xr],n),Ee),Se=k(n[1][1+G],n,Te);return Je===H2&&Ee===Ae&&Te===Se?a:[0,o,[3,H2,Ae,Se]]}},Nx,function(n,a){switch(a[0]){case 0:var f=a[1];return A0(h(n[1][1+a1],n),f,a,function(R){return[0,R]});case 1:var o=a[1];return A0(h(n[1][1+v1],n),o,a,function(R){return[1,R]});case 2:var m=a[1];return A0(h(n[1][1+hx],n),m,a,function(R){return[2,R]});case 3:var g=a[1];return A0(h(n[1][1+Sr],n),g,a,function(R){return[3,R]});case 4:var E=a[1];return A0(h(n[1][1+E0],n),E,a,function(R){return[4,R]});default:var O=a[1];return A0(h(n[1][1+lr],n),O,a,function(R){return[5,R]})}},a1,function(n,a){var f=a[1],o=a[2];return I0(h(n[1][1+ex],n),f,o,a,function(m){return[0,f,m]})},v1,function(n,a){var f=a[1],o=a[2];return I0(h(n[1][1+J2],n),f,o,a,function(m){return[0,f,m]})},hx,function(n,a){var f=a[1],o=a[2];return I0(h(n[1][1+Jn],n),f,o,a,function(m){return[0,f,m]})},Sr,function(n,a){return k(n[1][1+qx],n,a)},lr,function(n,a){return k(n[1][1+p3],n,a)},e1,function(n,a,f){var o=f[5],m=f[4],g=f[3],E=f[2],O=f[1],R=k(n[1][1+Yn],n,O),u0=Rx(k(n[1][1+L],n,7),E),l0=Rx(h(n[1][1+o0],n),g),F0=Rx(h(n[1][1+o0],n),m),V0=k(n[1][1+G],n,o);return O===R&&g===l0&&E===u0&&g===l0&&m===F0&&o===V0?f:[0,R,u0,l0,F0,V0]},u1,function(n,a){return xx(n[1][1+w3],n,K$,a)},v,function(n,a,f){return xx(n[1][1+w3],n,[0,a],f)},Ye,function(n,a){return xx(n[1][1+w3],n,H$,a)},st,function(n,a){return k(n[1][1+b3],n,a)},St,function(n,a){return k(n[1][1+b3],n,a)},w3,function(n,a,f){var o=a?a[1]:0;return xx(n[1][1+gr],n,[0,o],f)},b3,function(n,a){return xx(n[1][1+gr],n,0,a)},gr,function(n,a,f){var o=f[2],m=f[1];switch(o[0]){case 0:var g=o[1],E=g[3],O=g[2],R=g[1],u0=vr(k(n[1][1+H0],n,a),R),l0=k(n[1][1+f0],n,O),F0=k(n[1][1+G],n,E);x:{if(u0===R&&l0===O&&F0===E){var V0=o;break x}var V0=[0,[0,u0,l0,F0]]}var oe=V0;break;case 1:var Cx=o[1],jx=Cx[3],kr=Cx[2],Qr=Cx[1],Zr=vr(k(n[1][1+fr],n,a),Qr),Wx=k(n[1][1+f0],n,kr),P1=k(n[1][1+G],n,jx);x:{if(jx===P1&&Zr===Qr&&Wx===kr){var e2=o;break x}var e2=[1,[0,Zr,Wx,P1]]}var oe=e2;break;case 2:var q2=o[1],Te=q2[2],Ee=q2[1],Je=q2[3],H2=xx(n[1][1+X0],n,a,Ee),Ae=k(n[1][1+f0],n,Te);x:{if(Ee===H2&&Te===Ae){var Se=o;break x}var Se=[2,[0,H2,Ae,Je]]}var oe=Se;break;default:var cn=o[1],oe=A0(h(n[1][1+or],n),cn,o,function(an){return[3,an]})}return o===oe?f:[0,m,oe]},X0,function(n,a,f){return k(n[1][1+qx],n,f)},Dx,function(n,a,f,o){return xx(n[1][1+ex],n,f,o)},ur,function(n,a,f,o){return xx(n[1][1+J2],n,f,o)},Q0,function(n,a,f,o){return xx(n[1][1+Jn],n,f,o)},H0,function(n,a,f){if(f[0]===0){var o=f[1];return A0(k(n[1][1+Fx],n,a),o,f,function(g){return[0,g]})}var m=f[1];return A0(k(n[1][1+K0],n,a),m,f,function(g){return[1,g]})},Fx,function(n,a,f){var o=f[2],m=o[4],g=o[3],E=o[2],O=o[1],R=f[1],u0=xx(n[1][1+wx],n,a,O),l0=xx(n[1][1+V],n,a,E),F0=k(n[1][1+Ot],n,g);x:if(m){if(u0[0]===3){var V0=l0[2];if(V0[0]===2){var jx=Tr(u0[1][2][1],V0[1][1][2][1]);break x}}var Cx=O===u0?1:0,jx=Cx&&(E===l0?1:0)}else var jx=m;return u0===O&&l0===E&&F0===g&&m===jx?f:[0,R,[0,u0,l0,F0,jx]]},wx,function(n,a,f){switch(f[0]){case 0:var o=f[1];return A0(k(n[1][1+A],n,a),o,f,function(R){return[0,R]});case 1:var m=f[1];return A0(k(n[1][1+fx],n,a),m,f,function(R){return[1,R]});case 2:var g=f[1];return A0(k(n[1][1+xr],n,a),g,f,function(R){return[2,R]});case 3:var E=f[1];return A0(k(n[1][1+Ix],n,a),E,f,function(R){return[3,R]});default:var O=f[1];return A0(k(n[1][1+ox],n,a),O,f,function(R){return[4,R]})}},A,function(n,a,f){var o=f[1],m=f[2];return I0(k(n[1][1+Dx],n,a),o,m,f,function(g){return[0,o,g]})},fx,function(n,a,f){var o=f[1],m=f[2];return I0(k(n[1][1+ur],n,a),o,m,f,function(g){return[0,o,g]})},xr,function(n,a,f){var o=f[1],m=f[2];return I0(k(n[1][1+Q0],n,a),o,m,f,function(g){return[0,o,g]})},Ix,function(n,a,f){return xx(n[1][1+X0],n,a,f)},ox,function(n,a,f){return k(n[1][1+p3],n,f)},K0,function(n,a,f){var o=f[2],m=o[2],g=o[1],E=f[1],O=xx(n[1][1+Xx],n,a,g),R=k(n[1][1+G],n,m);return O===g&&m===R?f:[0,E,[0,O,R]]},V,function(n,a,f){return xx(n[1][1+gr],n,a,f)},Xx,function(n,a,f){return xx(n[1][1+gr],n,a,f)},fr,function(n,a,f){switch(f[0]){case 0:var o=f[1];return A0(k(n[1][1+$x],n,a),o,f,function(g){return[0,g]});case 1:var m=f[1];return A0(k(n[1][1+ix],n,a),m,f,function(g){return[1,g]});default:return f}},$x,function(n,a,f){var o=f[2],m=o[2],g=o[1],E=f[1],O=xx(n[1][1+ax],n,a,g),R=k(n[1][1+Ot],n,m);return g===O&&m===R?f:[0,E,[0,O,R]]},ax,function(n,a,f){return xx(n[1][1+gr],n,a,f)},ix,function(n,a,f){var o=f[2],m=o[2],g=o[1],E=f[1],O=xx(n[1][1+yx],n,a,g),R=k(n[1][1+G],n,m);return O===g&&m===R?f:[0,E,[0,O,R]]},yx,function(n,a,f){return xx(n[1][1+gr],n,a,f)},or,function(n,a){return k(n[1][1+Mx],n,a)},z,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1];if(m)var E=m[1],O=A0(h(n[1][1+Mx],n),E,m,function(u0){return[0,u0]});else var O=m;var R=k(n[1][1+G],n,o);return m===O&&o===R?a:[0,g,[0,O,R]]},$0,function(n,a){return k(n[1][1+Mx],n,a)},e0,function(n,a){var f=a[2],o=a[1],m=k(n[1][1+W],n,f);return il(m,f)?a:[0,o,m]},W,function(n,a){var f=a[2],o=f[3],m=f[2],g=m[2],E=m[1],O=f[1],R=a[1],u0=k(n[1][1+qx],n,E),l0=Rx(h(n[1][1+o0],n),g),F0=k(n[1][1+G],n,o);return u0===E&&l0===g&&F0===o?a:[0,R,[0,O,[0,u0,l0],F0]]},Cr,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+u1],n,m),O=k(n[1][1+G],n,o);return m===E&&o===O?a:[0,g,[0,E,O]]},_0,function(n,a,f){var o=f[2],m=f[1],g=f[3],E=Rx(h(n[1][1+Mx],n),m),O=k(n[1][1+G],n,o);return m===E&&o===O?f:[0,E,O,g]},K,function(n,a,f){var o=f[2],m=f[1],g=vr(h(n[1][1+Mx],n),m),E=k(n[1][1+G],n,o);return m===g&&o===E?f:[0,g,E]},P0,function(n,a){return k(n[1][1+nx],n,a)},nx,function(n,a){var f=h(n[1][1+px],n),o=y2(function(g,E){var O=g[2],R=g[1],u0=h(f,E);if(!u0)return[0,R,1];if(u0[2])return[0,al(u0,R),1];var l0=u0[1],F0=O||(E!==l0?1:0);return[0,[0,l0,R],F0]},V$,a),m=o[1];return o[2]?cx(m):a},px,function(n,a){return[0,k(n[1][1+D0],n,a),0]},_x,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+Mx],n,m),O=k(n[1][1+G],n,o);return m===E&&o===O?a:[0,g,[0,E,O]]},dx,function(n,a){var f=a[2],o=f[2],m=f[1],g=a[1],E=k(n[1][1+Mx],n,m),O=k(n[1][1+G],n,o);return m===E&&o===O?a:[0,g,[0,E,O]]},ux,function(n,a,f){var o=f[1],m=k(n[1][1+G],n,o);return o===m?f:[0,m]},N0,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=f[4],O=k(n[1][1+Mx],n,g),R=vr(h(n[1][1+Z0],n),m),u0=k(n[1][1+G],n,o);return g===O&&m===R&&o===u0?f:[0,O,R,u0,E]},Z0,function(n,a){var f=a[2],o=f[3],m=f[2],g=f[1],E=a[1],O=Rx(h(n[1][1+Mx],n),g),R=k(n[1][1+nx],n,m),u0=k(n[1][1+G],n,o);return g===O&&m===R&&o===u0?a:[0,E,[0,O,R,u0]]},k0,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+Mx],n,g),O=Q1(h(n[1][1+G0],n),m),R=k(n[1][1+G],n,o);return g===E&&m===O&&o===R?f:[0,E,O,R]},G0,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=vr(h(n[1][1+T0],n),g),O=vr(h(n[1][1+Mx],n),m),R=k(n[1][1+G],n,o);return g===E&&m===O&&o===R?f:[0,E,O,R]},T0,function(n,a){return a},U0,function(n,a,f){var o=f[1],m=k(n[1][1+G],n,o);return o===m?f:[0,m]},M0,function(n,a,f){var o=f[2],m=f[1],g=k(n[1][1+Mx],n,m),E=k(n[1][1+G],n,o);return m===g&&o===E?f:[0,g,E]},C0,function(n,a,f){var o=f[4],m=f[3],g=f[2],E=f[1],O=Q1(h(n[1][1+Gn],n),E);if(g)var R=g[1],u0=R[1],l0=R[2],F0=I0(h(n[1][1+y3],n),u0,l0,g,function(Zr){return[0,[0,u0,Zr]]});else var F0=g;if(m)var V0=m[1],Cx=V0[1],jx=V0[2],kr=I0(h(n[1][1+Gn],n),Cx,jx,m,function(Zr){return[0,[0,Cx,Zr]]});else var kr=m;var Qr=k(n[1][1+G],n,o);return E===O&&g===F0&&m===kr&&o===Qr?f:[0,O,F0,kr,Qr]},x0,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+Mx],n,g),O=k(n[1][1+r0],n,m),R=k(n[1][1+G],n,o);return E===g&&O===m&&R===o?f:[0,E,O,R]},p0,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+Mx],n,g),O=k(n[1][1+r0],n,m),R=k(n[1][1+G],n,o);return E===g&&il(O,m)&&R===o?f:[0,E,O,R]},T,function(n,a,f){var o=f[3],m=f[2],g=k(n[1][1+Mx],n,m),E=k(n[1][1+G],n,o);return m===g&&o===E?f:[0,f[1],g,E]},p,function(n,a,f){var o=f[4],m=f[2],g=k(n[1][1+Mx],n,m),E=k(n[1][1+G],n,o);return m===g&&o===E?f:[0,f[1],g,f[3],E]},l,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=vr(k(n[1][1+s],n,m),g),O=k(n[1][1+G],n,o);return g===E&&o===O?f:[0,E,m,O]},s,function(n,a,f){var o=f[2],m=o[2],g=o[1],E=f[1],O=xx(n[1][1+v],n,a,g),R=Rx(h(n[1][1+Mx],n),m);return g===O&&m===R?f:[0,E,[0,O,R]]},u,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+$0],n,g),O=k(n[1][1+D0],n,m),R=k(n[1][1+G],n,o);return g===E&&m===O&&o===R?f:[0,E,O,R]},t,function(n,a,f){var o=f[3],m=f[2],g=f[1],E=k(n[1][1+Mx],n,g),O=k(n[1][1+D0],n,m),R=k(n[1][1+G],n,o);return g===E&&m===O&&o===R?f:[0,E,O,R]},v0,function(n,a,f){var o=f[4],m=f[3],g=f[2],E=f[1],O=k(n[1][1+Yn],n,E),R=Rx(k(n[1][1+L],n,5),g),u0=k(n[1][1+o0],n,m),l0=k(n[1][1+G],n,o);return E===O&&m===u0&&g===R&&o===l0?f:[0,O,R,u0,l0]},e,function(n,a,f){var o=f[2],m=f[1],g=f[4],E=f[3],O=Rx(h(n[1][1+Mx],n),m),R=k(n[1][1+G],n,o);return o===R&&m===O?f:[0,O,R,E,g]}]),function(n,a){return Md(a,x)}}),KN=[];function OU(x,r,e){var t=e[2];switch(t[0]){case 0:var u=t[1][1];return y2(h(KN[1],x),r,u);case 1:var i=t[1][1];return y2(h(KN[2],x),r,i);case 2:return k(x,r,t[1][1]);default:return r}}qr(KN,[0,function(x,r){return function(e){var t=e[0]===0?e[1][2][2]:e[1][2][1];return OU(x,r,t)}},function(x,r){return function(e){return e[0]===2?r:OU(x,r,e[1][2][1])}}]);var HN=[];function jU(x){var r=x[2];switch(r[0]){case 0:return sl(HN[1],r[1][1]);case 1:return sl(HN[2],r[1][1]);case 2:return 1;default:return 0}}qr(HN,[0,function(x){var r=x[0]===0?x[1][2][2]:x[1][2][1];return jU(r)},function(x){return x[0]===2?0:jU(x[1][2][1])}]);var Gd=[];function VN(x){var r=x[2];switch(r[0]){case 7:return 1;case 10:var e=r[1],t=e[1],u=h(Gd[2],e[2]);return u||sl(Gd[1],t);case 11:var i=r[1],c=i[1],v=h(Gd[2],i[2]);return v||sl(function(s){return VN(s[2])},c);case 12:return sl(VN,r[1][1]);case 13:return 1;default:return 0}}qr(Gd,[0,function(x){var r=x[2];return r[0]===0?VN(r[1][2]):0},function(x){return x&&x[1][2][1]?1:0}]);function $N(x){switch(x){case 0:return RW;case 1:return MW;default:return LW}}function Cn(x,r){return[0,r[1],[0,r[2],x]]}function DU(x,r,e){var t=x?x[1]:0,u=r?r[1]:0;return[0,t,u,e]}function Q(x,r,e){var t=x?x[1]:0,u=r?r[1]:0;return!t&&!u?0:[0,DU([0,t],[0,u],0)]}function j1(x,r,e,t){var u=x?x[1]:0,i=r?r[1]:0;return!u&&!i&&!e?0:[0,DU([0,u],[0,i],e)]}function N2(x,r){if(x){if(r){var e=r[1],t=x[1],u=[0,Gx(t[2],e[2])];return Q([0,Gx(e[1],t[1])],u,j)}var i=x}else var i=r;return i}function Yd(x,r){if(!r)return x;if(x){var e=r[1],t=x[1],u=e[1],i=t[3],c=t[1],v=[0,Gx(t[2],e[2])];return j1([0,Gx(u,c)],v,i,j)}var s=r[1];return j1([0,s[1]],[0,s[2]],0,j)}function FU(x,r){s2(x)(qW),h(s2(x)(BW),UW);var e=r[1];h(s2(x)(XW),e),s2(x)(GW),s2(x)(YW),h(s2(x)(zW),JW);var t=r[2];return h(s2(x)(KW),t),s2(x)(HW),s2(x)(VW)}qr([],[0,FU,FU,function(x,r){switch(r[0]){case 0:var e=r[1];return s2(x)(T$),h(s2(x)(E$),e),s2(x)(A$);case 1:var t=r[1];return s2(x)(S$),h(s2(x)(P$),t),s2(x)(I$);case 2:var u=r[1];return s2(x)(C$),h(s2(x)(N$),u),s2(x)(O$);default:var i=r[1];return s2(x)(j$),h(s2(x)(D$),i),s2(x)(F$)}}]);function Kr(x,r){return[0,x[1],x[2],r[3]]}function Es(x,r){var e=x[1]-r[1]|0;return e===0?x[2]-r[2]|0:e}function RU(x,r){var e=r[1],t=x[1];if(t){var u=t[1];if(e)var i=e[1],c=NU(i),v=NU(u)-c|0,s=v===0?sx(u[1],i[1]):v;else var s=-1}else var s=e?1:0;if(s!==0)return s;var l=Es(x[2],r[2]);return l===0?Es(x[3],r[3]):l}function mo(x,r){return RU(x,r)===0?1:0}var hr=[];qr(hr,[0,function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return je(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return je(r,e)},function(x,r,e){return je(r,e)},function(x,r,e){return je(r,e)},function(x,r,e){return je(r,e)},function(x,r,e){return je(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return je(r,e)},function(x,r,e){return je(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r){switch(x){case 0:if(!r)return 0;break;case 1:if(r===1)return 0;break;case 2:if(r===2)return 0;break;case 3:if(r===3)return 0;break;default:if(4<=r)return 0}function e(u){switch(u){case 0:return 0;case 1:return 1;case 2:return 2;case 3:return 3;default:return 4}}var t=e(r);return je(e(x),t)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return je(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)},function(x,r,e){return sx(r,e)}]);var MU=ax0.slice();function WN(x){for(var r=0,e=MU.length-1-1|0;;){if(e<r)return 0;var t=r+((e-r|0)/2|0)|0,u=MU[1+t],i=u[2];if(x<u[1])var e=t-1|0;else{if(i>x)return 1;var r=t+1|0}}}var LU=0;function qU(x){var r=x[2];return[0,x[1],[0,r[1],r[2],r[3],r[4],r[5],r[6],r[7],r[8],r[9],r[10],r[11],r[12]],x[3],x[4],x[5],x[6],x[7]]}function UU(x){return x[3][1]}function Jd(x,r){return x!==r[4]?[0,r[1],r[2],r[3],x,r[5],r[6],r[7]]:r}var ut=[];function BU(x,r){if(typeof x=="number"){var e=x;if(67<=e)if(pe<=e)switch(e){case 101:if(typeof r=="number"&&pe===r)return 1;break;case 102:if(typeof r=="number"&&E2===r)return 1;break;case 103:if(typeof r=="number"&&wn===r)return 1;break;case 104:if(typeof r=="number"&&We===r)return 1;break;case 105:if(typeof r=="number"&&vn===r)return 1;break;case 106:if(typeof r=="number"&&kt===r)return 1;break;case 107:if(typeof r=="number"&&q1===r)return 1;break;case 108:if(typeof r=="number"&&Sv===r)return 1;break;case 109:if(typeof r=="number"&&lt===r)return 1;break;case 110:if(typeof r=="number"&&B2===r)return 1;break;case 111:if(typeof r=="number"&&U2===r)return 1;break;case 112:if(typeof r=="number"&&ss===r)return 1;break;case 113:if(typeof r=="number"&&ke===r)return 1;break;case 114:if(typeof r=="number"&&br===r)return 1;break;case 115:if(typeof r=="number"&&gv===r)return 1;break;case 116:if(typeof r=="number"&&Tv===r)return 1;break;case 117:if(typeof r=="number"&&Q3===r)return 1;break;case 118:if(typeof r=="number"&&g6===r)return 1;break;case 119:if(typeof r=="number"&&D3===r)return 1;break;case 120:if(typeof r=="number"&&Cf===r)return 1;break;case 121:if(typeof r=="number"&&O6===r)return 1;break;case 122:if(typeof r=="number"&&o1===r)return 1;break;case 123:if(typeof r=="number"&&pn===r)return 1;break;case 124:if(typeof r=="number"&&J3===r)return 1;break;case 125:if(typeof r=="number"&&to===r)return 1;break;case 126:if(typeof r=="number"&&Gk===r)return 1;break;case 127:if(typeof r=="number"&&Jr===r)return 1;break;case 128:if(typeof r=="number"&&U1===r)return 1;break;case 129:if(typeof r=="number"&&uv===r)return 1;break;case 130:if(typeof r=="number"&&D6===r)return 1;break;case 131:if(typeof r=="number"&&h6===r)return 1;break;case 132:if(typeof r=="number"&&hm===r)return 1;break;default:if(typeof r=="number"&&qk<=r)return 1}else switch(e){case 67:if(typeof r=="number"&&r===67)return 1;break;case 68:if(typeof r=="number"&&r===68)return 1;break;case 69:if(typeof r=="number"&&r===69)return 1;break;case 70:if(typeof r=="number"&&r===70)return 1;break;case 71:if(typeof r=="number"&&r===71)return 1;break;case 72:if(typeof r=="number"&&r===72)return 1;break;case 73:if(typeof r=="number"&&r===73)return 1;break;case 74:if(typeof r=="number"&&r===74)return 1;break;case 75:if(typeof r=="number"&&r===75)return 1;break;case 76:if(typeof r=="number"&&r===76)return 1;break;case 77:if(typeof r=="number"&&r===77)return 1;break;case 78:if(typeof r=="number"&&r===78)return 1;break;case 79:if(typeof r=="number"&&r===79)return 1;break;case 80:if(typeof r=="number"&&r===80)return 1;break;case 81:if(typeof r=="number"&&r===81)return 1;break;case 82:if(typeof r=="number"&&r===82)return 1;break;case 83:if(typeof r=="number"&&r===83)return 1;break;case 84:if(typeof r=="number"&&r===84)return 1;break;case 85:if(typeof r=="number"&&r===85)return 1;break;case 86:if(typeof r=="number"&&r===86)return 1;break;case 87:if(typeof r=="number"&&r===87)return 1;break;case 88:if(typeof r=="number"&&r===88)return 1;break;case 89:if(typeof r=="number"&&r===89)return 1;break;case 90:if(typeof r=="number"&&r===90)return 1;break;case 91:if(typeof r=="number"&&r===91)return 1;break;case 92:if(typeof r=="number"&&r===92)return 1;break;case 93:if(typeof r=="number"&&r===93)return 1;break;case 94:if(typeof r=="number"&&r===94)return 1;break;case 95:if(typeof r=="number"&&r===95)return 1;break;case 96:if(typeof r=="number"&&r===96)return 1;break;case 97:if(typeof r=="number"&&r===97)return 1;break;case 98:if(typeof r=="number"&&r===98)return 1;break;case 99:if(typeof r=="number"&&r===99)return 1;break;default:if(typeof r=="number"&&E1===r)return 1}else if(34<=e)switch(e){case 34:if(typeof r=="number"&&r===34)return 1;break;case 35:if(typeof r=="number"&&r===35)return 1;break;case 36:if(typeof r=="number"&&r===36)return 1;break;case 37:if(typeof r=="number"&&r===37)return 1;break;case 38:if(typeof r=="number"&&r===38)return 1;break;case 39:if(typeof r=="number"&&r===39)return 1;break;case 40:if(typeof r=="number"&&r===40)return 1;break;case 41:if(typeof r=="number"&&r===41)return 1;break;case 42:if(typeof r=="number"&&r===42)return 1;break;case 43:if(typeof r=="number"&&r===43)return 1;break;case 44:if(typeof r=="number"&&r===44)return 1;break;case 45:if(typeof r=="number"&&r===45)return 1;break;case 46:if(typeof r=="number"&&r===46)return 1;break;case 47:if(typeof r=="number"&&r===47)return 1;break;case 48:if(typeof r=="number"&&r===48)return 1;break;case 49:if(typeof r=="number"&&r===49)return 1;break;case 50:if(typeof r=="number"&&r===50)return 1;break;case 51:if(typeof r=="number"&&r===51)return 1;break;case 52:if(typeof r=="number"&&r===52)return 1;break;case 53:if(typeof r=="number"&&r===53)return 1;break;case 54:if(typeof r=="number"&&r===54)return 1;break;case 55:if(typeof r=="number"&&r===55)return 1;break;case 56:if(typeof r=="number"&&r===56)return 1;break;case 57:if(typeof r=="number"&&r===57)return 1;break;case 58:if(typeof r=="number"&&r===58)return 1;break;case 59:if(typeof r=="number"&&r===59)return 1;break;case 60:if(typeof r=="number"&&r===60)return 1;break;case 61:if(typeof r=="number"&&r===61)return 1;break;case 62:if(typeof r=="number"&&r===62)return 1;break;case 63:if(typeof r=="number"&&r===63)return 1;break;case 64:if(typeof r=="number"&&r===64)return 1;break;case 65:if(typeof r=="number"&&r===65)return 1;break;default:if(typeof r=="number"&&r===66)return 1}else switch(e){case 0:if(typeof r=="number"&&!r)return 1;break;case 1:if(typeof r=="number"&&r===1)return 1;break;case 2:if(typeof r=="number"&&r===2)return 1;break;case 3:if(typeof r=="number"&&r===3)return 1;break;case 4:if(typeof r=="number"&&r===4)return 1;break;case 5:if(typeof r=="number"&&r===5)return 1;break;case 6:if(typeof r=="number"&&r===6)return 1;break;case 7:if(typeof r=="number"&&r===7)return 1;break;case 8:if(typeof r=="number"&&r===8)return 1;break;case 9:if(typeof r=="number"&&r===9)return 1;break;case 10:if(typeof r=="number"&&r===10)return 1;break;case 11:if(typeof r=="number"&&r===11)return 1;break;case 12:if(typeof r=="number"&&r===12)return 1;break;case 13:if(typeof r=="number"&&r===13)return 1;break;case 14:if(typeof r=="number"&&r===14)return 1;break;case 15:if(typeof r=="number"&&r===15)return 1;break;case 16:if(typeof r=="number"&&r===16)return 1;break;case 17:if(typeof r=="number"&&r===17)return 1;break;case 18:if(typeof r=="number"&&r===18)return 1;break;case 19:if(typeof r=="number"&&r===19)return 1;break;case 20:if(typeof r=="number"&&r===20)return 1;break;case 21:if(typeof r=="number"&&r===21)return 1;break;case 22:if(typeof r=="number"&&r===22)return 1;break;case 23:if(typeof r=="number"&&r===23)return 1;break;case 24:if(typeof r=="number"&&r===24)return 1;break;case 25:if(typeof r=="number"&&r===25)return 1;break;case 26:if(typeof r=="number"&&r===26)return 1;break;case 27:if(typeof r=="number"&&r===27)return 1;break;case 28:if(typeof r=="number"&&r===28)return 1;break;case 29:if(typeof r=="number"&&r===29)return 1;break;case 30:if(typeof r=="number"&&r===30)return 1;break;case 31:if(typeof r=="number"&&r===31)return 1;break;case 32:if(typeof r=="number"&&r===32)return 1;break;default:if(typeof r=="number"&&r===33)return 1}}else switch(x[0]){case 0:if(typeof r!="number"&&r[0]===0){var t=r[2],u=x[2],i=k(ut[13],x[1],r[1]);return i&&Tr(u,t)}break;case 1:if(typeof r!="number"&&r[0]===1){var c=r[2],v=x[2],s=k(ut[12],x[1],r[1]);return s&&Tr(v,c)}break;case 2:if(typeof r!="number"&&r[0]===2){var l=r[1],p=x[1],d=l[4],T=l[3],b=l[2],C=p[4],N=p[3],I=p[2],F=k(ut[11],p[1],l[1]),L=F&&Tr(I,b),X=L&&Tr(N,T);return X&&(C===d?1:0)}break;case 3:if(typeof r!="number"&&r[0]===3){var q=r[1],J=x[1],e0=q[5],W=q[4],x0=q[3],i0=q[2],f0=J[5],r0=J[4],v0=J[3],o0=J[2],w0=k(ut[10],J[1],q[1]),t0=w0&&Tr(o0,i0),s0=t0&&Tr(v0,x0),h0=s0&&(r0===W?1:0);return h0&&(f0===e0?1:0)}break;case 4:if(typeof r!="number"&&r[0]===4){var p0=r[3],C0=r[2],j0=x[3],P0=x[2],M0=k(ut[9],x[1],r[1]),U0=M0&&Tr(P0,C0);return U0&&Tr(j0,p0)}break;case 5:if(typeof r!="number"&&r[0]===5){var T0=r[3],G0=r[2],k0=x[3],G=x[2],S0=k(ut[8],x[1],r[1]),Z0=S0&&Tr(G,G0);return Z0&&Tr(k0,T0)}break;case 6:if(typeof r!="number"&&r[0]===6){var N0=r[2],ux=x[2],ex=k(ut[7],x[1],r[1]);return ex&&Tr(ux,N0)}break;case 7:if(typeof r!="number"&&r[0]===7)return Tr(x[1],r[1]);break;case 8:if(typeof r!="number"&&r[0]===8){var nx=Tr(x[1],r[1]),px=r[2],D0=x[2];return nx&&k(ut[6],D0,px)}break;case 9:if(typeof r!="number"&&r[0]===9){var dx=r[3],_x=r[2],K=x[3],_0=x[2],U=k(ut[5],x[1],r[1]),m0=U&&Tr(_0,_x);return m0&&Tr(K,dx)}break;case 10:if(typeof r!="number"&&r[0]===10){var b0=r[3],y0=r[2],E0=x[3],$0=x[2],z=k(ut[4],x[1],r[1]),Dx=z&&Tr($0,y0);return Dx&&Tr(E0,b0)}break;case 11:if(typeof r!="number"&&r[0]===11)return k(ut[3],x[1],r[1]);break;case 12:if(typeof r!="number"&&r[0]===12){var Xx=r[3],K0=r[2],A=x[3],V=x[2],fx=k(ut[2],x[1],r[1]),wx=fx&&(V==K0?1:0);return wx&&Tr(A,Xx)}break;default:if(typeof r!="number"&&r[0]===13){var Ix=r[2],ox=x[2],xr=r[3],Fx=x[3],H0=k(ut[1],x[1],r[1]);if(H0){x:{if(ox){if(Ix){var ur=il(ox[1],Ix[1]);break x}}else if(!Ix){var ur=1;break x}var ur=0}var X0=ur}else var X0=H0;return X0&&Tr(Fx,xr)}}return 0}function XU(x,r){switch(x){case 0:if(!r)return 1;break;case 1:if(r===1)return 1;break;case 2:if(r===2)return 1;break;case 3:if(r===3)return 1;break;default:if(4<=r)return 1}return 0}function GU(x,r){switch(x){case 0:if(!r)return 1;break;case 1:if(r===1)return 1;break;default:if(2<=r)return 1}return 0}qr(ut,[0,GU,XU,function(x,r){if(x){if(r)return 1}else if(!r)return 1;return 0},mo,mo,mo,mo,mo,mo,mo,mo,GU,XU]);function YU(x){if(typeof x!="number")switch(x[0]){case 0:return qt0;case 1:return Ut0;case 2:return Bt0;case 3:return Xt0;case 4:return Gt0;case 5:return Yt0;case 6:return Jt0;case 7:return zt0;case 8:return Kt0;case 9:return Ht0;case 10:return Vt0;case 11:return $t0;case 12:return Wt0;default:return Qt0}var r=x;if(67<=r){if(pe<=r)switch(r){case 101:return it0;case 102:return ft0;case 103:return ct0;case 104:return at0;case 105:return st0;case 106:return ot0;case 107:return vt0;case 108:return lt0;case 109:return pt0;case 110:return kt0;case 111:return mt0;case 112:return dt0;case 113:return ht0;case 114:return yt0;case 115:return gt0;case 116:return _t0;case 117:return wt0;case 118:return bt0;case 119:return Tt0;case 120:return Et0;case 121:return At0;case 122:return St0;case 123:return Pt0;case 124:return It0;case 125:return Ct0;case 126:return Nt0;case 127:return Ot0;case 128:return jt0;case 129:return Dt0;case 130:return Ft0;case 131:return Rt0;case 132:return Mt0;default:return Lt0}switch(r){case 67:return Ae0;case 68:return Se0;case 69:return Pe0;case 70:return Ie0;case 71:return Ce0;case 72:return Ne0;case 73:return Oe0;case 74:return je0;case 75:return De0;case 76:return Fe0;case 77:return Re0;case 78:return Me0;case 79:return Le0;case 80:return qe0;case 81:return Ue0;case 82:return Be0;case 83:return Xe0;case 84:return Ge0;case 85:return Ye0;case 86:return Je0;case 87:return ze0;case 88:return Ke0;case 89:return He0;case 90:return Ve0;case 91:return $e0;case 92:return We0;case 93:return Qe0;case 94:return Ze0;case 95:return xt0;case 96:return rt0;case 97:return et0;case 98:return tt0;case 99:return nt0;default:return ut0}}if(34<=r)switch(r){case 34:return K20;case 35:return H20;case 36:return V20;case 37:return $20;case 38:return W20;case 39:return Q20;case 40:return Z20;case 41:return xe0;case 42:return re0;case 43:return ee0;case 44:return te0;case 45:return ne0;case 46:return ue0;case 47:return ie0;case 48:return fe0;case 49:return ce0;case 50:return ae0;case 51:return se0;case 52:return oe0;case 53:return ve0;case 54:return le0;case 55:return pe0;case 56:return ke0;case 57:return me0;case 58:return de0;case 59:return he0;case 60:return ye0;case 61:return ge0;case 62:return _e0;case 63:return we0;case 64:return be0;case 65:return Te0;default:return Ee0}switch(r){case 0:return l20;case 1:return p20;case 2:return k20;case 3:return m20;case 4:return d20;case 5:return h20;case 6:return y20;case 7:return g20;case 8:return _20;case 9:return w20;case 10:return b20;case 11:return T20;case 12:return E20;case 13:return A20;case 14:return S20;case 15:return P20;case 16:return I20;case 17:return C20;case 18:return N20;case 19:return O20;case 20:return j20;case 21:return D20;case 22:return F20;case 23:return R20;case 24:return M20;case 25:return L20;case 26:return q20;case 27:return U20;case 28:return B20;case 29:return X20;case 30:return G20;case 31:return Y20;case 32:return J20;default:return z20}}function QN(x){if(typeof x!="number")switch(x[0]){case 0:return x[2];case 1:return x[2];case 2:return x[1][3];case 3:var r=x[1],e=r[5],t=r[4],u=r[3];return t&&e?Jx(e20,Jx(u,r20)):t?Jx(n20,Jx(u,t20)):e?Jx(i20,Jx(u,u20)):Jx(c20,Jx(u,f20));case 4:return x[3];case 5:var i=x[2];return Jx(s20,Jx(i,Jx(a20,x[3])));case 6:return x[2];case 7:return x[1];case 8:return x[1];case 9:return x[3];case 10:return x[3];case 11:return x[1]?o20:v20;case 12:return x[3];default:return x[3]}var c=x;if(67<=c){if(pe<=c)switch(c){case 101:return w10;case 102:return b10;case 103:return T10;case 104:return E10;case 105:return A10;case 106:return S10;case 107:return P10;case 108:return I10;case 109:return C10;case 110:return N10;case 111:return O10;case 112:return j10;case 113:return D10;case 114:return F10;case 115:return R10;case 116:return M10;case 117:return L10;case 118:return q10;case 119:return U10;case 120:return B10;case 121:return X10;case 122:return G10;case 123:return Y10;case 124:return J10;case 125:return z10;case 126:return K10;case 127:return H10;case 128:return V10;case 129:return $10;case 130:return W10;case 131:return Q10;case 132:return Z10;default:return x20}switch(c){case 67:return Xr0;case 68:return Gr0;case 69:return Yr0;case 70:return Jr0;case 71:return zr0;case 72:return Kr0;case 73:return Hr0;case 74:return Vr0;case 75:return $r0;case 76:return Wr0;case 77:return Qr0;case 78:return Zr0;case 79:return x10;case 80:return r10;case 81:return e10;case 82:return t10;case 83:return n10;case 84:return u10;case 85:return i10;case 86:return f10;case 87:return c10;case 88:return a10;case 89:return s10;case 90:return o10;case 91:return v10;case 92:return l10;case 93:return p10;case 94:return k10;case 95:return m10;case 96:return d10;case 97:return h10;case 98:return y10;case 99:return g10;default:return _10}}if(34<=c)switch(c){case 34:return ar0;case 35:return sr0;case 36:return or0;case 37:return vr0;case 38:return lr0;case 39:return pr0;case 40:return kr0;case 41:return mr0;case 42:return dr0;case 43:return hr0;case 44:return yr0;case 45:return gr0;case 46:return _r0;case 47:return wr0;case 48:return br0;case 49:return Tr0;case 50:return Er0;case 51:return Ar0;case 52:return Sr0;case 53:return Pr0;case 54:return Ir0;case 55:return Cr0;case 56:return Nr0;case 57:return Or0;case 58:return jr0;case 59:return Dr0;case 60:return Fr0;case 61:return Rr0;case 62:return Mr0;case 63:return Lr0;case 64:return qr0;case 65:return Ur0;default:return Br0}switch(c){case 0:return Ix0;case 1:return Cx0;case 2:return Nx0;case 3:return Ox0;case 4:return jx0;case 5:return Dx0;case 6:return Fx0;case 7:return Rx0;case 8:return Mx0;case 9:return Lx0;case 10:return qx0;case 11:return Ux0;case 12:return Bx0;case 13:return Xx0;case 14:return Gx0;case 15:return Yx0;case 16:return Jx0;case 17:return zx0;case 18:return Kx0;case 19:return Hx0;case 20:return Vx0;case 21:return $x0;case 22:return Wx0;case 23:return Qx0;case 24:return Zx0;case 25:return xr0;case 26:return rr0;case 27:return er0;case 28:return tr0;case 29:return nr0;case 30:return ur0;case 31:return ir0;case 32:return fr0;default:return cr0}}function zd(x){return h(ar(Px0),x)}function ZN(x,r){var e=x?x[1]:0;x:{if(typeof r=="number"){if(br===r){var t=vx0,u=lx0;break x}}else switch(r[0]){case 3:var t=px0,u=kx0;break x;case 5:var t=mx0,u=dx0;break x;case 0:case 12:var t=yx0,u=gx0;break x;case 1:case 13:var t=_x0,u=wx0;break x;case 4:case 8:var t=Ex0,u=Ax0;break x;case 6:case 7:case 11:break;default:var t=bx0,u=Tx0;break x}var t=hx0,u=zd(QN(r))}return e?Jx(t,Jx(Sx0,u)):u}function PT0(x){return av<x?EI<x?-1:bI<x?d4<x?mS<x?yb<x?S_<x?1:8:fA<x?$w<x?Iy<x?1:8:FI<x?1:8:bb<x?qT<x?1:8:t_<x?1:8:I4<x?A4<x?D4<x?w4<x?im<x?Kk<x?h_<x?1:8:J9<x?1:8:O5<x?$y<x?1:8:cC<x?1:8:Ek<x?m8<x?vS<x?1:8:am<x?1:8:nm<x?oT<x?1:8:W_<x?1:8:xk<x?fy<x?s4<x?Dw<x?1:8:Nb<x?1:8:T4<x?C8<x?1:8:$A<x?1:8:O8<x?_4<x?mm<x?1:8:B4<x?1:8:$k<x?Um<x?1:8:Pk<x?1:8:Ig<x?v4<x?N8<x?qb<x?sm<x?1:8:s8<x?1:8:ok<x?sg<x?1:8:uw<x?1:8:S4<x?Xm<x?G8<x?1:8:K8<x?1:8:dC<x?rd<x?1:8:L8<x?1:8:bm<x?cm<x?Dm<x?E4<x?1:8:z8<x?1:8:x4<x?Qw<x?1:8:dS<x?1:8:V8<x?gC<x?l8<x?1:8:n4<x?1:8:Km<x?EE<x?1:8:L9<x?1:8:hP<x?Wg<x?ag<x?Uy<x?Tg<x?G5<x?1:8:Ab<x?1:8:bP<x?sC<x?1:8:Q5<x?1:8:d_<x?m9<x?kI<x?1:8:vC<x?1:8:Ky<x?S5<x?1:8:_T<x?1:8:DT<x?X9<x?zP<x?vI<x?1:8:PI<x?1:8:cg<x?r4<x?1:8:MP<x?1:8:w5<x?_C<x?Kg<x?1:8:1:8:W8<x?Qk<x?u4<x?uk<x?Vb<x?1:8:$E<x?1:8:g4<x?H_<x?1:8:Qb<x?1:8:fm<x?P4<x?TC<x?1:8:_y<x?1:8:ak<x?xg<x?1:8:Iw<x?1:8:j8<x?K4<x?rm<x?Pw<x?1:8:j9<x?1:8:bC<x?Ub<x?1:8:A_<x?1:8:Dk<x?HE<x?bE<x?1:8:_k<x?1:8:Em<x?zy<x?1:8:hC<x?1:8:ky<x?Qm<x?_w<x?td<x?Z8<x?q4<x?n8<x?zg<x?1:8:YA<x?1:8:Db<x?hE<x?1:8:cI<x?1:8:Nm<x?Lk<x?P_<x?1:8:eI<x?1:8:e8<x?pm<x?1:8:E_<x?1:8:fk<x?KI<x?LT<x?Mg<x?1:8:Bb<x?1:8:zI<x?Nk<x?1:8:PA<x?1:8:mE<x?o4<x?wA<x?1:8:KT<x?1:8:sy<x?Hw<x?1:8:a_<x?1:8:mg<x?ow<x?H9<x?DA<x?N5<x?1:8:ug<x?1:8:$9<x?iw<x?1:8:R9<x?1:8:Ce<x?__<x?NT<x?1:8:FP<x?1:8:Dg<x?QE<x?1:8:zw<x?1:8:$g<x?IT<x?jE<x?L5<x?1:8:LE<x?1:8:xS<x?Z5<x?1:8:yE<x?1:8:sA<x?Y4<x?f4<x?1:8:Zg<x?1:8:z9<x?jA<x?1:8:Z_<x?1:8:py<x?zS<x?U_<x?QI<x?VP<x?K_<x?1:8:PP<x?1:8:nb<x?YE<x?1:8:PT<x?1:8:Fg<x?r_<x?my<x?1:8:BA<x?1:8:eb<x?Qs<x?1:8:tw<x?1:8:VA<x?D1<x?f9<x?QS<x?1:8:iA<x?1:8:lA<x?tI<x?1:8:lS<x?1:8:QP<x?hA<x?OT<x?1:8:Tw<x?1:8:OI<x?GS<x?1:8:XS<x?1:8:q8<x?Zm<x?UT<x?fI<x?HP<x?1:8:TS<x?1:8:lT<x?kw<x?1:8:yA<x?1:8:qP<x?em<x?nA<x?1:8:dA<x?1:8:l_<x?gE<x?1:8:fP<x?1:8:Y9<x?eA<x?Vk<x?oP<x?1:8:Gg<x?1:8:mw<x?J_<x?1:8:Lb<x?1:8:ST<x?_8<x?tS<x?1:8:j_<x?1:8:k9<x?DE<x?1:8:zk<x?1:8:YP<x?kg<x?OP<x?I9<x?pA<x?US<x?bw<x?1:8:lk<x?1:8:m_<x?QA<x?1:8:hb<x?1:8:vm<x?fb<x?jk<x?1:8:h8<x?1:8:dP<x?E9<x?1:8:Ob<x?1:8:$5<x?qw<x?Eb<x?hI<x?1:8:ZP<x?1:8:uI<x?aE<x?1:8:tC<x?1:8:Ly<x?uy<x?eP<x?1:8:$_<x?1:8:oS<x?WT<x?1:8:Gb<x?1:8:Bg<x?HI<x?YI<x?rw<x?Vy<x?1:8:fC<x?1:8:DI<x?pb<x?1:8:Xb<x?1:8:YT<x?db<x?wS<x?1:8:Yb<x?1:8:V_<x?Bk<x?1:8:K5<x?1:8:vT<x?CE<x?sb<x?WE<x?1:8:qy<x?1:8:d8<x?Rk<x?1:8:kA<x?1:8:F4<x?kP<x?T9<x?1:8:cw<x?1:8:vg<x?Vw<x?1:8:F9<x?1:8:f8<x?JP<x?qm<x?uE<x?pg<x?_P<x?1:8:y4<x?1:8:Hb<x?eT<x?1:8:MS<x?1:8:S8<x?_m<x?MA<x?1:8:NI<x?1:8:aC<x?cT<x?1:8:bx<x?1:8:i8<x?eg<x?c4<x?fT<x?1:8:V9<x?1:8:zm<x?LA<x?1:8:A5<x?1:8:u8<x?sk<x?s9<x?1:8:tm<x?1:8:uA<x?AA<x?1:8:OE<x?1:8:TA<x?Xw<x?c8<x?Eg<x?x9<x?1:8:Xk<x?1:8:O9<x?mb<x?1:8:Wy<x?1:8:Gw<x?gy<x?Ib<x?1:8:b8<x?1:8:qA<x?vw<x?1:8:ck<x?1:8:PS<x?v9<x?j5<x?m4<x?1:8:fg<x?1:8:C9<x?Kb<x?1:8:Fb<x?1:8:F8<x?Yx<x?Pb<x?1:8:CI<x?1:8:sI<x?Nw<x?1:8:VE<x?1:8:f_<x?zb<x?R_<x?om<x?KS<x?nS<x?BI<x?jI<x?ww<x?1:8:AT<x?1:8:$I<x?BT<x?1:8:k8<x?1:8:xT<x?kE<x?RP<x?1:8:lC<x?1:8:cb<x?iC<x?1:8:yC<x?1:8:JS<x?Ty<x?TI<x?ey<x?1:8:LS<x?1:8:XP<x?SC<x?1:8:wy<x?1:8:e_<x?WS<x?UI<x?1:8:D5<x?1:8:IP<x?Jb<x?1:8:mC<x?1:8:EP<x?Sm<x?SA<x?jP<x?nT<x?1:8:b5<x?1:8:p_<x?Hg<x?1:8:ly<x?1:8:V5<x?mk<x?og<x?1:8:LP<x?1:8:SP<x?J8<x?1:8:Ag<x?1:8:b4<x?gg<x?wC<x?w_<x?1:8:FE<x?1:8:DP<x?Rg<x?1:8:AP<x?1:8:_b<x?yS<x?Lw<x?1:8:H5<x?1:8:by<x?cy<x?1:8:Oy<x?1:8:WA<x?pC<x?Ny<x?X8<x?P5<x?Pm<x?1:8:dT<x?1:8:Ck<x?wg<x?1:8:X4<x?1:8:HT<x?KE<x?CA<x?1:8:sT<x?1:8:A9<x?gw<x?1:8:oI<x?1:8:pE<x?Ug<x?xC<x?wP<x?1:8:Uw<x?1:8:b9<x?zT<x?1:8:xy<x?1:8:My<x?TE<x?hw<x?1:8:IA<x?1:8:gA<x?M9<x?1:8:qg<x?1:8:tP<x?HA<x?tT<x?gP<x?VT<x?1:8:c9<x?1:8:pT<x?E5<x?1:8:Ay<x?1:8:N9<x?II<x?LI<x?1:8:zA<x?1:8:vE<x?xE<x?1:8:1:UA<x?B8<x?ob<x?8:ZI<x?1:8:GE<x?qE<x?1:8:aA<x?1:8:P9<x?ZS<x?vA<x?1:8:x_<x?1:8:$b<x?1:8:xd<x?AE<x?iT<x?uC<x?EC<x?8:c_<x?Yg<x?1:8:Q_<x?1:8:gT<x?NP<x?DS<x?1:8:jS<x?1:8:Ry<x?pP<x?1:8:wT<x?1:8:Y8<x?Z4<x?rk<x?v_<x?1:8:NA<x?1:8:Cm<x?KP<x?1:8:UE<x?1:8:CT<x?$m<x?X5<x?1:8:$S<x?1:8:lg<x?CP<x?1:8:l9<x?1:8:xI<x?CS<x?_S<x?a9<x?dE<x?1:8:1:8:JE<x?8:Ym<x?uS<x?1:8:B9<x?1:8:Y5<x?P8<x?xv<x?Ev<x?1:2:d9<x?1:8:ZE<x?jT<x?1:8:XT<x?1:8:FA<x?lI<x?kT<x?1:8:Ng<x?1:8:u9<x?sw<x?1:8:bA<x?1:8:L_<x?nI<x?xm<x?G4<x?M8<x?Yy<x?1:8:ib<x?1:8:Vm<x?Sk<x?1:8:NS<x?1:8:Ww<x?p4<x?n9<x?1:8:M_<x?1:8:HS<x?Tk<x?1:8:C5<x?1:8:GI<x?Jy<x?R5<x?nP<x?1:8:Zb<x?1:8:Ow<x?g_<x?1:8:ew<x?1:8:vb<x?cP<x?w9<x?1:8:Gy<x?1:8:uT<x?1:8:FS<x?Jk<x?i4<x?X_<x?1:8:Im<x?8:Y_<x?1:8:Cw<x?Wm<x?XI<x?1:8:yP<x?1:8:q_<x?B5<x?1:8:FT<x?1:8:sP<x?$P<x?rb<x?aS<x?1:8:1:G_<x?8:hT<x?1:8:YS<x?ty<x?1:8:mP<x?vP<x?1:8:Py<x?1:8:o_<x?Yk<x?eS<x?vy<x?lb<x?rI<x?ub<x?lE<x?1:8:G9<x?1:8:Pg<x?Rw<x?1:8:XE<x?1:8:JA<x?sS<x?Lm<x?1:8:1:8:BE<x?I_<x?Ie<x?Wb<x?1:8:xP<x?1:8:k4<x?Sw<x?1:8:eC<x?1:8:Og<x?Cy<x?1:8:ny<x?ng<x?1:8:Am<x?1:8:Zy<x?$T<x?Fw<x?aP<x?lP<x?1:8:Jg<x?1:8:IS<x?KA<x?1:8:_5<x?1:8:pI<x?hk<x?jw<x?1:8:Ak<x?1:8:aw<x?gb<x?1:8:Vg<x?1:8:Cb<x?nw<x?UP<x?bT<x?1:8:Lg<x?1:8:rE<x?i9<x?1:8:hy<x?1:8:q5<x&&S9<x?1:8:p9<x?n_<x?g9<x?WP<x?8:PE<x?Sb<x?1:8:iS<x?1:8:wE<x?Aw<x?bg<x?1:8:1:8:T5<x?B_<x&&uP<x?1:8:oy<x?ig<x?ry<x?1:8:1:b_<x?8:1:s_<x?Xg<x?N4<x?8:xw<x?1:8:kC<x?EA<x?M5<x?1:8:Qg<x?1:8:_9<x?1:8:Rb<x?Dy<x?J5<x?1:8:Tb<x?1:8:iI<x?tg<x?8:t9<x?1:8:RA<x?JT<x?1:8:TT<x?1:8:ZT<x?fS<x?nC<x?$8<x?RI<x?g8<x?qS<x?1:8:lw<x?1:8:hg<x?1:8:aT<x?gm<x?By<x?1:8:1:8:Bw<x?MI<x?AC<x?JI<x?1:8:1:8:dg<x?R8<x?Ew<x?1:8:yy<x?1:8:SS<x?1:8:Jm<x?t4<x?kv<x?zE<x?8:lv<x?1:2:U8<x?RS<x?1:8:wI<x?1:8:kk<x?wm<x?BP<x?1:8:fE<x?1:8:Qp<x?T_<x?1:8:oC<x?1:8:yw<x?Yw<x?Gm<x?MT<x?1:8:dy<x?1:8:U4<x?oE<x?1:8:Fk<x?1:8:Cg<x?RT<x?yg<x?1:8:v8<x?1:8:Bm<x?Qa<x?1:8:AI<x?1:8:T8<x?RE<x?fw<x?h9<x?GT<x?ay<x?1:8:jy<x?1:8:IE<x?1:8:iP<x?tb<x?8:1:8:jb<x?ab<x?gS<x?vk<x?1:8:rg<x?1:8:ZA<x?SE<x?1:8:1:8:hS<x?k_<x?w8<x?$4<x?ek<x?1:8:D8<x?1:8:Jw<x?Wk<x?1:8:1:j4<x?cS<x?8:z4<x?1:8:cA<x?nE<x?1:8:y8<x?1:8:GA<x?e9<x?_g<x?Hm<x?1:8:z_<x?1:8:z5<x?Mk<x?1:8:lm<x?1:8:cv<x?iv<x?yv<x?1:2:ov<x?1:2:m2<x?n2<x?1:3:tv<x?1:2:J0(`\x07\b	
\v\x07\f\r\x1B																										 	!																										"#$%																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																													`,x+1|0)-1|0}function xO(x){return 45<x?46<x?-1:0:-1}function Fa(x){return 8<x?SI<x?av<x?Ev<x?-1:lv<x?xv<x?0:-1:ov<x?iv<x?yv<x?kv<x?0:-1:0:-1:tv<x?cv<x?0:-1:0:-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x-9|0)-1|0:-1}function JU(x){return 47<x?Cf<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+t2|0)-1|0:-1}function dr(x){return 47<x?57<x?-1:0:-1}function Er(x){return 47<x?E2<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+t2|0)-1|0:-1}function Gt(x){return 47<x?B2<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+t2|0)-1|0:-1}function zU(x){return 47<x?59<x?-1:J0("\0",x+t2|0)-1|0:-1}function Yt(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function Kd(x){return 87<x?Cf<x?-1:J0(VS,x+lD|0)-1|0:-1}function ho(x){return 45<x?57<x?-1:J0("\0",x+fo|0)-1|0:-1}function rO(x){return-1<x?o1<x?pn<x?m2<x?n2<x?0:-1:0:-1:J0("\0\0\0\0",x)-1|0:-1}function KU(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function Bv(x){return 47<x?to<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+t2|0)-1|0:-1}function yl(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function Hd(x){return 45<x?pe<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+fo|0)-1|0:-1}function HU(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function Vd(x){return 47<x?95<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+t2|0)-1|0:-1}function $d(x){return 47<x?B2<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+t2|0)-1|0:-1}function Wd(x){return 47<x?B2<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+t2|0)-1|0:-1}function Qd(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function Zd(x){return 8<x?SI<x?av<x?Ev<x?-1:lv<x?xv<x?0:-1:ov<x?iv<x?yv<x?kv<x?0:-1:0:-1:tv<x?cv<x?0:-1:0:-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x-9|0)-1|0:-1}function Ra(x){return 47<x?49<x?-1:0:-1}function xh(x){return 47<x?95<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+t2|0)-1|0:-1}function yo(x){return 47<x?57<x?-1:J0("",x+t2|0)-1|0:-1}function rh(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function eO(x){return br<x?gv<x?-1:0:-1}function Nn(x){return 60<x?61<x?-1:0:-1}function gl(x){return 47<x?B2<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+t2|0)-1|0:-1}function eh(x){return 47<x?B2<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+t2|0)-1|0:-1}function tO(x){return 60<x?62<x?-1:J0(r8,x+XM|0)-1|0:-1}function th(x){return 65<x?98<x?-1:J0(VS,x-66|0)-1|0:-1}function Y1(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function nh(x){return gv<x?Tv<x?-1:0:-1}function te(x){return 47<x?55<x?-1:0:-1}function uh(x){return lt<x?B2<x?-1:0:-1}function ih(x){return B2<x?U2<x?-1:0:-1}function op(x){return 98<x?99<x?-1:0:-1}function Fe(x){return 47<x?48<x?-1:0:-1}function fh(x){return 45<x?pe<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+fo|0)-1|0:-1}function ch(x){return 78<x?U2<x?-1:J0(VS,x-79|0)-1|0:-1}function VU(x){return 41<x?42<x?-1:0:-1}function $U(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function ah(x){return 47<x?pe<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+t2|0)-1|0:-1}function go(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function WU(x){return 41<x?61<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+F_|0)-1|0:-1}function QU(x){return 44<x?45<x?-1:0:-1}function ZU(x){return We<x?vn<x?-1:0:-1}function sh(x){return q1<x?Sv<x?-1:0:-1}function nO(x){return 99<x?E1<x?-1:0:-1}function oh(x){return 47<x?E2<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+t2|0)-1|0:-1}function vp(x){return ke<x?br<x?-1:0:-1}function _l(x){return 45<x?57<x?-1:J0("\0",x+fo|0)-1|0:-1}function xB(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function Xv(x){return 47<x?pn<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+t2|0)-1|0:-1}function rB(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x07\b\0\0\0\0\0\0	\x07\b",x+d2|0)-1|0:-1}function Re(x){return 9<x?10<x?-1:0:-1}function eB(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function tB(x){return 96<x?97<x?-1:0:-1}function Ma(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function vh(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function _o(x){return 47<x?95<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+t2|0)-1|0:-1}function nB(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function Gv(x){return E1<x?pe<x?-1:0:-1}function uB(x){return 58<x?59<x?-1:0:-1}function iB(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function lh(x){return 41<x?47<x?-1:J0("\0\0\0\0",x+F_|0)-1|0:-1}function ph(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function fB(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function cB(x){return g6<x?D3<x?-1:0:-1}function kh(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function ye(x){return 47<x?pe<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+t2|0)-1|0:-1}function mh(x){return 42<x?57<x?-1:J0("\0\0\0",x+Mw|0)-1|0:-1}function aB(x){return 47<x?E2<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+t2|0)-1|0:-1}function wo(x){return 45<x?95<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+fo|0)-1|0:-1}function bo(x){return Tv<x?Q3<x?-1:0:-1}function sB(x){return 46<x?47<x?-1:0:-1}function oB(x){return 57<x?58<x?-1:0:-1}function IT0(x){return av<x?EI<x?-1:bI<x?d4<x?mS<x?yb<x?S_<x?1:6:fA<x?$w<x?Iy<x?1:6:FI<x?1:6:bb<x?qT<x?1:6:t_<x?1:6:I4<x?A4<x?D4<x?w4<x?im<x?Kk<x?h_<x?1:6:J9<x?1:6:O5<x?$y<x?1:6:cC<x?1:6:Ek<x?m8<x?vS<x?1:6:am<x?1:6:nm<x?oT<x?1:6:W_<x?1:6:xk<x?fy<x?s4<x?Dw<x?1:6:Nb<x?1:6:T4<x?C8<x?1:6:$A<x?1:6:O8<x?_4<x?mm<x?1:6:B4<x?1:6:$k<x?Um<x?1:6:Pk<x?1:6:Ig<x?v4<x?N8<x?qb<x?sm<x?1:6:s8<x?1:6:ok<x?sg<x?1:6:uw<x?1:6:S4<x?Xm<x?G8<x?1:6:K8<x?1:6:dC<x?rd<x?1:6:L8<x?1:6:bm<x?cm<x?Dm<x?E4<x?1:6:z8<x?1:6:x4<x?Qw<x?1:6:dS<x?1:6:V8<x?gC<x?l8<x?1:6:n4<x?1:6:Km<x?EE<x?1:6:L9<x?1:6:hP<x?Wg<x?ag<x?Uy<x?Tg<x?G5<x?1:6:Ab<x?1:6:bP<x?sC<x?1:6:Q5<x?1:6:d_<x?m9<x?kI<x?1:6:vC<x?1:6:Ky<x?S5<x?1:6:_T<x?1:6:DT<x?X9<x?zP<x?vI<x?1:6:PI<x?1:6:cg<x?r4<x?1:6:MP<x?1:6:w5<x?_C<x?Kg<x?1:6:1:6:W8<x?Qk<x?u4<x?uk<x?Vb<x?1:6:$E<x?1:6:g4<x?H_<x?1:6:Qb<x?1:6:fm<x?P4<x?TC<x?1:6:_y<x?1:6:ak<x?xg<x?1:6:Iw<x?1:6:j8<x?K4<x?rm<x?Pw<x?1:6:j9<x?1:6:bC<x?Ub<x?1:6:A_<x?1:6:Dk<x?HE<x?bE<x?1:6:_k<x?1:6:Em<x?zy<x?1:6:hC<x?1:6:ky<x?Qm<x?_w<x?td<x?Z8<x?q4<x?n8<x?zg<x?1:6:YA<x?1:6:Db<x?hE<x?1:6:cI<x?1:6:Nm<x?Lk<x?P_<x?1:6:eI<x?1:6:e8<x?pm<x?1:6:E_<x?1:6:fk<x?KI<x?LT<x?Mg<x?1:6:Bb<x?1:6:zI<x?Nk<x?1:6:PA<x?1:6:mE<x?o4<x?wA<x?1:6:KT<x?1:6:sy<x?Hw<x?1:6:a_<x?1:6:mg<x?ow<x?H9<x?DA<x?N5<x?1:6:ug<x?1:6:$9<x?iw<x?1:6:R9<x?1:6:Ce<x?__<x?NT<x?1:6:FP<x?1:6:Dg<x?QE<x?1:6:zw<x?1:6:$g<x?IT<x?jE<x?L5<x?1:6:LE<x?1:6:xS<x?Z5<x?1:6:yE<x?1:6:sA<x?Y4<x?f4<x?1:6:Zg<x?1:6:z9<x?jA<x?1:6:Z_<x?1:6:py<x?zS<x?U_<x?QI<x?VP<x?K_<x?1:6:PP<x?1:6:nb<x?YE<x?1:6:PT<x?1:6:Fg<x?r_<x?my<x?1:6:BA<x?1:6:eb<x?Qs<x?1:6:tw<x?1:6:VA<x?D1<x?f9<x?QS<x?1:6:iA<x?1:6:lA<x?tI<x?1:6:lS<x?1:6:QP<x?hA<x?OT<x?1:6:Tw<x?1:6:OI<x?GS<x?1:6:XS<x?1:6:q8<x?Zm<x?UT<x?fI<x?HP<x?1:6:TS<x?1:6:lT<x?kw<x?1:6:yA<x?1:6:qP<x?em<x?nA<x?1:6:dA<x?1:6:l_<x?gE<x?1:6:fP<x?1:6:Y9<x?eA<x?Vk<x?oP<x?1:6:Gg<x?1:6:mw<x?J_<x?1:6:Lb<x?1:6:ST<x?_8<x?tS<x?1:6:j_<x?1:6:k9<x?DE<x?1:6:zk<x?1:6:YP<x?kg<x?OP<x?I9<x?pA<x?US<x?bw<x?1:6:lk<x?1:6:m_<x?QA<x?1:6:hb<x?1:6:vm<x?fb<x?jk<x?1:6:h8<x?1:6:dP<x?E9<x?1:6:Ob<x?1:6:$5<x?qw<x?Eb<x?hI<x?1:6:ZP<x?1:6:uI<x?aE<x?1:6:tC<x?1:6:Ly<x?uy<x?eP<x?1:6:$_<x?1:6:oS<x?WT<x?1:6:Gb<x?1:6:Bg<x?HI<x?YI<x?rw<x?Vy<x?1:6:fC<x?1:6:DI<x?pb<x?1:6:Xb<x?1:6:YT<x?db<x?wS<x?1:6:Yb<x?1:6:V_<x?Bk<x?1:6:K5<x?1:6:vT<x?CE<x?sb<x?WE<x?1:6:qy<x?1:6:d8<x?Rk<x?1:6:kA<x?1:6:F4<x?kP<x?T9<x?1:6:cw<x?1:6:vg<x?Vw<x?1:6:F9<x?1:6:f8<x?JP<x?qm<x?uE<x?pg<x?_P<x?1:6:y4<x?1:6:Hb<x?eT<x?1:6:MS<x?1:6:S8<x?_m<x?MA<x?1:6:NI<x?1:6:aC<x?cT<x?1:6:bx<x?1:6:i8<x?eg<x?c4<x?fT<x?1:6:V9<x?1:6:zm<x?LA<x?1:6:A5<x?1:6:u8<x?sk<x?s9<x?1:6:tm<x?1:6:uA<x?AA<x?1:6:OE<x?1:6:TA<x?Xw<x?c8<x?Eg<x?x9<x?1:6:Xk<x?1:6:O9<x?mb<x?1:6:Wy<x?1:6:Gw<x?gy<x?Ib<x?1:6:b8<x?1:6:qA<x?vw<x?1:6:ck<x?1:6:PS<x?v9<x?j5<x?m4<x?1:6:fg<x?1:6:C9<x?Kb<x?1:6:Fb<x?1:6:F8<x?Yx<x?Pb<x?1:6:CI<x?1:6:sI<x?Nw<x?1:6:VE<x?1:6:f_<x?zb<x?R_<x?om<x?KS<x?nS<x?BI<x?jI<x?ww<x?1:6:AT<x?1:6:$I<x?BT<x?1:6:k8<x?1:6:xT<x?kE<x?RP<x?1:6:lC<x?1:6:cb<x?iC<x?1:6:yC<x?1:6:JS<x?Ty<x?TI<x?ey<x?1:6:LS<x?1:6:XP<x?SC<x?1:6:wy<x?1:6:e_<x?WS<x?UI<x?1:6:D5<x?1:6:IP<x?Jb<x?1:6:mC<x?1:6:EP<x?Sm<x?SA<x?jP<x?nT<x?1:6:b5<x?1:6:p_<x?Hg<x?1:6:ly<x?1:6:V5<x?mk<x?og<x?1:6:LP<x?1:6:SP<x?J8<x?1:6:Ag<x?1:6:b4<x?gg<x?wC<x?w_<x?1:6:FE<x?1:6:DP<x?Rg<x?1:6:AP<x?1:6:_b<x?yS<x?Lw<x?1:6:H5<x?1:6:by<x?cy<x?1:6:Oy<x?1:6:WA<x?pC<x?Ny<x?X8<x?P5<x?Pm<x?1:6:dT<x?1:6:Ck<x?wg<x?1:6:X4<x?1:6:HT<x?KE<x?CA<x?1:6:sT<x?1:6:A9<x?gw<x?1:6:oI<x?1:6:pE<x?Ug<x?xC<x?wP<x?1:6:Uw<x?1:6:b9<x?zT<x?1:6:xy<x?1:6:My<x?TE<x?hw<x?1:6:IA<x?1:6:gA<x?M9<x?1:6:qg<x?1:6:tP<x?HA<x?tT<x?gP<x?VT<x?1:6:c9<x?1:6:pT<x?E5<x?1:6:Ay<x?1:6:N9<x?II<x?LI<x?1:6:zA<x?1:6:vE<x?xE<x?1:6:1:UA<x?B8<x?ob<x?6:ZI<x?1:6:GE<x?qE<x?1:6:aA<x?1:6:P9<x?ZS<x?vA<x?1:6:x_<x?1:6:$b<x?1:6:xd<x?AE<x?iT<x?uC<x?EC<x?6:c_<x?Yg<x?1:6:Q_<x?1:6:gT<x?NP<x?DS<x?1:6:jS<x?1:6:Ry<x?pP<x?1:6:wT<x?1:6:Y8<x?Z4<x?rk<x?v_<x?1:6:NA<x?1:6:Cm<x?KP<x?1:6:UE<x?1:6:CT<x?$m<x?X5<x?1:6:$S<x?1:6:lg<x?CP<x?1:6:l9<x?1:6:xI<x?CS<x?_S<x?a9<x?dE<x?1:6:1:6:JE<x?6:Ym<x?uS<x?1:6:B9<x?1:6:Y5<x?P8<x?xv<x?Ev<x?1:2:d9<x?1:6:ZE<x?jT<x?1:6:XT<x?1:6:FA<x?lI<x?kT<x?1:6:Ng<x?1:6:u9<x?sw<x?1:6:bA<x?1:6:L_<x?nI<x?xm<x?G4<x?M8<x?Yy<x?1:6:ib<x?1:6:Vm<x?Sk<x?1:6:NS<x?1:6:Ww<x?p4<x?n9<x?1:6:M_<x?1:6:HS<x?Tk<x?1:6:C5<x?1:6:GI<x?Jy<x?R5<x?nP<x?1:6:Zb<x?1:6:Ow<x?g_<x?1:6:ew<x?1:6:vb<x?cP<x?w9<x?1:6:Gy<x?1:6:uT<x?1:6:FS<x?Jk<x?i4<x?X_<x?1:6:Im<x?6:Y_<x?1:6:Cw<x?Wm<x?XI<x?1:6:yP<x?1:6:q_<x?B5<x?1:6:FT<x?1:6:sP<x?$P<x?rb<x?aS<x?1:6:1:G_<x?6:hT<x?1:6:YS<x?ty<x?1:6:mP<x?vP<x?1:6:Py<x?1:6:o_<x?Yk<x?eS<x?vy<x?lb<x?rI<x?ub<x?lE<x?1:6:G9<x?1:6:Pg<x?Rw<x?1:6:XE<x?1:6:JA<x?sS<x?Lm<x?1:6:1:6:BE<x?I_<x?Ie<x?Wb<x?1:6:xP<x?1:6:k4<x?Sw<x?1:6:eC<x?1:6:Og<x?Cy<x?1:6:ny<x?ng<x?1:6:Am<x?1:6:Zy<x?$T<x?Fw<x?aP<x?lP<x?1:6:Jg<x?1:6:IS<x?KA<x?1:6:_5<x?1:6:pI<x?hk<x?jw<x?1:6:Ak<x?1:6:aw<x?gb<x?1:6:Vg<x?1:6:Cb<x?nw<x?UP<x?bT<x?1:6:Lg<x?1:6:rE<x?i9<x?1:6:hy<x?1:6:q5<x&&S9<x?1:6:p9<x?n_<x?g9<x?WP<x?6:PE<x?Sb<x?1:6:iS<x?1:6:wE<x?Aw<x?bg<x?1:6:1:6:T5<x?B_<x&&uP<x?1:6:oy<x?ig<x?ry<x?1:6:1:b_<x?6:1:s_<x?Xg<x?N4<x?6:xw<x?1:6:kC<x?EA<x?M5<x?1:6:Qg<x?1:6:_9<x?1:6:Rb<x?Dy<x?J5<x?1:6:Tb<x?1:6:iI<x?tg<x?6:t9<x?1:6:RA<x?JT<x?1:6:TT<x?1:6:ZT<x?fS<x?nC<x?$8<x?RI<x?g8<x?qS<x?1:6:lw<x?1:6:hg<x?1:6:aT<x?gm<x?By<x?1:6:1:6:Bw<x?MI<x?AC<x?JI<x?1:6:1:6:dg<x?R8<x?Ew<x?1:6:yy<x?1:6:SS<x?1:6:Jm<x?t4<x?kv<x?zE<x?6:lv<x?1:2:U8<x?RS<x?1:6:wI<x?1:6:kk<x?wm<x?BP<x?1:6:fE<x?1:6:Qp<x?T_<x?1:6:oC<x?1:6:yw<x?Yw<x?Gm<x?MT<x?1:6:dy<x?1:6:U4<x?oE<x?1:6:Fk<x?1:6:Cg<x?RT<x?yg<x?1:6:v8<x?1:6:Bm<x?Qa<x?1:6:AI<x?1:6:T8<x?RE<x?fw<x?h9<x?GT<x?ay<x?1:6:jy<x?1:6:IE<x?1:6:iP<x?tb<x?6:1:6:jb<x?ab<x?gS<x?vk<x?1:6:rg<x?1:6:ZA<x?SE<x?1:6:1:6:hS<x?k_<x?w8<x?$4<x?ek<x?1:6:D8<x?1:6:Jw<x?Wk<x?1:6:1:j4<x?cS<x?6:z4<x?1:6:cA<x?nE<x?1:6:y8<x?1:6:GA<x?e9<x?_g<x?Hm<x?1:6:z_<x?1:6:z5<x?Mk<x?1:6:lm<x?1:6:cv<x?iv<x?yv<x?1:2:ov<x?1:2:m2<x?n2<x?1:3:tv<x?1:2:J0(`\x07\b	
\v\f\r\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x1B\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07 \x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07\x07`,x+1|0)-1|0}function sr(x){return 35<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x+d2|0)-1|0:-1}function vB(x){return 34<x?o1<x?-1:J0("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0",x-35|0)-1|0:-1}function CT0(x){return av<x?EI<x?-1:bI<x?d4<x?mS<x?yb<x?S_<x?1:6:fA<x?$w<x?Iy<x?1:6:FI<x?1:6:bb<x?qT<x?1:6:t_<x?1:6:I4<x?A4<x?D4<x?w4<x?im<x?Kk<x?h_<x?1:6:J9<x?1:6:O5<x?$y<x?1:6:cC<x?1:6:Ek<x?m8<x?vS<x?1:6:am<x?1:6:nm<x?oT<x?1:6:W_<x?1:6:xk<x?fy<x?s4<x?Dw<x?1:6:Nb<x?1:6:T4<x?C8<x?1:6:$A<x?1:6:O8<x?_4<x?mm<x?1:6:B4<x?1:6:$k<x?Um<x?1:6:Pk<x?1:6:Ig<x?v4<x?N8<x?qb<x?sm<x?1:6:s8<x?1:6:ok<x?sg<x?1:6:uw<x?1:6:S4<x?Xm<x?G8<x?1:6:K8<x?1:6:dC<x?rd<x?1:6:L8<x?1:6:bm<x?cm<x?Dm<x?E4<x?1:6:z8<x?1:6:x4<x?Qw<x?1:6:dS<x?1:6:V8<x?gC<x?l8<x?1:6:n4<x?1:6:Km<x?EE<x?1:6:L9<x?1:6:hP<x?Wg<x?ag<x?Uy<x?Tg<x?G5<x?1:6:Ab<x?1:6:bP<x?sC<x?1:6:Q5<x?1:6:d_<x?m9<x?kI<x?1:6:vC<x?1:6:Ky<x?S5<x?1:6:_T<x?1:6:DT<x?X9<x?zP<x?vI<x?1:6:PI<x?1:6:cg<x?r4<x?1:6:MP<x?1:6:w5<x?_C<x?Kg<x?1:6:1:6:W8<x?Qk<x?u4<x?uk<x?Vb<x?1:6:$E<x?1:6:g4<x?H_<x?1:6:Qb<x?1:6:fm<x?P4<x?TC<x?1:6:_y<x?1:6:ak<x?xg<x?1:6:Iw<x?1:6:j8<x?K4<x?rm<x?Pw<x?1:6:j9<x?1:6:bC<x?Ub<x?1:6:A_<x?1:6:Dk<x?HE<x?bE<x?1:6:_k<x?1:6:Em<x?zy<x?1:6:hC<x?1:6:ky<x?Qm<x?_w<x?td<x?Z8<x?q4<x?n8<x?zg<x?1:6:YA<x?1:6:Db<x?hE<x?1:6:cI<x?1:6:Nm<x?Lk<x?P_<x?1:6:eI<x?1:6:e8<x?pm<x?1:6:E_<x?1:6:fk<x?KI<x?LT<x?Mg<x?1:6:Bb<x?1:6:zI<x?Nk<x?1:6:PA<x?1:6:mE<x?o4<x?wA<x?1:6:KT<x?1:6:sy<x?Hw<x?1:6:a_<x?1:6:mg<x?ow<x?H9<x?DA<x?N5<x?1:6:ug<x?1:6:$9<x?iw<x?1:6:R9<x?1:6:Ce<x?__<x?NT<x?1:6:FP<x?1:6:Dg<x?QE<x?1:6:zw<x?1:6:$g<x?IT<x?jE<x?L5<x?1:6:LE<x?1:6:xS<x?Z5<x?1:6:yE<x?1:6:sA<x?Y4<x?f4<x?1:6:Zg<x?1:6:z9<x?jA<x?1:6:Z_<x?1:6:py<x?zS<x?U_<x?QI<x?VP<x?K_<x?1:6:PP<x?1:6:nb<x?YE<x?1:6:PT<x?1:6:Fg<x?r_<x?my<x?1:6:BA<x?1:6:eb<x?Qs<x?1:6:tw<x?1:6:VA<x?D1<x?f9<x?QS<x?1:6:iA<x?1:6:lA<x?tI<x?1:6:lS<x?1:6:QP<x?hA<x?OT<x?1:6:Tw<x?1:6:OI<x?GS<x?1:6:XS<x?1:6:q8<x?Zm<x?UT<x?fI<x?HP<x?1:6:TS<x?1:6:lT<x?kw<x?1:6:yA<x?1:6:qP<x?em<x?nA<x?1:6:dA<x?1:6:l_<x?gE<x?1:6:fP<x?1:6:Y9<x?eA<x?Vk<x?oP<x?1:6:Gg<x?1:6:mw<x?J_<x?1:6:Lb<x?1:6:ST<x?_8<x?tS<x?1:6:j_<x?1:6:k9<x?DE<x?1:6:zk<x?1:6:YP<x?kg<x?OP<x?I9<x?pA<x?US<x?bw<x?1:6:lk<x?1:6:m_<x?QA<x?1:6:hb<x?1:6:vm<x?fb<x?jk<x?1:6:h8<x?1:6:dP<x?E9<x?1:6:Ob<x?1:6:$5<x?qw<x?Eb<x?hI<x?1:6:ZP<x?1:6:uI<x?aE<x?1:6:tC<x?1:6:Ly<x?uy<x?eP<x?1:6:$_<x?1:6:oS<x?WT<x?1:6:Gb<x?1:6:Bg<x?HI<x?YI<x?rw<x?Vy<x?1:6:fC<x?1:6:DI<x?pb<x?1:6:Xb<x?1:6:YT<x?db<x?wS<x?1:6:Yb<x?1:6:V_<x?Bk<x?1:6:K5<x?1:6:vT<x?CE<x?sb<x?WE<x?1:6:qy<x?1:6:d8<x?Rk<x?1:6:kA<x?1:6:F4<x?kP<x?T9<x?1:6:cw<x?1:6:vg<x?Vw<x?1:6:F9<x?1:6:f8<x?JP<x?qm<x?uE<x?pg<x?_P<x?1:6:y4<x?1:6:Hb<x?eT<x?1:6:MS<x?1:6:S8<x?_m<x?MA<x?1:6:NI<x?1:6:aC<x?cT<x?1:6:bx<x?1:6:i8<x?eg<x?c4<x?fT<x?1:6:V9<x?1:6:zm<x?LA<x?1:6:A5<x?1:6:u8<x?sk<x?s9<x?1:6:tm<x?1:6:uA<x?AA<x?1:6:OE<x?1:6:TA<x?Xw<x?c8<x?Eg<x?x9<x?1:6:Xk<x?1:6:O9<x?mb<x?1:6:Wy<x?1:6:Gw<x?gy<x?Ib<x?1:6:b8<x?1:6:qA<x?vw<x?1:6:ck<x?1:6:PS<x?v9<x?j5<x?m4<x?1:6:fg<x?1:6:C9<x?Kb<x?1:6:Fb<x?1:6:F8<x?Yx<x?Pb<x?1:6:CI<x?1:6:sI<x?Nw<x?1:6:VE<x?1:6:f_<x?zb<x?R_<x?om<x?KS<x?nS<x?BI<x?jI<x?ww<x?1:6:AT<x?1:6:$I<x?BT<x?1:6:k8<x?1:6:xT<x?kE<x?RP<x?1:6:lC<x?1:6:cb<x?iC<x?1:6:yC<x?1:6:JS<x?Ty<x?TI<x?ey<x?1:6:LS<x?1:6:XP<x?SC<x?1:6:wy<x?1:6:e_<x?WS<x?UI<x?1:6:D5<x?1:6:IP<x?Jb<x?1:6:mC<x?1:6:EP<x?Sm<x?SA<x?jP<x?nT<x?1:6:b5<x?1:6:p_<x?Hg<x?1:6:ly<x?1:6:V5<x?mk<x?og<x?1:6:LP<x?1:6:SP<x?J8<x?1:6:Ag<x?1:6:b4<x?gg<x?wC<x?w_<x?1:6:FE<x?1:6:DP<