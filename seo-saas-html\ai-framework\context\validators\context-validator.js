/**
 * Context Validator - Advanced Context Quality Control System
 * Validates context quality, completeness, and optimization potential
 */

const Ajv = require('ajv');
const addFormats = require('ajv-formats');

class ContextValidator {
    constructor(options = {}) {
        this.ajv = new Ajv({ allErrors: true, verbose: true });
        addFormats(this.ajv);
        
        this.contextSchema = null;
        this.validationRules = new Map();
        this.qualityMetrics = new Map();
        
        // Configuration
        this.config = {
            strictValidation: options.strictValidation !== false,
            qualityThreshold: options.qualityThreshold || 0.8,
            enableQualityScoring: options.enableQualityScoring !== false,
            ...options
        };
        
        this.initialize();
    }
    
    /**
     * Initialize the validator
     */
    async initialize() {
        try {
            // Load context schema
            await this.loadContextSchema();
            
            // Initialize validation rules
            this.initializeValidationRules();
            
            // Initialize quality metrics
            this.initializeQualityMetrics();
            
            console.log('ContextValidator initialized successfully');
        } catch (error) {
            console.error('Failed to initialize ContextValidator:', error);
            throw error;
        }
    }
    
    /**
     * Load context schema
     */
    async loadContextSchema() {
        try {
            const fs = require('fs').promises;
            const path = require('path');
            const schemaPath = path.join(__dirname, '../schemas/context-schema.json');
            const schemaContent = await fs.readFile(schemaPath, 'utf8');
            this.contextSchema = JSON.parse(schemaContent);
            
            // Compile schema for validation
            this.validateSchema = this.ajv.compile(this.contextSchema);
        } catch (error) {
            console.error('Failed to load context schema:', error);
            throw error;
        }
    }
    
    /**
     * Initialize validation rules
     */
    initializeValidationRules() {
        // Content quality rules
        this.validationRules.set('content_keyword_presence', {
            name: 'Primary Keyword Presence',
            validate: (context) => {
                if (!context.content?.primaryKeyword) return { valid: false, message: 'Primary keyword is required' };
                return { valid: true };
            },
            weight: 1.0,
            category: 'content'
        });
        
        this.validationRules.set('content_word_count_range', {
            name: 'Word Count Range',
            validate: (context) => {
                const wordCount = context.content?.wordCount;
                if (!wordCount) return { valid: false, message: 'Word count is required' };
                if (wordCount < 300 || wordCount > 3000) {
                    return { valid: false, message: 'Word count must be between 300 and 3000' };
                }
                return { valid: true };
            },
            weight: 0.8,
            category: 'content'
        });
        
        this.validationRules.set('content_type_validity', {
            name: 'Content Type Validity',
            validate: (context) => {
                const validTypes = ['blog_post', 'article', 'product_description', 'landing_page', 'meta_description', 'social_media_post', 'email_content', 'press_release'];
                const contentType = context.content?.type;
                if (!contentType) return { valid: false, message: 'Content type is required' };
                if (!validTypes.includes(contentType)) {
                    return { valid: false, message: `Invalid content type. Must be one of: ${validTypes.join(', ')}` };
                }
                return { valid: true };
            },
            weight: 1.0,
            category: 'content'
        });
        
        // SEO quality rules
        this.validationRules.set('seo_target_location', {
            name: 'SEO Target Location',
            validate: (context) => {
                if (!context.seo?.targetLocation) return { valid: false, message: 'Target location is required' };
                return { valid: true };
            },
            weight: 0.7,
            category: 'seo'
        });
        
        this.validationRules.set('seo_keyword_density_target', {
            name: 'Keyword Density Target',
            validate: (context) => {
                const density = context.seo?.optimization?.keywordDensity?.target;
                if (density && (density < 0.5 || density > 3.0)) {
                    return { valid: false, message: 'Keyword density target should be between 0.5% and 3.0%' };
                }
                return { valid: true };
            },
            weight: 0.6,
            category: 'seo'
        });
        
        // AI configuration rules
        this.validationRules.set('ai_provider_validity', {
            name: 'AI Provider Validity',
            validate: (context) => {
                const validProviders = ['openai', 'groq', 'anthropic', 'google'];
                const provider = context.ai?.provider;
                if (!provider) return { valid: false, message: 'AI provider is required' };
                if (!validProviders.includes(provider)) {
                    return { valid: false, message: `Invalid AI provider. Must be one of: ${validProviders.join(', ')}` };
                }
                return { valid: true };
            },
            weight: 1.0,
            category: 'ai'
        });
        
        this.validationRules.set('ai_temperature_range', {
            name: 'AI Temperature Range',
            validate: (context) => {
                const temperature = context.ai?.temperature;
                if (temperature !== undefined && (temperature < 0 || temperature > 2)) {
                    return { valid: false, message: 'AI temperature must be between 0 and 2' };
                }
                return { valid: true };
            },
            weight: 0.5,
            category: 'ai'
        });
        
        // User context rules
        this.validationRules.set('user_subscription_validity', {
            name: 'User Subscription Validity',
            validate: (context) => {
                const validSubscriptions = ['free', 'professional', 'business', 'enterprise'];
                const subscription = context.user?.subscription;
                if (!subscription) return { valid: false, message: 'User subscription is required' };
                if (!validSubscriptions.includes(subscription)) {
                    return { valid: false, message: `Invalid subscription. Must be one of: ${validSubscriptions.join(', ')}` };
                }
                return { valid: true };
            },
            weight: 0.8,
            category: 'user'
        });
        
        // Project context rules
        this.validationRules.set('project_industry_validity', {
            name: 'Project Industry Validity',
            validate: (context) => {
                const validIndustries = ['technology', 'healthcare', 'finance', 'ecommerce', 'realestate', 'legal', 'education', 'travel', 'food', 'fitness', 'fashion', 'automotive', 'saas', 'consulting', 'nonprofit'];
                const industry = context.project?.industry;
                if (industry && !validIndustries.includes(industry)) {
                    return { valid: false, message: `Invalid industry. Must be one of: ${validIndustries.join(', ')}` };
                }
                return { valid: true };
            },
            weight: 0.6,
            category: 'project'
        });
    }
    
    /**
     * Initialize quality metrics
     */
    initializeQualityMetrics() {
        this.qualityMetrics.set('completeness', {
            name: 'Context Completeness',
            calculate: (context) => {
                const requiredFields = [
                    'contextId', 'user.userId', 'content.type', 'content.primaryKeyword',
                    'content.intent', 'content.wordCount', 'seo.targetLocation',
                    'seo.language', 'ai.provider', 'ai.model'
                ];
                
                let completedFields = 0;
                for (const field of requiredFields) {
                    if (this.getNestedValue(context, field)) {
                        completedFields++;
                    }
                }
                
                return completedFields / requiredFields.length;
            },
            weight: 1.0
        });
        
        this.qualityMetrics.set('seo_optimization', {
            name: 'SEO Optimization Level',
            calculate: (context) => {
                let score = 0;
                let maxScore = 0;
                
                // Check for secondary keywords
                if (context.content?.secondaryKeywords?.length > 0) score += 0.2;
                maxScore += 0.2;
                
                // Check for competitor analysis
                if (context.seo?.competitorAnalysis?.topCompetitors?.length > 0) score += 0.3;
                maxScore += 0.3;
                
                // Check for E-E-A-T optimization
                if (context.seo?.optimization?.eeat) score += 0.2;
                maxScore += 0.2;
                
                // Check for keyword density optimization
                if (context.seo?.optimization?.keywordDensity?.target) score += 0.15;
                maxScore += 0.15;
                
                // Check for readability optimization
                if (context.seo?.optimization?.readabilityScore?.target) score += 0.15;
                maxScore += 0.15;
                
                return maxScore > 0 ? score / maxScore : 0;
            },
            weight: 0.8
        });
        
        this.qualityMetrics.set('ai_configuration', {
            name: 'AI Configuration Quality',
            calculate: (context) => {
                let score = 0;
                let maxScore = 0;
                
                // Check for appropriate model selection
                if (context.ai?.model) score += 0.3;
                maxScore += 0.3;
                
                // Check for temperature optimization
                const temp = context.ai?.temperature;
                if (temp >= 0.3 && temp <= 1.0) score += 0.2;
                maxScore += 0.2;
                
                // Check for token limit optimization
                const tokens = context.ai?.maxTokens;
                if (tokens >= 1000 && tokens <= 6000) score += 0.2;
                maxScore += 0.2;
                
                // Check for system prompt
                if (context.ai?.systemPrompt) score += 0.3;
                maxScore += 0.3;
                
                return maxScore > 0 ? score / maxScore : 0;
            },
            weight: 0.6
        });
        
        this.qualityMetrics.set('content_structure', {
            name: 'Content Structure Quality',
            calculate: (context) => {
                let score = 0;
                let maxScore = 0;
                
                // Check for heading structure
                if (context.content?.structure?.headings?.length > 0) score += 0.4;
                maxScore += 0.4;
                
                // Check for section planning
                if (context.content?.structure?.sections?.length > 0) score += 0.3;
                maxScore += 0.3;
                
                // Check for target audience definition
                if (context.project?.targetAudience) score += 0.3;
                maxScore += 0.3;
                
                return maxScore > 0 ? score / maxScore : 0;
            },
            weight: 0.7
        });
    }
    
    /**
     * Validate context using schema and rules
     */
    async validateContext(context) {
        try {
            const result = {
                valid: true,
                errors: [],
                warnings: [],
                qualityScore: 0,
                categoryScores: {},
                suggestions: []
            };
            
            // Schema validation
            const schemaValid = this.validateSchema(context);
            if (!schemaValid) {
                result.valid = false;
                result.errors.push(...this.validateSchema.errors.map(err => 
                    `Schema validation: ${err.instancePath} ${err.message}`
                ));
            }
            
            // Rule-based validation
            const ruleResults = await this.validateRules(context);
            result.errors.push(...ruleResults.errors);
            result.warnings.push(...ruleResults.warnings);
            result.categoryScores = ruleResults.categoryScores;
            
            if (ruleResults.errors.length > 0) {
                result.valid = false;
            }
            
            // Quality scoring
            if (this.config.enableQualityScoring) {
                result.qualityScore = await this.calculateQualityScore(context);
                result.suggestions = await this.generateSuggestions(context, result.qualityScore);
            }
            
            return result;
        } catch (error) {
            console.error('Context validation error:', error);
            return {
                valid: false,
                errors: ['Validation process failed'],
                warnings: [],
                qualityScore: 0,
                categoryScores: {},
                suggestions: []
            };
        }
    }
    
    /**
     * Validate using custom rules
     */
    async validateRules(context) {
        const errors = [];
        const warnings = [];
        const categoryScores = {};
        
        for (const [ruleId, rule] of this.validationRules) {
            try {
                const ruleResult = rule.validate(context);
                
                if (!ruleResult.valid) {
                    if (this.config.strictValidation) {
                        errors.push(`${rule.name}: ${ruleResult.message}`);
                    } else {
                        warnings.push(`${rule.name}: ${ruleResult.message}`);
                    }
                }
                
                // Track category scores
                if (!categoryScores[rule.category]) {
                    categoryScores[rule.category] = { passed: 0, total: 0, score: 0 };
                }
                
                categoryScores[rule.category].total++;
                if (ruleResult.valid) {
                    categoryScores[rule.category].passed++;
                }
            } catch (error) {
                console.error(`Rule validation error for ${ruleId}:`, error);
                warnings.push(`Rule ${rule.name} failed to execute`);
            }
        }
        
        // Calculate category scores
        for (const category in categoryScores) {
            const cat = categoryScores[category];
            cat.score = cat.total > 0 ? cat.passed / cat.total : 0;
        }
        
        return { errors, warnings, categoryScores };
    }
    
    /**
     * Calculate overall quality score
     */
    async calculateQualityScore(context) {
        let totalScore = 0;
        let totalWeight = 0;
        
        for (const [metricId, metric] of this.qualityMetrics) {
            try {
                const score = metric.calculate(context);
                totalScore += score * metric.weight;
                totalWeight += metric.weight;
            } catch (error) {
                console.error(`Quality metric error for ${metricId}:`, error);
            }
        }
        
        return totalWeight > 0 ? totalScore / totalWeight : 0;
    }
    
    /**
     * Generate improvement suggestions
     */
    async generateSuggestions(context, qualityScore) {
        const suggestions = [];
        
        if (qualityScore < this.config.qualityThreshold) {
            suggestions.push('Overall context quality is below threshold. Consider improving completeness and optimization.');
        }
        
        // Content suggestions
        if (!context.content?.secondaryKeywords?.length) {
            suggestions.push('Add secondary keywords to improve SEO optimization.');
        }
        
        if (!context.content?.structure?.headings?.length) {
            suggestions.push('Define heading structure for better content organization.');
        }
        
        // SEO suggestions
        if (!context.seo?.competitorAnalysis?.topCompetitors?.length) {
            suggestions.push('Add competitor analysis for better content positioning.');
        }
        
        if (!context.seo?.optimization?.eeat) {
            suggestions.push('Enable E-E-A-T optimization for better search rankings.');
        }
        
        // AI suggestions
        if (!context.ai?.systemPrompt) {
            suggestions.push('Add system prompt for more consistent AI responses.');
        }
        
        const temp = context.ai?.temperature;
        if (temp && (temp < 0.3 || temp > 1.0)) {
            suggestions.push('Adjust AI temperature to 0.3-1.0 range for optimal creativity/consistency balance.');
        }
        
        return suggestions;
    }
    
    /**
     * Get nested object value by path
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }
    
    /**
     * Get validation statistics
     */
    getValidationStats() {
        return {
            totalRules: this.validationRules.size,
            totalMetrics: this.qualityMetrics.size,
            qualityThreshold: this.config.qualityThreshold,
            strictValidation: this.config.strictValidation
        };
    }
}

module.exports = ContextValidator;
