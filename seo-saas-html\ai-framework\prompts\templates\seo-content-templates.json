{"templates": {"blog_post": {"name": "SEO Blog Post Generator", "description": "Comprehensive blog post generation with advanced SEO optimization", "category": "content_generation", "version": "1.0.0", "systemPrompt": "You are an expert SEO content writer with 20+ years of experience. You specialize in creating high-quality, engaging content that ranks #1 on Google while providing exceptional value to readers. You understand E-E-A-T guidelines, semantic SEO, and user intent optimization.", "userPromptTemplate": "Create a comprehensive {{contentType}} about \"{{primaryKeyword}}\" targeting {{targetLocation}} audience.\n\n**Content Requirements:**\n- Word count: {{wordCount}} words\n- Tone: {{tone}}\n- Industry: {{industry}}\n- Target audience: {{targetAudience}}\n- Search intent: {{intent}}\n- Primary keyword: {{primaryKeyword}}\n- Secondary keywords: {{secondaryKeywords}}\n\n**SEO Optimization:**\n- Keyword density: {{keywordDensity}}% for primary keyword\n- Include semantic keywords and LSI terms\n- Optimize for featured snippets\n- Create compelling meta title and description\n- Use proper heading hierarchy (H1, H2, H3)\n- Include internal and external linking opportunities\n\n**E-E-A-T Guidelines:**\n{{#if eeat.experience}}- Demonstrate real-world experience and practical insights{{/if}}\n{{#if eeat.expertise}}- Show subject matter expertise with authoritative information{{/if}}\n{{#if eeat.authoritativeness}}- Reference credible sources and industry authorities{{/if}}\n{{#if eeat.trustworthiness}}- Ensure factual accuracy and transparent information{{/if}}\n\n**Content Structure:**\n{{#if structure.headings}}\n**Required Headings:**\n{{#each structure.headings}}\n- {{level}}: {{text}} (Keywords: {{keywords}})\n{{/each}}\n{{/if}}\n\n{{#if competitorAnalysis}}\n**Competitor Analysis Insights:**\n{{#each competitorAnalysis.topCompetitors}}\n- {{title}}: {{wordCount}} words, {{keywordDensity}}% density\n{{/each}}\n\n**Content Gaps to Address:**\n{{#each competitorAnalysis.gapAnalysis}}\n- {{this}}\n{{/each}}\n{{/if}}\n\n**Additional Instructions:**\n{{additionalInstructions}}\n\n**Output Format:**\n1. SEO-optimized title (H1)\n2. Meta description (150-160 characters)\n3. Introduction with hook and primary keyword\n4. Main content with proper heading structure\n5. Conclusion with call-to-action\n6. Suggested internal/external links\n7. SEO analysis summary", "variables": ["contentType", "primaryKeyword", "targetLocation", "wordCount", "tone", "industry", "targetAudience", "intent", "secondaryKeywords", "keywordDensity", "eeat", "structure", "competitorAnalysis", "additionalInstructions"], "outputFormat": "structured_content", "qualityChecks": ["keyword_density_check", "readability_score", "heading_structure", "content_length", "eeat_compliance"]}, "product_description": {"name": "SEO Product Description Generator", "description": "Conversion-focused product descriptions with SEO optimization", "category": "ecommerce", "version": "1.0.0", "systemPrompt": "You are an expert ecommerce copywriter and SEO specialist. You create compelling product descriptions that convert visitors into customers while ranking high in search results. You understand buyer psychology, persuasive writing, and technical SEO.", "userPromptTemplate": "Create a compelling product description for \"{{primaryKeyword}}\" targeting {{targetLocation}} customers.\n\n**Product Information:**\n- Product name: {{primaryKeyword}}\n- Industry: {{industry}}\n- Target audience: {{targetAudience}}\n- Word count: {{wordCount}} words\n- Tone: {{tone}}\n\n**SEO Requirements:**\n- Primary keyword: {{primaryKeyword}}\n- Secondary keywords: {{secondaryKeywords}}\n- Keyword density: {{keywordDensity}}%\n- Search intent: {{intent}}\n\n**Conversion Elements:**\n- Highlight unique selling propositions\n- Address customer pain points\n- Include social proof elements\n- Create urgency and scarcity\n- Strong call-to-action\n\n**Technical Details:**\n{{#if productSpecs}}\n**Product Specifications:**\n{{#each productSpecs}}\n- {{name}}: {{value}}\n{{/each}}\n{{/if}}\n\n**Output Format:**\n1. SEO-optimized product title\n2. Meta description\n3. Product description with bullet points\n4. Key features and benefits\n5. Technical specifications\n6. Call-to-action\n7. SEO keyword analysis", "variables": ["primaryKeyword", "targetLocation", "industry", "targetAudience", "wordCount", "tone", "secondaryKeywords", "keywordDensity", "intent", "productSpecs"], "outputFormat": "product_content", "qualityChecks": ["conversion_elements", "keyword_optimization", "persuasive_language", "technical_accuracy"]}, "meta_description": {"name": "SEO Meta Description Generator", "description": "Click-worthy meta descriptions optimized for search results", "category": "seo_elements", "version": "1.0.0", "systemPrompt": "You are an expert SEO specialist focused on creating compelling meta descriptions that maximize click-through rates from search results. You understand SERP psychology and user behavior.", "userPromptTemplate": "Create an optimized meta description for content about \"{{primaryKeyword}}\" targeting {{targetLocation}} audience.\n\n**Requirements:**\n- Length: 150-160 characters\n- Include primary keyword: {{primaryKeyword}}\n- Search intent: {{intent}}\n- Tone: {{tone}}\n- Include compelling call-to-action\n\n**Content Context:**\n- Content type: {{contentType}}\n- Industry: {{industry}}\n- Target audience: {{targetAudience}}\n\n**Optimization Goals:**\n- Maximize click-through rate\n- Include emotional triggers\n- Create urgency or curiosity\n- Match search intent\n\n**Output Format:**\n1. Primary meta description (150-160 chars)\n2. Alternative version A\n3. Alternative version B\n4. Character count for each\n5. CTR optimization analysis", "variables": ["primaryKeyword", "targetLocation", "intent", "tone", "contentType", "industry", "targetAudience"], "outputFormat": "meta_content", "qualityChecks": ["character_count", "keyword_inclusion", "cta_presence", "emotional_appeal"]}, "landing_page": {"name": "SEO Landing Page Generator", "description": "High-converting landing pages with SEO optimization", "category": "conversion", "version": "1.0.0", "systemPrompt": "You are an expert conversion copywriter and SEO specialist. You create landing pages that convert visitors into customers while ranking high in search results. You understand conversion psychology, A/B testing, and technical SEO.", "userPromptTemplate": "Create a high-converting landing page for \"{{primaryKeyword}}\" targeting {{targetLocation}} audience.\n\n**Page Requirements:**\n- Primary keyword: {{primaryKeyword}}\n- Secondary keywords: {{secondaryKeywords}}\n- Word count: {{wordCount}} words\n- Industry: {{industry}}\n- Target audience: {{targetAudience}}\n- Conversion goal: {{conversionGoal}}\n- Tone: {{tone}}\n\n**SEO Optimization:**\n- Search intent: {{intent}}\n- Keyword density: {{keywordDensity}}%\n- Meta optimization\n- Schema markup suggestions\n\n**Conversion Elements:**\n- Compelling headline and subheadline\n- Value proposition\n- Social proof\n- Benefit-focused content\n- Objection handling\n- Strong call-to-action\n- Trust signals\n\n**Page Structure:**\n1. Hero section with headline\n2. Value proposition\n3. Benefits section\n4. Social proof\n5. Features overview\n6. FAQ section\n7. Final CTA\n\n**Output Format:**\n1. SEO-optimized headline (H1)\n2. Meta title and description\n3. Complete landing page copy\n4. CTA variations\n5. SEO recommendations\n6. Conversion optimization tips", "variables": ["primaryKeyword", "secondaryKeywords", "targetLocation", "wordCount", "industry", "targetAudience", "conversionGoal", "tone", "intent", "keywordDensity"], "outputFormat": "landing_page_content", "qualityChecks": ["conversion_elements", "seo_optimization", "persuasive_structure", "cta_effectiveness"]}, "competitor_analysis": {"name": "SEO Competitor Analysis Generator", "description": "Comprehensive competitor analysis for content strategy", "category": "analysis", "version": "1.0.0", "systemPrompt": "You are an expert SEO analyst and competitive intelligence specialist. You analyze competitor content to identify opportunities and create superior content strategies.", "userPromptTemplate": "Analyze the competitive landscape for \"{{primaryKeyword}}\" and create a content strategy to outrank competitors.\n\n**Analysis Parameters:**\n- Primary keyword: {{primaryKeyword}}\n- Target location: {{targetLocation}}\n- Industry: {{industry}}\n- Content type: {{contentType}}\n\n**Competitor Data:**\n{{#each competitors}}\n**Competitor {{@index}}:**\n- URL: {{url}}\n- Title: {{title}}\n- Word count: {{wordCount}}\n- Keyword density: {{keywordDensity}}%\n- Heading structure: {{headingStructure}}\n- Key topics covered: {{topics}}\n{{/each}}\n\n**Analysis Requirements:**\n1. Content gap analysis\n2. Keyword opportunity identification\n3. Content length recommendations\n4. Heading structure optimization\n5. Unique value proposition suggestions\n6. Content improvement opportunities\n\n**Output Format:**\n1. Competitive landscape overview\n2. Content gap analysis\n3. Keyword opportunities\n4. Content strategy recommendations\n5. Differentiation opportunities\n6. Action plan for outranking competitors", "variables": ["primaryKeyword", "targetLocation", "industry", "contentType", "competitors"], "outputFormat": "analysis_report", "qualityChecks": ["gap_identification", "opportunity_analysis", "strategic_recommendations", "actionable_insights"]}}, "promptModifiers": {"tone_modifiers": {"professional": "Use a professional, authoritative tone suitable for business audiences.", "casual": "Use a conversational, friendly tone that feels approachable and relatable.", "technical": "Use precise, technical language appropriate for expert audiences.", "friendly": "Use a warm, welcoming tone that builds trust and connection.", "authoritative": "Use a confident, expert tone that demonstrates deep knowledge.", "conversational": "Use a natural, dialogue-like tone as if speaking directly to the reader."}, "industry_modifiers": {"technology": "Focus on innovation, efficiency, and technical benefits. Use industry-specific terminology appropriately.", "healthcare": "Emphasize safety, efficacy, and patient outcomes. Ensure medical accuracy and compliance.", "finance": "Highlight security, returns, and risk management. Use financial terminology correctly.", "travel": "Focus on experiences, destinations, and practical travel advice. Include cultural insights.", "ecommerce": "Emphasize product benefits, customer satisfaction, and purchasing decisions."}, "intent_modifiers": {"informational": "Focus on providing comprehensive, educational content that answers user questions.", "commercial": "Balance information with subtle product positioning and comparison opportunities.", "transactional": "Focus on conversion elements, product benefits, and clear calls-to-action.", "navigational": "Provide clear, direct information to help users find what they're looking for."}}, "qualityStandards": {"keyword_density": {"primary_keyword": {"min": 1.0, "max": 3.0, "optimal": 2.0}, "secondary_keywords": {"min": 0.5, "max": 1.5, "optimal": 1.0}}, "readability": {"flesch_kincaid": {"min": 60, "optimal": 70}, "sentence_length": {"max": 20, "optimal": 15}}, "structure": {"headings": {"h1_count": 1, "h2_min": 3, "h3_recommended": true}, "paragraphs": {"max_sentences": 4, "optimal_sentences": 3}}}}