const OpenAIContentGenerator = require('./openaiContentGenerator');
const FirecrawlCompetitorAnalysis = require('./firecrawlCompetitorAnalysis');
const CompetitorDataProcessor = require('./competitorDataProcessor');
const DynamicContentOptimizer = require('./dynamicContentOptimizer');

/**
 * Enhanced Content Generator with Firecrawl Integration
 * Combines OpenAI content generation with deep competitor analysis
 * Produces superior SEO content based on comprehensive market research
 */
class EnhancedContentGenerator {
    constructor() {
        try {
            this.openaiGenerator = new OpenAIContentGenerator();
            this.isConfigured = this.openaiGenerator.isConfigured;
        } catch (error) {
            console.error('⚠️ Enhanced Content Generator: OpenAI initialization failed:', error.message);
            this.openaiGenerator = null;
            this.isConfigured = false;
        }
        
        this.firecrawlAnalysis = new FirecrawlCompetitorAnalysis();
        this.dataProcessor = new CompetitorDataProcessor();
        this.dynamicOptimizer = new DynamicContentOptimizer();
        
        console.log('🚀 Enhanced Content Generator with dynamic optimization initialized');
    }

    /**
     * Generate enhanced content with dynamic competitor analysis
     * @param {object} params - Content generation parameters
     * @returns {object} Enhanced content with competitive insights
     */
    async generateEnhancedContent(params) {
        try {
            console.log('🎯 Starting enhanced content generation with dynamic optimization...');
            
            // Check if OpenAI is configured
            if (!this.isConfigured || !this.openaiGenerator) {
                throw new Error('OpenAI is not configured. Enhanced content generation requires a valid OPENAI_API_KEY.');
            }
            
            // Step 1: Perform dynamic competitor analysis and optimization
            const location = this.determineSearchLocation(params);
            const optimizationParams = await this.dynamicOptimizer.analyzeAndOptimize(
                params.keyword, 
                location,
                { contentType: params.contentType }
            );
            
            console.log(`📊 Dynamic optimization completed - Target word count: ${optimizationParams.optimization.wordCount.target}`);
            
            // Step 2: Analyze competitors using Firecrawl (for detailed insights)
            const competitorAnalysis = await this.analyzeCompetitors(params);
            
            // Step 3: Process competitor data for insights
            const competitorInsights = await this.dataProcessor.processForContentGeneration(
                competitorAnalysis, 
                params.keyword
            );
            
            // Step 4: Enhance content parameters with both dynamic optimization and competitor insights
            const enhancedParams = this.enhanceContentParamsWithOptimization(
                params, 
                optimizationParams, 
                competitorInsights
            );
            
            // Step 5: Generate content with OpenAI using enhanced parameters
            const content = await this.openaiGenerator.generateContent(enhancedParams);
            
            // Step 6: Add competitive analysis data and optimization insights to response
            const enhancedContent = this.enrichContentWithOptimization(
                content, 
                optimizationParams, 
                competitorInsights, 
                competitorAnalysis
            );
            
            console.log('✅ Enhanced content generation with dynamic optimization completed successfully');
            return enhancedContent;
            
        } catch (error) {
            console.error('❌ Enhanced content generation failed:', error);
            
            // Fallback to standard OpenAI generation
            console.log('🔄 Falling back to standard content generation...');
            return await this.openaiGenerator.generateContent(params);
        }
    }

    /**
     * Analyze competitors for the given keyword
     * @param {object} params - Content generation parameters
     * @returns {object} Competitor analysis results
     */
    async analyzeCompetitors(params) {
        try {
            const location = this.determineSearchLocation(params);
            
            console.log(`🔍 Analyzing competitors for "${params.keyword}" in ${location}`);
            
            const analysis = await this.firecrawlAnalysis.analyzeCompetitors(
                params.keyword,
                location,
                { forceRefresh: params.forceRefresh || false }
            );
            
            console.log(`📊 Analyzed ${analysis.totalCompetitors} competitors with ${analysis.totalPagesCrawled} pages`);
            return analysis;
            
        } catch (error) {
            console.warn('⚠️ Competitor analysis failed, proceeding with real-time SERP analysis:', error.message);
            const location = this.determineSearchLocation(params);
            return await this.createRealTimeAnalysis(params.keyword, location);
        }
    }

    /**
     * Enhance content parameters with dynamic optimization and competitor insights
     * @param {object} originalParams - Original content parameters
     * @param {object} optimizationParams - Dynamic optimization results
     * @param {object} insights - Competitor insights
     * @returns {object} Enhanced parameters
     */
    enhanceContentParamsWithOptimization(originalParams, optimizationParams, insights) {
        const enhanced = { ...originalParams };
        
        // Apply dynamic optimization parameters
        enhanced.wordCount = `${optimizationParams.optimization.wordCount.minimum}-${optimizationParams.optimization.wordCount.maximum}`;
        enhanced.targetWordCount = optimizationParams.optimization.wordCount.target;
        
        // Apply heading structure optimization
        enhanced.headingStructure = optimizationParams.optimization.headingStructure;
        
        // Apply keyword strategy optimization
        enhanced.keywordStrategy = optimizationParams.optimization.keywordStrategy;
        
        // Apply section recommendations
        enhanced.sectionStructure = optimizationParams.optimization.sections;
        
        // Add competitor-derived keywords from insights
        if (insights.keywordStrategy?.relatedKeywords) {
            const relatedKeywords = insights.keywordStrategy.relatedKeywords
                .slice(0, 10)
                .map(kw => kw.keyword)
                .join(', ');
            
            enhanced.secondaryKeywords = enhanced.secondaryKeywords 
                ? `${enhanced.secondaryKeywords}, ${relatedKeywords}`
                : relatedKeywords;
        }
        
        // Add LSI keywords
        if (insights.keywordStrategy?.lsiKeywords) {
            const lsiKeywords = insights.keywordStrategy.lsiKeywords.join(', ');
            enhanced.lsiKeywords = lsiKeywords;
        }
        
        // Enhance content instructions with competitive insights and optimization
        enhanced.competitorInsights = this.buildCompetitorInstructions(insights);
        enhanced.contentGaps = insights.contentGaps?.missingTopics || [];
        enhanced.optimizationTargets = optimizationParams.recommendations || [];
        enhanced.competitorBenchmarks = optimizationParams.competitorInsights || {};
        
        // Add dynamic optimization instructions
        enhanced.additionalInstructions = this.buildOptimizationInstructions(optimizationParams);
        
        return enhanced;
    }

    /**
     * Build optimization instructions for content generation
     * @param {object} optimizationParams - Dynamic optimization results
     * @returns {string} Formatted optimization instructions
     */
    buildOptimizationInstructions(optimizationParams) {
        const instructions = [];
        
        // Word count instructions
        instructions.push(`Target word count: ${optimizationParams.optimization.wordCount.target} words (range: ${optimizationParams.optimization.wordCount.minimum}-${optimizationParams.optimization.wordCount.maximum})`);
        
        // Heading structure instructions
        const headings = optimizationParams.optimization.headingStructure;
        instructions.push(`Use exactly ${headings.h2} H2 headings, ${headings.h3} H3 headings, and ${headings.h4} H4 headings`);
        
        // Section structure instructions
        const sections = optimizationParams.optimization.sections;
        instructions.push(`Organize content into ${sections.recommended} main sections with approximately ${sections.averageWordsPerSection} words each`);
        
        // Keyword strategy instructions
        const keywordStrategy = optimizationParams.optimization.keywordStrategy;
        instructions.push(`Use primary keyword "${keywordStrategy.primary}" ${keywordStrategy.recommendedOccurrences} times (${keywordStrategy.targetDensity}% density)`);
        
        // Competitor benchmarks
        if (optimizationParams.competitorInsights.totalAnalyzed > 0) {
            instructions.push(`Exceed competitor average of ${optimizationParams.optimization.wordCount.competitorAverage} words`);
            instructions.push(`Target quality score above ${optimizationParams.competitorInsights.averageQualityScore}`);
        }
        
        return instructions.join('\n');
    }

    /**
     * Build competitor-based instructions for content generation
     * @param {object} insights - Competitor insights
     * @returns {string} Formatted instructions
     */
    buildCompetitorInstructions(insights) {
        const instructions = [];
        
        // Content strategy insights
        if (insights.contentStrategy) {
            instructions.push(`Target word count: ${insights.contentStrategy.recommendedWordCount} words (optimized based on competitor analysis)`);
        }
        
        // Heading structure recommendations
        if (insights.headingStructure?.recommended) {
            const structure = insights.headingStructure.recommended;
            instructions.push(`Use ${structure.h2} H2 headings, ${structure.h3} H3 headings, and ${structure.h4} H4 headings for optimal structure`);
        }
        
        // Keyword density optimization
        if (insights.keywordStrategy?.targetKeywordDensity) {
            instructions.push(`Maintain keyword density around ${insights.keywordStrategy.targetKeywordDensity.recommended}% (competitor average: ${insights.keywordStrategy.targetKeywordDensity.competitorAverage.toFixed(2)}%)`);
        }
        
        // Content gaps to address
        if (insights.contentGaps?.missingTopics?.length > 0) {
            instructions.push(`Address these underserved topics: ${insights.contentGaps.missingTopics.slice(0, 3).join(', ')}`);
        }
        
        // Competitive advantages to highlight
        if (insights.competitiveAdvantage?.contentQualityGaps?.length > 0) {
            instructions.push(`Emphasize these competitive advantages: ${insights.competitiveAdvantage.contentQualityGaps.slice(0, 2).join(', ')}`);
        }
        
        return instructions.join('\n');
    }

    /**
     * Enrich generated content with competitive insights
     * @param {object} content - Generated content from OpenAI
     * @param {object} insights - Competitor insights
     * @param {object} analysis - Raw competitor analysis
     * @returns {object} Enriched content
     */
    enrichContentWithInsights(content, insights, analysis) {
        if (!content.success) return content;
        
        const enriched = {
            ...content,
            competitiveAnalysis: {
                competitorsAnalyzed: analysis.totalCompetitors,
                pagesAnalyzed: analysis.totalPagesCrawled,
                competitiveAdvantages: insights.competitiveAdvantage,
                contentGaps: insights.contentGaps,
                keywordOpportunities: insights.keywordStrategy,
                technicalOptimizations: insights.technicalOptimization,
                benchmarks: {
                    averageCompetitorWordCount: insights.contentStrategy?.averageContentLength || 0,
                    recommendedWordCount: insights.contentStrategy?.recommendedWordCount || 0,
                    keywordDensityRange: insights.keywordStrategy?.targetKeywordDensity || {},
                    headingStructureBenchmark: insights.headingStructure?.competitorAverage || {}
                }
            },
            enhancedSEORecommendations: this.generateEnhancedSEORecommendations(insights),
            competitorComparison: this.generateCompetitorComparison(analysis, content.data)
        };
        
        return enriched;
    }

    /**
     * Generate enhanced SEO recommendations based on competitor analysis
     * @param {object} insights - Competitor insights
     * @returns {Array} Enhanced SEO recommendations
     */
    generateEnhancedSEORecommendations(insights) {
        const recommendations = [];
        
        // Content length recommendations
        if (insights.contentStrategy?.recommendedWordCount) {
            recommendations.push(`Optimize content length to ${insights.contentStrategy.recommendedWordCount} words to exceed competitor average`);
        }
        
        // Keyword strategy recommendations
        if (insights.keywordStrategy?.keywordGaps?.length > 0) {
            recommendations.push(`Target these underutilized keywords: ${insights.keywordStrategy.keywordGaps.slice(0, 3).join(', ')}`);
        }
        
        // Technical SEO recommendations
        if (insights.technicalOptimization?.recommendations?.length > 0) {
            recommendations.push(...insights.technicalOptimization.recommendations.slice(0, 3));
        }
        
        // Content gap recommendations
        if (insights.contentGaps?.missingTopics?.length > 0) {
            recommendations.push(`Address these content gaps: ${insights.contentGaps.missingTopics.slice(0, 2).join(', ')}`);
        }
        
        return recommendations;
    }

    /**
     * Enrich generated content with dynamic optimization insights
     * @param {object} content - Generated content from OpenAI
     * @param {object} optimizationParams - Dynamic optimization results
     * @param {object} insights - Competitor insights
     * @param {object} analysis - Raw competitor analysis
     * @returns {object} Enriched content with optimization data
     */
    enrichContentWithOptimization(content, optimizationParams, insights, analysis) {
        if (!content.success) return content;
        
        const enriched = {
            ...content,
            dynamicOptimization: {
                targetWordCount: optimizationParams.optimization.wordCount.target,
                actualWordCount: content.data?.word_count || 0,
                wordCountPerformance: {
                    target: optimizationParams.optimization.wordCount.target,
                    actual: content.data?.word_count || 0,
                    percentage: content.data?.word_count ? 
                        Math.round((content.data.word_count / optimizationParams.optimization.wordCount.target) * 100) : 0,
                    status: this.getWordCountStatus(content.data?.word_count || 0, optimizationParams.optimization.wordCount)
                },
                competitorBenchmarks: {
                    averageWordCount: optimizationParams.optimization.wordCount.competitorAverage,
                    competitorRange: optimizationParams.optimization.wordCount.competitorRange,
                    ourAdvantage: (content.data?.word_count || 0) - optimizationParams.optimization.wordCount.competitorAverage,
                    percentageAboveAverage: optimizationParams.optimization.wordCount.competitorAverage > 0 ? 
                        Math.round(((content.data?.word_count || 0) - optimizationParams.optimization.wordCount.competitorAverage) / optimizationParams.optimization.wordCount.competitorAverage * 100) : 0
                },
                headingOptimization: {
                    recommended: optimizationParams.optimization.headingStructure,
                    actual: this.analyzeGeneratedHeadings(content.data?.content || ''),
                    compliance: this.checkHeadingCompliance(content.data?.content || '', optimizationParams.optimization.headingStructure)
                },
                keywordOptimization: {
                    strategy: optimizationParams.optimization.keywordStrategy,
                    analysis: this.analyzeKeywordUsage(content.data?.content || '', optimizationParams.optimization.keywordStrategy)
                },
                competitiveAdvantage: {
                    totalCompetitorsAnalyzed: optimizationParams.competitorInsights.totalAnalyzed,
                    avgQualityScore: optimizationParams.competitorInsights.averageQualityScore,
                    contentGaps: optimizationParams.competitorInsights.contentGaps,
                    topPerformers: optimizationParams.competitorInsights.topPerformers
                }
            },
            competitiveAnalysis: {
                competitorsAnalyzed: analysis.totalCompetitors,
                pagesAnalyzed: analysis.totalPagesCrawled,
                competitiveAdvantages: insights.competitiveAdvantage,
                contentGaps: insights.contentGaps,
                keywordOpportunities: insights.keywordStrategy,
                technicalOptimizations: insights.technicalOptimization,
                benchmarks: {
                    averageCompetitorWordCount: insights.contentStrategy?.averageContentLength || 0,
                    recommendedWordCount: insights.contentStrategy?.recommendedWordCount || 0,
                    headingStructure: insights.headingStructure?.recommended || {}
                }
            }
        };
        
        // Add enhanced SEO recommendations
        enriched.data.seo_recommendations = [
            ...enriched.data.seo_recommendations || [],
            ...this.generateEnhancedSEORecommendations(insights),
            ...this.generateOptimizationRecommendations(optimizationParams)
        ];
        
        return enriched;
    }

    /**
     * Get word count status compared to target
     * @param {number} actual - Actual word count
     * @param {object} wordCountTarget - Word count target object
     * @returns {string} Status description
     */
    getWordCountStatus(actual, wordCountTarget) {
        if (actual >= wordCountTarget.minimum && actual <= wordCountTarget.maximum) {
            return 'optimal';
        } else if (actual < wordCountTarget.minimum) {
            return 'under_target';
        } else {
            return 'over_target';
        }
    }

    /**
     * Analyze generated headings
     * @param {string} content - Generated content
     * @returns {object} Heading analysis
     */
    analyzeGeneratedHeadings(content) {
        const headings = {
            h1: (content.match(/<h1[^>]*>/gi) || []).length,
            h2: (content.match(/<h2[^>]*>/gi) || []).length,
            h3: (content.match(/<h3[^>]*>/gi) || []).length,
            h4: (content.match(/<h4[^>]*>/gi) || []).length
        };
        
        return headings;
    }

    /**
     * Check heading compliance with optimization targets
     * @param {string} content - Generated content
     * @param {object} headingStructure - Target heading structure
     * @returns {object} Compliance analysis
     */
    checkHeadingCompliance(content, headingStructure) {
        const actual = this.analyzeGeneratedHeadings(content);
        
        return {
            h1: { target: headingStructure.h1, actual: actual.h1, compliant: actual.h1 === headingStructure.h1 },
            h2: { target: headingStructure.h2, actual: actual.h2, compliant: actual.h2 >= headingStructure.h2 },
            h3: { target: headingStructure.h3, actual: actual.h3, compliant: actual.h3 >= headingStructure.h3 },
            h4: { target: headingStructure.h4, actual: actual.h4, compliant: actual.h4 >= headingStructure.h4 }
        };
    }

    /**
     * Analyze keyword usage in generated content
     * @param {string} content - Generated content
     * @param {object} keywordStrategy - Keyword strategy
     * @returns {object} Keyword usage analysis
     */
    analyzeKeywordUsage(content, keywordStrategy) {
        const plainText = content.replace(/<[^>]*>/g, '').toLowerCase();
        const words = plainText.split(/\s+/);
        const totalWords = words.length;
        
        const keywordCount = (plainText.match(new RegExp(keywordStrategy.primary.toLowerCase(), 'g')) || []).length;
        const actualDensity = totalWords > 0 ? (keywordCount / totalWords) * 100 : 0;
        
        return {
            keyword: keywordStrategy.primary,
            targetCount: keywordStrategy.recommendedOccurrences,
            actualCount: keywordCount,
            targetDensity: keywordStrategy.targetDensity,
            actualDensity: actualDensity,
            compliant: actualDensity >= keywordStrategy.targetDensity * 0.8 && actualDensity <= keywordStrategy.targetDensity * 1.2
        };
    }

    /**
     * Generate optimization recommendations
     * @param {object} optimizationParams - Dynamic optimization results
     * @returns {Array} Optimization recommendations
     */
    generateOptimizationRecommendations(optimizationParams) {
        const recommendations = [];
        
        // Word count recommendations
        recommendations.push(`Target ${optimizationParams.optimization.wordCount.target} words to exceed competitor average of ${optimizationParams.optimization.wordCount.competitorAverage}`);
        
        // Heading structure recommendations
        const headings = optimizationParams.optimization.headingStructure;
        recommendations.push(`Use ${headings.h2} H2 headings and ${headings.h3} H3 headings for optimal structure`);
        
        // Keyword strategy recommendations
        const keywordStrategy = optimizationParams.optimization.keywordStrategy;
        recommendations.push(`Use "${keywordStrategy.primary}" ${keywordStrategy.recommendedOccurrences} times (${keywordStrategy.targetDensity}% density)`);
        
        // Competitor-based recommendations
        if (optimizationParams.competitorInsights.totalAnalyzed > 0) {
            recommendations.push(`Exceed top competitor quality score of ${optimizationParams.competitorInsights.averageQualityScore}`);
        }
        
        return recommendations;
    }

    /**
     * Generate competitor comparison insights
     * @param {object} analysis - Competitor analysis
     * @param {object} generatedContent - Generated content data
     * @returns {object} Competitor comparison
     */
    generateCompetitorComparison(analysis, generatedContent) {
        return {
            contentLength: {
                generated: generatedContent.word_count || 0,
                competitorAverage: analysis.aggregateInsights?.averageWordCount || 0,
                advantage: generatedContent.word_count > (analysis.aggregateInsights?.averageWordCount || 0) ? 'Longer than average' : 'Shorter than average'
            },
            headingStructure: {
                generated: {
                    h1: 1,
                    h2: (generatedContent.headings || []).filter(h => h.startsWith('## ')).length,
                    h3: (generatedContent.headings || []).filter(h => h.startsWith('### ')).length
                },
                competitorAverage: analysis.aggregateInsights?.commonHeadingStructure || {}
            },
            keywordOptimization: {
                targetKeywordDensity: this.calculateKeywordDensity(generatedContent.content, analysis.keyword),
                competitorAverageDensity: analysis.aggregateInsights?.keywordDensities?.average || 0
            },
            uniqueValuePropositions: this.identifyUniqueValueProps(analysis, generatedContent)
        };
    }

    /**
     * Calculate keyword density in generated content
     * @param {string} content - Generated content
     * @param {string} keyword - Target keyword
     * @returns {number} Keyword density percentage
     */
    calculateKeywordDensity(content, keyword) {
        if (!content || !keyword) return 0;
        
        const words = content.toLowerCase().split(/\s+/);
        const keywordWords = keyword.toLowerCase().split(/\s+/);
        let matches = 0;
        
        for (let i = 0; i <= words.length - keywordWords.length; i++) {
            const phrase = words.slice(i, i + keywordWords.length).join(' ');
            if (phrase === keyword.toLowerCase()) {
                matches++;
            }
        }
        
        return (matches / words.length) * 100;
    }

    /**
     * Identify unique value propositions compared to competitors
     * @param {object} analysis - Competitor analysis
     * @param {object} content - Generated content
     * @returns {Array} Unique value propositions
     */
    identifyUniqueValueProps(analysis, content) {
        // This would analyze the generated content against competitor content
        // to identify unique selling points and differentiators
        return [
            'Comprehensive service coverage',
            'Transparent pricing structure',
            'Advanced tracking and communication',
            'Specialized handling expertise',
            'Customer-centric approach'
        ];
    }

    /**
     * Determine search location based on parameters
     * @param {object} params - Content parameters
     * @returns {string} Search location
     */
    determineSearchLocation(params) {
        // Extract location from keyword or use default
        const keyword = params.keyword.toLowerCase();
        
        if (keyword.includes('dubai') || keyword.includes('uae')) {
            return 'google.ae';
        } else if (keyword.includes('uk') || keyword.includes('london')) {
            return 'google.co.uk';
        } else if (keyword.includes('canada') || keyword.includes('toronto')) {
            return 'google.ca';
        } else if (keyword.includes('australia') || keyword.includes('sydney')) {
            return 'google.com.au';
        }
        
        return 'google.com';
    }

    /**
     * Create real-time analysis using basic SERP data when full competitor analysis fails
     * @param {string} keyword - Target keyword
     * @param {string} location - Target location
     * @returns {object} Real-time analysis based on SERP data
     */
    async createRealTimeAnalysis(keyword, location = 'google.com') {
        try {
            console.log(`🔍 Creating real-time analysis for "${keyword}" using SERP data`);
            
            // Get basic SERP data for the keyword
            const serpData = await this.getSerpData(keyword, location);
            
            if (serpData && serpData.organic && serpData.organic.length > 0) {
                // Analyze top 5 results for basic insights
                const insights = await this.analyzeSerpResults(serpData.organic.slice(0, 5), keyword);
                
                return {
                    keyword: keyword,
                    location: location,
                    totalCompetitors: serpData.organic.length,
                    totalPagesCrawled: Math.min(serpData.organic.length, 5),
                    competitors: serpData.organic.slice(0, 5).map(result => ({
                        url: result.link,
                        title: result.title,
                        snippet: result.snippet,
                        position: result.position
                    })),
                    aggregateInsights: insights,
                    dataSource: 'realtime_serp',
                    timestamp: new Date().toISOString()
                };
            }
            
            // If SERP data fails, return minimal analysis
            return this.createMinimalAnalysis(keyword);
            
        } catch (error) {
            console.warn('⚠️ Real-time analysis failed:', error.message);
            return this.createMinimalAnalysis(keyword);
        }
    }
    
    /**
     * Get SERP data for a keyword
     * @param {string} keyword - Target keyword
     * @param {string} location - Target location
     * @returns {object} SERP data
     */
    async getSerpData(keyword, location) {
        const axios = require('axios');
        
        try {
            const response = await axios.post('https://google.serper.dev/search', {
                q: keyword,
                gl: this.getCountryCode(location),
                hl: 'en',
                num: 10,
                type: 'search'
            }, {
                headers: {
                    'X-API-KEY': process.env.SERPER_API_KEY,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });
            
            return response.data;
        } catch (error) {
            console.warn('⚠️ SERP data fetch failed:', error.message);
            return null;
        }
    }
    
    /**
     * Analyze SERP results for insights
     * @param {Array} serpResults - SERP results to analyze
     * @param {string} keyword - Target keyword
     * @returns {object} Analysis insights
     */
    async analyzeSerpResults(serpResults, keyword) {
        const insights = {
            averageWordCount: 1000,
            commonHeadingStructure: {
                avgH1: 1,
                avgH2: 4,
                avgH3: 3,
                avgH4: 1
            },
            keywordDensities: {
                average: 1.2,
                range: { min: 0.5, max: 2.0 }
            },
            commonTopics: [],
            titlePatterns: [],
            snippetInsights: []
        };
        
        // Analyze titles for patterns
        const titleWords = new Map();
        serpResults.forEach((result, index) => {
            if (result.title) {
                insights.titlePatterns.push({
                    position: index + 1,
                    title: result.title,
                    length: result.title.length,
                    containsKeyword: result.title.toLowerCase().includes(keyword.toLowerCase())
                });
                
                // Extract common words from titles
                const words = result.title.toLowerCase().split(/\s+/);
                words.forEach(word => {
                    if (word.length > 3) {
                        titleWords.set(word, (titleWords.get(word) || 0) + 1);
                    }
                });
            }
        });
        
        // Get most common title words
        const commonTitleWords = Array.from(titleWords.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10)
            .map(([word, count]) => ({ word, frequency: count }));
        
        insights.commonTopics = commonTitleWords;
        
        // Analyze snippets for content insights
        serpResults.forEach((result, index) => {
            if (result.snippet) {
                insights.snippetInsights.push({
                    position: index + 1,
                    snippet: result.snippet,
                    length: result.snippet.length,
                    keywordMentions: (result.snippet.toLowerCase().match(new RegExp(keyword.toLowerCase(), 'g')) || []).length
                });
            }
        });
        
        // Calculate dynamic word count target based on top results
        const avgTitleLength = insights.titlePatterns.reduce((sum, p) => sum + p.length, 0) / insights.titlePatterns.length;
        const avgSnippetLength = insights.snippetInsights.reduce((sum, s) => sum + s.length, 0) / insights.snippetInsights.length;
        
        // Estimate content length based on SERP analysis
        insights.averageWordCount = Math.max(800, Math.min(2000, Math.round((avgTitleLength + avgSnippetLength) * 4)));
        
        return insights;
    }
    
    /**
     * Create minimal analysis when all else fails
     * @param {string} keyword - Target keyword
     * @returns {object} Minimal analysis
     */
    createMinimalAnalysis(keyword) {
        return {
            keyword: keyword,
            totalCompetitors: 0,
            totalPagesCrawled: 0,
            competitors: [],
            aggregateInsights: {
                averageWordCount: 1000,
                commonHeadingStructure: {
                    avgH1: 1,
                    avgH2: 4,
                    avgH3: 3,
                    avgH4: 1
                },
                keywordDensities: {
                    average: 1.2,
                    range: { min: 0.5, max: 2.0 }
                },
                commonTopics: [],
                titlePatterns: [],
                snippetInsights: []
            },
            dataSource: 'minimal_fallback',
            timestamp: new Date().toISOString()
        };
    }
    
    /**
     * Get country code from location
     * @param {string} location - Location string
     * @returns {string} Country code
     */
    getCountryCode(location) {
        const locationMap = {
            'google.com': 'us',
            'google.co.uk': 'gb',
            'google.ca': 'ca',
            'google.com.au': 'au',
            'google.de': 'de',
            'google.fr': 'fr',
            'google.it': 'it',
            'google.es': 'es',
            'google.nl': 'nl'
        };
        
        return locationMap[location] || 'us';
    }
}

module.exports = EnhancedContentGenerator;
