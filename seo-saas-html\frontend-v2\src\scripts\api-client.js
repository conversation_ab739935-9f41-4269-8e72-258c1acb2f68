/**
 * API Client for SEO SAAS Application
 * Handles all HTTP requests to the backend with authentication, error handling, and caching
 */

class APIClient {
  constructor(baseURL = 'http://localhost:3001') {
    this.baseURL = baseURL;
    this.apiPrefix = '/api'; // Add API prefix
    this.token = this.getStoredToken();
    this.refreshToken = this.getStoredRefreshToken();
    this.requestQueue = [];
    this.isRefreshing = false;
    
    // Request interceptor for authentication
    this.setupInterceptors();
  }

  /**
   * Setup request/response interceptors
   */
  setupInterceptors() {
    // Add default headers and authentication
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };

    // Add auth token if available
    if (this.token) {
      this.defaultHeaders['Authorization'] = `Bearer ${this.token}`;
    }
  }

  /**
   * Get stored authentication token
   */
  getStoredToken() {
    try {
      return localStorage.getItem('auth_token');
    } catch (e) {
      return null;
    }
  }

  /**
   * Get stored refresh token
   */
  getStoredRefreshToken() {
    try {
      return localStorage.getItem('refresh_token');
    } catch (e) {
      return null;
    }
  }

  /**
   * Set authentication tokens
   */
  setTokens(token, refreshToken) {
    this.token = token;
    this.refreshToken = refreshToken;
    
    try {
      localStorage.setItem('auth_token', token);
      if (refreshToken) {
        localStorage.setItem('refresh_token', refreshToken);
      }
    } catch (e) {
      console.warn('Could not store tokens');
    }
    
    // Update default headers
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  /**
   * Clear authentication tokens
   */
  clearTokens() {
    this.token = null;
    this.refreshToken = null;
    
    try {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
    } catch (e) {
      console.warn('Could not clear tokens');
    }
    
    delete this.defaultHeaders['Authorization'];
  }

  /**
   * Make HTTP request with automatic retry and error handling
   */
  async request(endpoint, options = {}) {
    // Ensure endpoint starts with / and prepend API prefix if not already present
    const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    const fullEndpoint = normalizedEndpoint.startsWith('/api') ? normalizedEndpoint : `${this.apiPrefix}${normalizedEndpoint}`;
    const url = `${this.baseURL}${fullEndpoint}`;
    
    const config = {
      method: 'GET',
      headers: { ...this.defaultHeaders },
      ...options
    };

    // Add request body if provided
    if (options.body && typeof options.body === 'object') {
      config.body = JSON.stringify(options.body);
    }

    try {
      const response = await fetch(url, config);
      
      // Handle authentication errors
      if (response.status === 401 && this.refreshToken) {
        return await this.handleTokenRefresh(endpoint, options);
      }

      // Handle rate limiting
      if (response.status === 429) {
        const retryAfter = response.headers.get('Retry-After') || 60;
        throw new APIError('Rate limit exceeded', 'RATE_LIMIT', {
          retryAfter: parseInt(retryAfter) * 1000,
          status: 429
        });
      }

      // Parse response
      const data = await this.parseResponse(response);
      
      if (!response.ok) {
        throw new APIError(
          data.message || 'Request failed',
          data.code || 'REQUEST_FAILED',
          {
            status: response.status,
            data
          }
        );
      }

      return data;
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      
      // Network or other errors
      throw new APIError(
        'Network error or server unavailable',
        'NETWORK_ERROR',
        { originalError: error }
      );
    }
  }

  /**
   * Handle token refresh
   */
  async handleTokenRefresh(originalEndpoint, originalOptions) {
    // Prevent multiple refresh attempts
    if (this.isRefreshing) {
      return new Promise((resolve, reject) => {
        this.requestQueue.push({ resolve, reject, endpoint: originalEndpoint, options: originalOptions });
      });
    }

    this.isRefreshing = true;

    try {
      const response = await fetch(`${this.baseURL}/api/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ refreshToken: this.refreshToken })
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const data = await response.json();
      this.setTokens(data.token, data.refreshToken);

      // Process queued requests
      this.requestQueue.forEach(({ resolve, endpoint, options }) => {
        resolve(this.request(endpoint, options));
      });
      this.requestQueue = [];

      // Retry original request
      return await this.request(originalEndpoint, originalOptions);
    } catch (error) {
      // Clear tokens and redirect to login
      this.clearTokens();
      this.requestQueue.forEach(({ reject }) => {
        reject(new APIError('Authentication failed', 'AUTH_FAILED'));
      });
      this.requestQueue = [];
      
      // Dispatch auth error event
      window.dispatchEvent(new CustomEvent('auth:error', { detail: error }));
      
      throw new APIError('Authentication failed', 'AUTH_FAILED');
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * Parse response based on content type
   */
  async parseResponse(response) {
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    } else if (contentType && contentType.includes('text/')) {
      return await response.text();
    } else {
      return await response.blob();
    }
  }

  /**
   * HTTP Methods
   */
  get(endpoint, options = {}) {
    return this.request(endpoint, { ...options, method: 'GET' });
  }

  post(endpoint, body, options = {}) {
    return this.request(endpoint, { ...options, method: 'POST', body });
  }

  put(endpoint, body, options = {}) {
    return this.request(endpoint, { ...options, method: 'PUT', body });
  }

  patch(endpoint, body, options = {}) {
    return this.request(endpoint, { ...options, method: 'PATCH', body });
  }

  delete(endpoint, options = {}) {
    return this.request(endpoint, { ...options, method: 'DELETE' });
  }

  /**
   * Upload file with progress tracking
   */
  async upload(endpoint, file, options = {}) {
    const formData = new FormData();
    formData.append('file', file);
    
    // Add additional fields
    if (options.fields) {
      Object.keys(options.fields).forEach(key => {
        formData.append(key, options.fields[key]);
      });
    }

    const config = {
      method: 'POST',
      headers: {
        'Authorization': this.defaultHeaders['Authorization']
      },
      body: formData
    };

    // Add progress tracking if callback provided
    if (options.onProgress) {
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        
        xhr.upload.addEventListener('progress', (e) => {
          if (e.lengthComputable) {
            const percentComplete = (e.loaded / e.total) * 100;
            options.onProgress(percentComplete);
          }
        });
        
        xhr.addEventListener('load', () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const data = JSON.parse(xhr.responseText);
              resolve(data);
            } catch (e) {
              resolve(xhr.responseText);
            }
          } else {
            reject(new APIError('Upload failed', 'UPLOAD_FAILED', {
              status: xhr.status,
              response: xhr.responseText
            }));
          }
        });
        
        xhr.addEventListener('error', () => {
          reject(new APIError('Upload failed', 'UPLOAD_FAILED'));
        });
        
        xhr.open('POST', `${this.baseURL}${endpoint}`);
        xhr.setRequestHeader('Authorization', this.defaultHeaders['Authorization']);
        xhr.send(formData);
      });
    }

    return await fetch(`${this.baseURL}${endpoint}`, config);
  }

  /**
   * Download file
   */
  async download(endpoint, filename, options = {}) {
    const response = await this.request(endpoint, {
      ...options,
      headers: {
        ...this.defaultHeaders,
        'Accept': 'application/octet-stream'
      }
    });

    if (response instanceof Blob) {
      const url = window.URL.createObjectURL(response);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    }
  }

  /**
   * Authentication Methods
   */
  async login(credentials) {
    const data = await this.post('/api/auth/login', credentials);
    this.setTokens(data.token, data.refreshToken);
    return data;
  }

  async register(userData) {
    const data = await this.post('/api/auth/register', userData);
    this.setTokens(data.token, data.refreshToken);
    return data;
  }

  async logout() {
    try {
      await this.post('/api/auth/logout');
    } catch (error) {
      console.warn('Logout request failed:', error);
    } finally {
      this.clearTokens();
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated() {
    return !!this.token;
  }

  /**
   * Get current user info
   */
  async getCurrentUser() {
    return await this.get('/api/auth/me');
  }
}

/**
 * Custom API Error class
 */
class APIError extends Error {
  constructor(message, code, details = {}) {
    super(message);
    this.name = 'APIError';
    this.code = code;
    this.details = details;
  }

  isRetryable() {
    return ['NETWORK_ERROR', 'RATE_LIMIT'].includes(this.code);
  }

  getRetryDelay() {
    if (this.code === 'RATE_LIMIT') {
      return this.details.retryAfter || 60000;
    }
    return 1000; // Default 1 second
  }
}

// Export for use in other modules
window.APIClient = APIClient;
window.APIError = APIError;

// Create global API instance
window.api = new APIClient();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { APIClient, APIError };
}