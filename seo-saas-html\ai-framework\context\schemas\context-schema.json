{"$schema": "http://json-schema.org/draft-07/schema#", "title": "AI Context Schema for SEO SAAS", "description": "Comprehensive context schema for AI-powered SEO content generation", "type": "object", "properties": {"contextId": {"type": "string", "description": "Unique identifier for this context instance", "pattern": "^ctx_[a-zA-Z0-9]{16}$"}, "version": {"type": "string", "description": "Context schema version", "default": "1.0.0"}, "timestamp": {"type": "string", "format": "date-time", "description": "Context creation timestamp"}, "user": {"type": "object", "description": "User context information", "properties": {"userId": {"type": "string", "description": "User identifier"}, "subscription": {"type": "string", "enum": ["free", "professional", "business", "enterprise"], "description": "User subscription level"}, "preferences": {"type": "object", "properties": {"defaultTone": {"type": "string", "enum": ["professional", "casual", "friendly", "authoritative", "conversational", "technical"]}, "defaultIndustry": {"type": "string"}, "defaultWordCount": {"type": "integer", "minimum": 300, "maximum": 3000}}}}, "required": ["userId", "subscription"]}, "project": {"type": "object", "description": "Project context information", "properties": {"projectId": {"type": "string", "description": "Project identifier"}, "name": {"type": "string", "description": "Project name"}, "industry": {"type": "string", "enum": ["technology", "healthcare", "finance", "ecommerce", "realestate", "legal", "education", "travel", "food", "fitness", "fashion", "automotive", "saas", "consulting", "nonprofit"]}, "targetAudience": {"type": "string", "description": "Primary target audience"}, "brandVoice": {"type": "object", "properties": {"tone": {"type": "string", "enum": ["professional", "casual", "friendly", "authoritative", "conversational", "technical"]}, "personality": {"type": "array", "items": {"type": "string"}, "description": "Brand personality traits"}, "guidelines": {"type": "string", "description": "Specific brand voice guidelines"}}}}, "required": ["projectId", "industry"]}, "content": {"type": "object", "description": "Content generation context", "properties": {"type": {"type": "string", "enum": ["blog_post", "article", "product_description", "landing_page", "meta_description", "social_media_post", "email_content", "press_release"]}, "primaryKeyword": {"type": "string", "description": "Main target keyword"}, "secondaryKeywords": {"type": "array", "items": {"type": "string"}, "description": "Supporting keywords"}, "intent": {"type": "string", "enum": ["informational", "commercial", "transactional", "navigational"], "description": "Search intent for the content"}, "wordCount": {"type": "integer", "minimum": 300, "maximum": 3000, "description": "Target word count"}, "structure": {"type": "object", "properties": {"headings": {"type": "array", "items": {"type": "object", "properties": {"level": {"type": "integer", "minimum": 1, "maximum": 6}, "text": {"type": "string"}, "keywords": {"type": "array", "items": {"type": "string"}}}}}, "sections": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "wordCount": {"type": "integer"}, "focus": {"type": "string"}}}}}}}, "required": ["type", "primaryKeyword", "intent", "wordCount"]}, "seo": {"type": "object", "description": "SEO-specific context", "properties": {"targetLocation": {"type": "string", "description": "Geographic target (e.g., USA, UK, Global)"}, "language": {"type": "string", "default": "en", "description": "Content language code"}, "competitorAnalysis": {"type": "object", "properties": {"topCompetitors": {"type": "array", "items": {"type": "object", "properties": {"url": {"type": "string", "format": "uri"}, "title": {"type": "string"}, "wordCount": {"type": "integer"}, "keywordDensity": {"type": "number"}, "headingStructure": {"type": "array", "items": {"type": "string"}}}}}, "gapAnalysis": {"type": "array", "items": {"type": "string"}, "description": "Content gaps identified from competitor analysis"}}}, "optimization": {"type": "object", "properties": {"keywordDensity": {"type": "object", "properties": {"target": {"type": "number", "minimum": 0.5, "maximum": 3.0}, "current": {"type": "number"}}}, "readabilityScore": {"type": "object", "properties": {"target": {"type": "number", "minimum": 60, "maximum": 100}, "current": {"type": "number"}}}, "eeat": {"type": "object", "properties": {"experience": {"type": "boolean", "description": "Include experience-based content"}, "expertise": {"type": "boolean", "description": "Demonstrate subject expertise"}, "authoritativeness": {"type": "boolean", "description": "Include authoritative sources"}, "trustworthiness": {"type": "boolean", "description": "Ensure factual accuracy"}}}}}}, "required": ["targetLocation", "language"]}, "ai": {"type": "object", "description": "AI processing context", "properties": {"provider": {"type": "string", "enum": ["openai", "groq", "anthropic", "google"], "description": "Preferred AI provider"}, "model": {"type": "string", "description": "Specific model to use"}, "temperature": {"type": "number", "minimum": 0, "maximum": 2, "default": 0.7, "description": "AI creativity level"}, "maxTokens": {"type": "integer", "minimum": 100, "maximum": 8000, "default": 4000, "description": "Maximum tokens for response"}, "systemPrompt": {"type": "string", "description": "System-level instructions for AI"}, "previousInteractions": {"type": "array", "items": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}, "prompt": {"type": "string"}, "response": {"type": "string"}, "quality": {"type": "number", "minimum": 0, "maximum": 10}}}, "description": "Previous AI interactions for context continuity"}}, "required": ["provider", "model"]}, "performance": {"type": "object", "description": "Performance tracking context", "properties": {"metrics": {"type": "object", "properties": {"responseTime": {"type": "number", "description": "Response time in milliseconds"}, "qualityScore": {"type": "number", "minimum": 0, "maximum": 10, "description": "Content quality score"}, "userSatisfaction": {"type": "number", "minimum": 0, "maximum": 10, "description": "User satisfaction rating"}, "costPerRequest": {"type": "number", "description": "Cost in USD per request"}}}, "optimization": {"type": "object", "properties": {"suggestions": {"type": "array", "items": {"type": "string"}, "description": "Optimization suggestions"}, "appliedOptimizations": {"type": "array", "items": {"type": "string"}, "description": "Previously applied optimizations"}}}}}}, "required": ["contextId", "version", "timestamp", "user", "content", "seo", "ai"], "additionalProperties": false}