/**
 * Provider Manager - Multi-Provider AI Integration Management
 * Manages multiple AI providers with unified interface and intelligent routing
 */

const OpenAIProvider = require('./openai-provider');
const GroqProvider = require('./groq-provider');
const AnthropicProvider = require('./anthropic-provider');

class ProviderManager {
    constructor(options = {}) {
        this.providers = new Map();
        this.providerConfigs = new Map();
        this.fallbackChains = new Map();
        
        // Configuration
        this.config = {
            defaultProvider: options.defaultProvider || 'openai',
            enableFallbacks: options.enableFallbacks !== false,
            providerTimeout: options.providerTimeout || 30000,
            ...options
        };
        
        this.initialize();
    }
    
    /**
     * Initialize provider manager
     */
    async initialize() {
        try {
            // Initialize provider configurations
            this.initializeProviderConfigs();
            
            // Initialize providers
            await this.initializeProviders();
            
            // Setup fallback chains
            this.setupFallbackChains();
            
            console.log('ProviderManager initialized successfully');
        } catch (error) {
            console.error('Failed to initialize ProviderManager:', error);
            throw error;
        }
    }
    
    /**
     * Initialize provider configurations
     */
    initializeProviderConfigs() {
        // OpenAI Configuration
        this.providerConfigs.set('openai', {
            id: 'openai',
            name: 'OpenAI',
            class: OpenAIProvider,
            priority: 1,
            maxConcurrentRequests: 10,
            supportedModels: ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo'],
            supportedContentTypes: ['blog_post', 'article', 'product_description', 'landing_page', 'meta_description'],
            costPerToken: {
                'gpt-4o': { input: 0.005, output: 0.015 },
                'gpt-4o-mini': { input: 0.00015, output: 0.0006 },
                'gpt-4-turbo': { input: 0.01, output: 0.03 },
                'gpt-3.5-turbo': { input: 0.001, output: 0.002 }
            },
            features: ['streaming', 'function_calling', 'json_mode'],
            rateLimit: {
                requestsPerMinute: 500,
                tokensPerMinute: 150000
            }
        });
        
        // Groq Configuration
        this.providerConfigs.set('groq', {
            id: 'groq',
            name: 'Groq',
            class: GroqProvider,
            priority: 2,
            maxConcurrentRequests: 8,
            supportedModels: ['llama-3.1-70b-versatile', 'llama-3.1-8b-instant', 'mixtral-8x7b-32768'],
            supportedContentTypes: ['blog_post', 'article', 'product_description'],
            costPerToken: {
                'llama-3.1-70b-versatile': { input: 0.0008, output: 0.0008 },
                'llama-3.1-8b-instant': { input: 0.0001, output: 0.0001 },
                'mixtral-8x7b-32768': { input: 0.0007, output: 0.0007 }
            },
            features: ['streaming', 'high_speed'],
            rateLimit: {
                requestsPerMinute: 30,
                tokensPerMinute: 6000
            }
        });
        
        // Anthropic Configuration
        this.providerConfigs.set('anthropic', {
            id: 'anthropic',
            name: 'Anthropic',
            class: AnthropicProvider,
            priority: 3,
            maxConcurrentRequests: 5,
            supportedModels: ['claude-3-5-sonnet-20241022', 'claude-3-haiku-20240307'],
            supportedContentTypes: ['blog_post', 'article', 'landing_page'],
            costPerToken: {
                'claude-3-5-sonnet-20241022': { input: 0.003, output: 0.015 },
                'claude-3-haiku-20240307': { input: 0.00025, output: 0.00125 }
            },
            features: ['long_context', 'analysis'],
            rateLimit: {
                requestsPerMinute: 50,
                tokensPerMinute: 40000
            }
        });
    }
    
    /**
     * Initialize providers
     */
    async initializeProviders() {
        for (const [providerId, config] of this.providerConfigs) {
            try {
                const ProviderClass = config.class;
                const provider = new ProviderClass({
                    id: providerId,
                    config: config,
                    timeout: this.config.providerTimeout
                });
                
                await provider.initialize();
                this.providers.set(providerId, provider);
                
                console.log(`Provider ${providerId} initialized successfully`);
            } catch (error) {
                console.error(`Failed to initialize provider ${providerId}:`, error);
                // Continue with other providers
            }
        }
    }
    
    /**
     * Setup fallback chains
     */
    setupFallbackChains() {
        if (!this.config.enableFallbacks) return;
        
        // OpenAI fallback chain
        this.fallbackChains.set('openai', ['groq', 'anthropic']);
        
        // Groq fallback chain
        this.fallbackChains.set('groq', ['openai', 'anthropic']);
        
        // Anthropic fallback chain
        this.fallbackChains.set('anthropic', ['openai', 'groq']);
    }
    
    /**
     * Execute request with specified provider
     */
    async executeRequest(providerId, context, prompt) {
        try {
            const provider = this.providers.get(providerId);
            if (!provider) {
                throw new Error(`Provider ${providerId} not found`);
            }
            
            // Validate provider can handle request
            this.validateProviderCapability(provider, context);
            
            // Execute request
            const result = await provider.generateContent(context, prompt);
            
            return {
                providerId,
                success: true,
                result,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error(`Provider ${providerId} request failed:`, error);
            throw error;
        }
    }
    
    /**
     * Validate provider capability for request
     */
    validateProviderCapability(provider, context) {
        const config = this.providerConfigs.get(provider.id);
        
        // Check model support
        const requestedModel = context.ai?.model;
        if (requestedModel && !config.supportedModels.includes(requestedModel)) {
            throw new Error(`Provider ${provider.id} does not support model ${requestedModel}`);
        }
        
        // Check content type support
        const contentType = context.content?.type;
        if (contentType && !config.supportedContentTypes.includes(contentType)) {
            throw new Error(`Provider ${provider.id} does not support content type ${contentType}`);
        }
        
        // Check token limits
        const maxTokens = context.ai?.maxTokens || 4000;
        if (maxTokens > provider.getMaxTokens()) {
            throw new Error(`Requested tokens (${maxTokens}) exceed provider limit (${provider.getMaxTokens()})`);
        }
    }
    
    /**
     * Get provider by ID
     */
    getProvider(providerId) {
        const provider = this.providers.get(providerId);
        if (!provider) return null;
        
        return {
            id: providerId,
            name: this.providerConfigs.get(providerId)?.name,
            instance: provider,
            config: this.providerConfigs.get(providerId)
        };
    }
    
    /**
     * Get all available providers
     */
    getAvailableProviders() {
        const providers = [];
        
        for (const [providerId, provider] of this.providers) {
            if (provider.isAvailable()) {
                const config = this.providerConfigs.get(providerId);
                providers.push({
                    id: providerId,
                    name: config.name,
                    priority: config.priority,
                    instance: provider,
                    config: config
                });
            }
        }
        
        // Sort by priority
        return providers.sort((a, b) => a.priority - b.priority);
    }
    
    /**
     * Get default provider
     */
    getDefaultProvider() {
        const defaultId = this.config.defaultProvider;
        return this.getProvider(defaultId);
    }
    
    /**
     * Get fallback providers for a given provider
     */
    getFallbackProviders(providerId) {
        const fallbackIds = this.fallbackChains.get(providerId) || [];
        const fallbackProviders = [];
        
        for (const fallbackId of fallbackIds) {
            const provider = this.getProvider(fallbackId);
            if (provider && provider.instance.isAvailable()) {
                fallbackProviders.push(provider);
            }
        }
        
        return fallbackProviders;
    }
    
    /**
     * Get provider statistics
     */
    getProviderStats() {
        const stats = {};
        
        for (const [providerId, provider] of this.providers) {
            const config = this.providerConfigs.get(providerId);
            stats[providerId] = {
                name: config.name,
                available: provider.isAvailable(),
                supportedModels: config.supportedModels,
                supportedContentTypes: config.supportedContentTypes,
                features: config.features,
                rateLimit: config.rateLimit,
                stats: provider.getStats()
            };
        }
        
        return stats;
    }
    
    /**
     * Get optimal provider for context
     */
    async getOptimalProvider(context) {
        const availableProviders = this.getAvailableProviders();
        
        if (availableProviders.length === 0) {
            throw new Error('No available providers');
        }
        
        // If specific provider requested
        if (context.ai?.provider) {
            const requestedProvider = availableProviders.find(p => p.id === context.ai.provider);
            if (requestedProvider) {
                return requestedProvider;
            }
        }
        
        // Score providers based on context
        const scoredProviders = [];
        
        for (const provider of availableProviders) {
            const score = this.calculateProviderScore(provider, context);
            scoredProviders.push({ provider, score });
        }
        
        // Sort by score (highest first)
        scoredProviders.sort((a, b) => b.score - a.score);
        
        return scoredProviders[0].provider;
    }
    
    /**
     * Calculate provider score for context
     */
    calculateProviderScore(provider, context) {
        let score = 0;
        const config = provider.config;
        
        // Base priority score (30%)
        score += (4 - config.priority) * 7.5;
        
        // Model compatibility (25%)
        const requestedModel = context.ai?.model;
        if (requestedModel && config.supportedModels.includes(requestedModel)) {
            score += 25;
        } else if (config.supportedModels.length > 0) {
            score += 15; // Partial score for having models
        }
        
        // Content type compatibility (20%)
        const contentType = context.content?.type;
        if (contentType && config.supportedContentTypes.includes(contentType)) {
            score += 20;
        } else if (config.supportedContentTypes.length > 0) {
            score += 10;
        }
        
        // Feature support (15%)
        const requiredFeatures = context.ai?.requiredFeatures || [];
        const supportedFeatures = requiredFeatures.filter(feature => 
            config.features.includes(feature)
        );
        score += (supportedFeatures.length / Math.max(requiredFeatures.length, 1)) * 15;
        
        // Cost efficiency (10%)
        const wordCount = context.content?.wordCount || 1000;
        const estimatedTokens = wordCount * 1.3; // Rough estimation
        const model = requestedModel || config.supportedModels[0];
        const costPerToken = config.costPerToken[model];
        
        if (costPerToken) {
            const estimatedCost = estimatedTokens * costPerToken.input;
            // Lower cost = higher score (inverse relationship)
            score += Math.max(0, 10 - (estimatedCost * 1000));
        }
        
        return Math.min(100, Math.max(0, score));
    }
    
    /**
     * Health check for all providers
     */
    async healthCheck() {
        const results = {};
        
        for (const [providerId, provider] of this.providers) {
            try {
                const isHealthy = await provider.healthCheck();
                results[providerId] = {
                    status: isHealthy ? 'healthy' : 'unhealthy',
                    timestamp: new Date().toISOString()
                };
            } catch (error) {
                results[providerId] = {
                    status: 'error',
                    error: error.message,
                    timestamp: new Date().toISOString()
                };
            }
        }
        
        return results;
    }
    
    /**
     * Update provider configuration
     */
    updateProviderConfig(providerId, updates) {
        const config = this.providerConfigs.get(providerId);
        if (!config) {
            throw new Error(`Provider ${providerId} not found`);
        }
        
        // Update configuration
        Object.assign(config, updates);
        
        // Update provider instance if needed
        const provider = this.providers.get(providerId);
        if (provider && provider.updateConfig) {
            provider.updateConfig(updates);
        }
    }
    
    /**
     * Add new provider
     */
    async addProvider(providerId, config) {
        if (this.providers.has(providerId)) {
            throw new Error(`Provider ${providerId} already exists`);
        }
        
        try {
            const ProviderClass = config.class;
            const provider = new ProviderClass({
                id: providerId,
                config: config,
                timeout: this.config.providerTimeout
            });
            
            await provider.initialize();
            
            this.providers.set(providerId, provider);
            this.providerConfigs.set(providerId, config);
            
            console.log(`Provider ${providerId} added successfully`);
        } catch (error) {
            console.error(`Failed to add provider ${providerId}:`, error);
            throw error;
        }
    }
    
    /**
     * Remove provider
     */
    async removeProvider(providerId) {
        const provider = this.providers.get(providerId);
        if (!provider) {
            throw new Error(`Provider ${providerId} not found`);
        }
        
        try {
            if (provider.shutdown) {
                await provider.shutdown();
            }
            
            this.providers.delete(providerId);
            this.providerConfigs.delete(providerId);
            this.fallbackChains.delete(providerId);
            
            console.log(`Provider ${providerId} removed successfully`);
        } catch (error) {
            console.error(`Failed to remove provider ${providerId}:`, error);
            throw error;
        }
    }
    
    /**
     * Shutdown all providers
     */
    async shutdown() {
        for (const [providerId, provider] of this.providers) {
            try {
                if (provider.shutdown) {
                    await provider.shutdown();
                }
            } catch (error) {
                console.error(`Error shutting down provider ${providerId}:`, error);
            }
        }
        
        this.providers.clear();
        this.providerConfigs.clear();
        this.fallbackChains.clear();
    }
}

module.exports = ProviderManager;
