/**
 * Main JavaScript File
 * Common functionality shared across all pages
 */

// Global configuration
window.APP_CONFIG = {
  API_URL: 'http://localhost:3001',
  WS_URL: 'ws://localhost:3001/ws',
  APP_NAME: 'SEO Pro',
  VERSION: '2.0.0',
  DEBUG: true,
  DEMO_MODE: true // Enable demo mode for development
};

// Utility functions
const Utils = {
  /**
   * Format number with commas
   */
  formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  },

  /**
   * Format date relative to now
   */
  formatRelativeTime(date) {
    const now = new Date();
    const diff = now - new Date(date);
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    return 'Just now';
  },

  /**
   * Debounce function
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  /**
   * Generate unique ID
   */
  generateId() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  },

  /**
   * Safe JSON parse
   */
  parseJSON(str, fallback = null) {
    try {
      return JSON.parse(str);
    } catch (e) {
      return fallback;
    }
  },

  /**
   * Get cookie value
   */
  getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return null;
  },

  /**
   * Set cookie
   */
  setCookie(name, value, days = 7) {
    const date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    const expires = `expires=${date.toUTCString()}`;
    document.cookie = `${name}=${value};${expires};path=/`;
  },

  /**
   * Show toast notification
   */
  showToast(message, type = 'info', duration = 3000) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
      <div class="toast-icon">${this.getToastIcon(type)}</div>
      <div class="toast-content">
        <div class="toast-message">${message}</div>
      </div>
      <button class="toast-close" onclick="this.parentElement.remove()">×</button>
    `;

    const container = document.querySelector('.toast-container') || this.createToastContainer();
    container.appendChild(toast);

    // Auto remove
    setTimeout(() => {
      toast.classList.add('toast-fade-out');
      setTimeout(() => toast.remove(), 300);
    }, duration);
  },

  /**
   * Get toast icon
   */
  getToastIcon(type) {
    const icons = {
      success: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>',
      error: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/><line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/><line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/></svg>',
      warning: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10.29 3.86L1.82 18C1.64539 18.3024 1.55296 18.6453 1.55199 18.9945C1.55101 19.3437 1.64151 19.6871 1.81442 19.9905C1.98734 20.2939 2.23673 20.5467 2.53771 20.7238C2.83868 20.901 3.18058 20.9962 3.53 21H20.47C20.8194 20.9962 21.1613 20.901 21.4623 20.7238C21.7633 20.5467 22.0127 20.2939 22.1856 19.9905C22.3585 19.6871 22.449 19.3437 22.448 18.9945C22.447 18.6453 22.3546 18.3024 22.18 18L13.71 3.86C13.5318 3.56611 13.2807 3.32313 12.9812 3.15449C12.6817 2.98585 12.3438 2.89726 12 2.89726C11.6562 2.89726 11.3183 2.98585 11.0188 3.15449C10.7193 3.32313 10.4682 3.56611 10.29 3.86Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><line x1="12" y1="9" x2="12" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round"/><line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round"/></svg>',
      info: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/><line x1="12" y1="16" x2="12" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/><line x1="12" y1="8" x2="12.01" y2="8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/></svg>'
    };
    return icons[type] || icons.info;
  },

  /**
   * Create toast container
   */
  createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container';
    document.body.appendChild(container);
    return container;
  }
};

// Navigation functionality
class Navigation {
  constructor() {
    this.init();
  }

  init() {
    // Mobile menu toggle
    const mobileToggle = document.querySelector('.header-mobile-toggle');
    const mobileMenu = document.querySelector('.header-mobile-menu');
    
    if (mobileToggle && mobileMenu) {
      mobileToggle.addEventListener('click', () => {
        mobileToggle.classList.toggle('active');
        mobileMenu.classList.toggle('active');
      });
    }

    // Dropdown menus
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
      const trigger = dropdown.querySelector('.dropdown-trigger');
      const menu = dropdown.querySelector('.dropdown-menu');
      
      if (trigger && menu) {
        trigger.addEventListener('click', (e) => {
          e.stopPropagation();
          dropdown.classList.toggle('active');
        });
      }
    });

    // Close dropdowns on outside click
    document.addEventListener('click', () => {
      dropdowns.forEach(dropdown => {
        dropdown.classList.remove('active');
      });
    });

    // Active nav link
    this.setActiveNavLink();

    // Sidebar toggle
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    const appContent = document.querySelector('.app-content');
    
    if (sidebarToggle && sidebar && appContent) {
      sidebarToggle.addEventListener('click', () => {
        sidebar.classList.toggle('collapsed');
        appContent.classList.toggle('sidebar-collapsed');
        
        // Save preference
        localStorage.setItem('sidebar-collapsed', sidebar.classList.contains('collapsed'));
      });

      // Restore sidebar state
      if (localStorage.getItem('sidebar-collapsed') === 'true') {
        sidebar.classList.add('collapsed');
        appContent.classList.add('sidebar-collapsed');
      }
    }
  }

  setActiveNavLink() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link, .sidebar-nav-link');
    
    navLinks.forEach(link => {
      const href = link.getAttribute('href');
      if (href && currentPath.includes(href.replace('.html', ''))) {
        link.classList.add('active');
        
        // Expand parent if in sidebar
        const parent = link.closest('.sidebar-nav-item');
        if (parent) {
          parent.classList.add('active');
        }
      }
    });
  }
}

// Form handling
class FormHandler {
  constructor() {
    this.init();
  }

  init() {
    // Handle all forms
    const forms = document.querySelectorAll('form[data-ajax]');
    forms.forEach(form => {
      form.addEventListener('submit', (e) => this.handleSubmit(e));
    });

    // Input validation
    const inputs = document.querySelectorAll('input[required], textarea[required]');
    inputs.forEach(input => {
      input.addEventListener('blur', () => this.validateInput(input));
      input.addEventListener('input', () => this.clearError(input));
    });

    // Password visibility toggle
    const passwordToggles = document.querySelectorAll('.password-toggle');
    passwordToggles.forEach(toggle => {
      toggle.addEventListener('click', () => this.togglePasswordVisibility(toggle));
    });
  }

  async handleSubmit(e) {
    e.preventDefault();
    const form = e.target;
    
    // Validate form
    if (!this.validateForm(form)) {
      return;
    }

    // Show loading
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.disabled = true;
    submitButton.textContent = 'Loading...';

    try {
      const formData = new FormData(form);
      const data = Object.fromEntries(formData);
      
      // Get form action and method
      const action = form.getAttribute('data-action') || form.action;
      const method = form.getAttribute('data-method') || form.method || 'POST';

      // Make request
      const response = await fetch(action, {
        method: method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      const result = await response.json();

      if (response.ok) {
        this.handleSuccess(form, result);
      } else {
        this.handleError(form, result);
      }
    } catch (error) {
      console.error('Form submission error:', error);
      Utils.showToast('An error occurred. Please try again.', 'error');
    } finally {
      submitButton.disabled = false;
      submitButton.textContent = originalText;
    }
  }

  validateForm(form) {
    const inputs = form.querySelectorAll('input[required], textarea[required]');
    let isValid = true;

    inputs.forEach(input => {
      if (!this.validateInput(input)) {
        isValid = false;
      }
    });

    return isValid;
  }

  validateInput(input) {
    const value = input.value.trim();
    const type = input.type;
    let isValid = true;
    let errorMessage = '';

    // Required validation
    if (input.hasAttribute('required') && !value) {
      errorMessage = 'This field is required';
      isValid = false;
    }
    // Email validation
    else if (type === 'email' && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        errorMessage = 'Please enter a valid email address';
        isValid = false;
      }
    }
    // Password validation
    else if (type === 'password' && value) {
      if (value.length < 8) {
        errorMessage = 'Password must be at least 8 characters';
        isValid = false;
      }
    }

    // Show error
    if (!isValid) {
      this.showError(input, errorMessage);
    } else {
      this.clearError(input);
    }

    return isValid;
  }

  showError(input, message) {
    const formGroup = input.closest('.form-group');
    if (formGroup) {
      formGroup.classList.add('error');
      
      let errorElement = formGroup.querySelector('.error-message');
      if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'error-message';
        formGroup.appendChild(errorElement);
      }
      errorElement.textContent = message;
    }
  }

  clearError(input) {
    const formGroup = input.closest('.form-group');
    if (formGroup) {
      formGroup.classList.remove('error');
      const errorElement = formGroup.querySelector('.error-message');
      if (errorElement) {
        errorElement.remove();
      }
    }
  }

  togglePasswordVisibility(toggle) {
    const input = toggle.previousElementSibling;
    if (input && input.type === 'password') {
      input.type = 'text';
      toggle.innerHTML = '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.13 1 11C1.73 8.87 2.73 7 4 5.27M9.9 4.24A9.12 9.12 0 0 1 12 4C17 4 21.27 7.87 23 13C22.27 15.13 21.27 17 20 18.73M14.12 14.12A3 3 0 1 1 9.88 9.88" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>';
    } else if (input && input.type === 'text') {
      input.type = 'password';
      toggle.innerHTML = '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>';
    }
  }

  handleSuccess(form, result) {
    const successCallback = form.getAttribute('data-success');
    if (successCallback && window[successCallback]) {
      window[successCallback](result);
    } else {
      Utils.showToast(result.message || 'Success!', 'success');
      
      // Redirect if specified
      const redirect = form.getAttribute('data-redirect');
      if (redirect) {
        setTimeout(() => {
          window.location.href = redirect;
        }, 1000);
      }
    }
  }

  handleError(form, result) {
    const errorCallback = form.getAttribute('data-error');
    if (errorCallback && window[errorCallback]) {
      window[errorCallback](result);
    } else {
      Utils.showToast(result.error || result.message || 'An error occurred', 'error');
    }
  }
}

// Search functionality
class Search {
  constructor() {
    this.init();
  }

  init() {
    const searchInputs = document.querySelectorAll('.search-input');
    searchInputs.forEach(input => {
      input.addEventListener('input', Utils.debounce((e) => this.handleSearch(e), 300));
      input.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.performSearch(e.target.value);
        }
      });
    });

    // Search button
    const searchButtons = document.querySelectorAll('.search-button');
    searchButtons.forEach(button => {
      button.addEventListener('click', () => {
        const input = button.previousElementSibling;
        if (input) {
          this.performSearch(input.value);
        }
      });
    });
  }

  handleSearch(e) {
    const query = e.target.value.trim();
    if (query.length > 2) {
      this.showSearchSuggestions(query);
    } else {
      this.hideSearchSuggestions();
    }
  }

  performSearch(query) {
    if (!query.trim()) return;
    
    // In demo mode, show mock results
    if (window.APP_CONFIG.DEMO_MODE) {
      Utils.showToast(`Searching for: ${query}`, 'info');
      // Could redirect to a search results page
      // window.location.href = `/search.html?q=${encodeURIComponent(query)}`;
    } else {
      // Real search implementation
      window.location.href = `/search?q=${encodeURIComponent(query)}`;
    }
  }

  showSearchSuggestions(query) {
    // Implement search suggestions
    console.log('Search suggestions for:', query);
  }

  hideSearchSuggestions() {
    // Hide suggestions dropdown
  }
}

// Modal functionality
class Modal {
  static show(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.classList.add('active');
      const overlay = document.querySelector('.overlay') || this.createOverlay();
      overlay.classList.add('active');
      
      // Close on overlay click
      overlay.addEventListener('click', () => this.hide(modalId));
      
      // Close on escape key
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
          this.hide(modalId);
        }
      });
    }
  }

  static hide(modalId) {
    const modal = document.getElementById(modalId);
    const overlay = document.querySelector('.overlay');
    
    if (modal) {
      modal.classList.remove('active');
    }
    
    if (overlay) {
      overlay.classList.remove('active');
    }
  }

  static createOverlay() {
    const overlay = document.createElement('div');
    overlay.className = 'overlay';
    document.body.appendChild(overlay);
    return overlay;
  }
}

// Tab functionality
class Tabs {
  constructor() {
    this.init();
  }

  init() {
    const tabContainers = document.querySelectorAll('.tabs');
    tabContainers.forEach(container => {
      const tabs = container.querySelectorAll('.tab');
      const contents = container.nextElementSibling.querySelectorAll('.tab-content');
      
      tabs.forEach((tab, index) => {
        tab.addEventListener('click', () => {
          // Remove active class from all tabs and contents
          tabs.forEach(t => t.classList.remove('active'));
          contents.forEach(c => c.classList.remove('active'));
          
          // Add active class to clicked tab and corresponding content
          tab.classList.add('active');
          if (contents[index]) {
            contents[index].classList.add('active');
          }
        });
      });
    });
  }
}

// Smooth scrolling
class SmoothScroll {
  constructor() {
    this.init();
  }

  init() {
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
      link.addEventListener('click', (e) => {
        const href = link.getAttribute('href');
        if (href !== '#') {
          e.preventDefault();
          const target = document.querySelector(href);
          if (target) {
            target.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        }
      });
    });
  }
}

// Initialize common functionality
document.addEventListener('DOMContentLoaded', () => {
  // Initialize components
  new Navigation();
  new FormHandler();
  new Search();
  new Tabs();
  new SmoothScroll();
  
  // Add loaded class to body
  document.body.classList.add('loaded');
  
  // Log app info
  console.log(`${window.APP_CONFIG.APP_NAME} v${window.APP_CONFIG.VERSION} initialized`);
  
  // Demo mode notification
  if (window.APP_CONFIG.DEMO_MODE) {
    console.log('Demo mode enabled - Using mock data');
  }
});

// Export utilities
window.Utils = Utils;
window.Modal = Modal;