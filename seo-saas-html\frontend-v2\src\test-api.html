<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - SEO Pro</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #111827;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 500;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .result.success {
            border-color: #10b981;
            background: #f0fdf4;
        }
        .result.error {
            border-color: #ef4444;
            background: #fef2f2;
        }
        .config-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
            color: #856404;
        }
        .config-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .status-item {
            padding: 10px;
            border-radius: 6px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status-item.valid {
            background: #f0fdf4;
            color: #166534;
        }
        .status-item.invalid {
            background: #fef2f2;
            color: #991b1b;
        }
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API Test Dashboard</h1>
        
        <div id="config-warning"></div>
        
        <div class="test-section">
            <h2>System Status</h2>
            <button class="test-button" onclick="testHealthCheck()">Test Health Check</button>
            <button class="test-button" onclick="testConfigStatus()">Check API Configuration</button>
            <button class="test-button" onclick="validateOpenAI()">Validate OpenAI</button>
            <div id="system-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h2>Authentication</h2>
            <button class="test-button" onclick="testLogin()">Test Login (<EMAIL>)</button>
            <button class="test-button" onclick="testVerifyToken()">Verify Token</button>
            <button class="test-button" onclick="testLogout()">Logout</button>
            <div id="auth-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h2>Content Generation</h2>
            <button class="test-button" onclick="testContentGeneration()">Generate Test Content</button>
            <button class="test-button" onclick="testDatabaseConnection()">Test Database</button>
            <div id="content-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h2>Dashboard & Analytics</h2>
            <button class="test-button" onclick="testDashboardOverview()">Get Dashboard Overview</button>
            <button class="test-button" onclick="testAnalytics()">Get Analytics</button>
            <div id="dashboard-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        let authToken = localStorage.getItem('auth_token');

        // Update auth header
        function getHeaders() {
            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            };
            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }
            return headers;
        }

        // Show result
        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
        }

        // Health check
        async function testHealthCheck() {
            try {
                console.log('Testing health check...');
                const response = await fetch(`${API_BASE}/health`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    },
                    mode: 'cors'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                showResult('system-result', data);
            } catch (error) {
                console.error('Health check error:', error);
                showResult('system-result', `Error: ${error.message}\n\nMake sure the backend server is running on port 3001.`, true);
            }
        }

        // Config status
        async function testConfigStatus() {
            try {
                const response = await fetch(`${API_BASE}/config/status`);
                const data = await response.json();
                
                if (data.success && data.data) {
                    // Show configuration warning if needed
                    const warningDiv = document.getElementById('config-warning');
                    if (data.data.missing.length > 0) {
                        warningDiv.innerHTML = `
                            <div class="config-warning">
                                <strong>⚠️ Configuration Required</strong>
                                <p>Missing API keys: ${data.data.missing.join(', ')}</p>
                                <div class="config-status">
                                    ${Object.entries(data.data).filter(([key, val]) => typeof val === 'object' && val.configured !== undefined).map(([key, val]) => `
                                        <div class="status-item ${val.valid ? 'valid' : 'invalid'}">
                                            ${val.valid ? '✅' : '❌'} ${key.toUpperCase()}: ${val.valid ? 'Valid' : val.configured ? 'Invalid' : 'Not configured'}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `;
                    } else {
                        warningDiv.innerHTML = `
                            <div class="config-warning" style="background: #f0fdf4; border-color: #86efac; color: #166534;">
                                <strong>✅ All APIs Configured</strong>
                                <p>System is ready for content generation!</p>
                            </div>
                        `;
                    }
                }
                
                showResult('system-result', data);
            } catch (error) {
                showResult('system-result', `Error: ${error.message}`, true);
            }
        }

        // Validate OpenAI
        async function validateOpenAI() {
            try {
                const response = await fetch(`${API_BASE}/config/validate-openai`, {
                    method: 'POST',
                    headers: getHeaders()
                });
                const data = await response.json();
                showResult('system-result', data, !response.ok);
            } catch (error) {
                showResult('system-result', `Error: ${error.message}`, true);
            }
        }

        // Login
        async function testLogin() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: getHeaders(),
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'demo123'
                    })
                });
                const data = await response.json();
                
                if (data.token) {
                    authToken = data.token;
                    localStorage.setItem('auth_token', authToken);
                    showResult('auth-result', { ...data, message: 'Login successful! Token stored.' });
                } else {
                    showResult('auth-result', data, true);
                }
            } catch (error) {
                showResult('auth-result', `Error: ${error.message}`, true);
            }
        }

        // Verify token
        async function testVerifyToken() {
            try {
                const response = await fetch(`${API_BASE}/auth/verify`, {
                    headers: getHeaders()
                });
                const data = await response.json();
                showResult('auth-result', data, !response.ok);
            } catch (error) {
                showResult('auth-result', `Error: ${error.message}`, true);
            }
        }

        // Logout
        function testLogout() {
            authToken = null;
            localStorage.removeItem('auth_token');
            showResult('auth-result', { message: 'Logged out successfully' });
        }

        // Content generation
        async function testContentGeneration() {
            try {
                const response = await fetch(`${API_BASE}/content/test-generate`, {
                    method: 'POST',
                    headers: getHeaders(),
                    body: JSON.stringify({
                        keyword: 'SEO optimization tips',
                        contentType: 'blog-post',
                        tone: 'professional',
                        wordCount: '800-1200'
                    })
                });
                const data = await response.json();
                showResult('content-result', data, !response.ok);
            } catch (error) {
                showResult('content-result', `Error: ${error.message}`, true);
            }
        }

        // Database connection
        async function testDatabaseConnection() {
            try {
                const response = await fetch(`${API_BASE}/content/database/test-connection`);
                const data = await response.json();
                showResult('content-result', data, !response.ok);
            } catch (error) {
                showResult('content-result', `Error: ${error.message}`, true);
            }
        }

        // Dashboard overview
        async function testDashboardOverview() {
            try {
                const response = await fetch(`${API_BASE}/dashboard/overview`, {
                    headers: getHeaders()
                });
                const data = await response.json();
                showResult('dashboard-result', data, !response.ok);
            } catch (error) {
                showResult('dashboard-result', `Error: ${error.message}`, true);
            }
        }

        // Analytics
        async function testAnalytics() {
            try {
                const response = await fetch(`${API_BASE}/dashboard/analytics`, {
                    headers: getHeaders()
                });
                const data = await response.json();
                showResult('dashboard-result', data, !response.ok);
            } catch (error) {
                showResult('dashboard-result', `Error: ${error.message}`, true);
            }
        }

        // Load config status on page load
        window.onload = () => {
            testConfigStatus();
        };
    </script>
</body>
</html>